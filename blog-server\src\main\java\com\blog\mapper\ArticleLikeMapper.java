package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.ArticleLike;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 文章点赞Mapper接口
 */
public interface ArticleLikeMapper extends BaseMapper<ArticleLike> {
    
    /**
     * 查询文章点赞数量
     * @param articleId 文章ID
     * @return 点赞数量
     */
    @Select("SELECT COUNT(*) FROM article_like WHERE article_id = #{articleId}")
    int selectLikeCount(@Param("articleId") Long articleId);
    
    /**
     * 查询用户是否已点赞文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 点赞记录数
     */
    @Select("SELECT COUNT(*) FROM article_like WHERE article_id = #{articleId} AND user_id = #{userId}")
    int selectUserLikeStatus(@Param("articleId") Long articleId, @Param("userId") Long userId);
} 