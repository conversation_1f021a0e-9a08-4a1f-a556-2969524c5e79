package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.EmailTemplateDTO;
import com.blog.service.EmailTemplateService;
import com.blog.vo.EmailTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 邮件模板控制器
 */
@RestController
@RequestMapping("/email-template")
@Api(tags = "邮件模板管理")
public class EmailTemplateController {

    @Autowired
    private EmailTemplateService emailTemplateService;

    /**
     * 获取邮件模板列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("获取邮件模板列表")
    public Result<List<EmailTemplateVO>> getTemplateList() {
        List<EmailTemplateVO> templates = emailTemplateService.getTemplateList();
        return Result.success(templates);
    }

    /**
     * 获取邮件模板详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("获取邮件模板详情")
    public Result<EmailTemplateVO> getTemplateDetail(@PathVariable Long id) {
        EmailTemplateVO template = emailTemplateService.getTemplateDetail(id);
        return Result.success(template);
    }

    /**
     * 添加邮件模板
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("添加邮件模板")
    public Result<Long> addTemplate(@Valid @RequestBody EmailTemplateDTO templateDTO) {
        Long templateId = emailTemplateService.addTemplate(templateDTO);
        return Result.success(templateId, "邮件模板添加成功");
    }

    /**
     * 更新邮件模板
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("更新邮件模板")
    public Result<Boolean> updateTemplate(@Valid @RequestBody EmailTemplateDTO templateDTO) {
        boolean success = emailTemplateService.updateTemplate(templateDTO);
        return Result.success(success, success ? "邮件模板更新成功" : "邮件模板更新失败");
    }

    /**
     * 删除邮件模板
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("删除邮件模板")
    public Result<Boolean> deleteTemplate(@PathVariable Long id) {
        boolean success = emailTemplateService.deleteTemplate(id);
        return Result.success(success, success ? "邮件模板删除成功" : "邮件模板删除失败");
    }

    /**
     * 启用/禁用邮件模板
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("启用/禁用邮件模板")
    public Result<Boolean> updateTemplateStatus(@PathVariable Long id, @RequestParam Integer status) {
        boolean success = emailTemplateService.updateTemplateStatus(id, status);
        String message = status == 1 ? "模板启用成功" : "模板禁用成功";
        return Result.success(success, success ? message : "操作失败");
    }

    /**
     * 预览邮件模板
     */
    @PostMapping("/preview")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("预览邮件模板")
    public Result<Map<String, String>> previewTemplate(@RequestParam String templateCode, 
                                                       @RequestBody(required = false) Map<String, String> variables) {
        Map<String, String> preview = emailTemplateService.previewTemplate(templateCode, variables);
        return Result.success(preview);
    }
}
