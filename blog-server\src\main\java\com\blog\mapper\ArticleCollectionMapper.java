package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.ArticleCollection;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文章收藏Mapper接口
 */
public interface ArticleCollectionMapper extends BaseMapper<ArticleCollection> {
    
    /**
     * 查询文章收藏数量
     * @param articleId 文章ID
     * @return 收藏数量
     */
    @Select("SELECT COUNT(*) FROM article_collection WHERE article_id = #{articleId}")
    int selectCollectionCount(@Param("articleId") Long articleId);
    
    /**
     * 查询用户是否已收藏文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 收藏记录数
     */
    @Select("SELECT COUNT(*) FROM article_collection WHERE article_id = #{articleId} AND user_id = #{userId}")
    int selectUserCollectionStatus(@Param("articleId") Long articleId, @Param("userId") Long userId);
    
    /**
     * 查询用户收藏的文章ID列表
     * @param userId 用户ID
     * @return 文章ID列表
     */
    @Select("SELECT article_id FROM article_collection WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Long> selectUserCollectionIds(@Param("userId") Long userId);
} 