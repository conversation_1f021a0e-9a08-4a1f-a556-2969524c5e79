# 个人动态博客系统 - 第三阶段测试指南

本文档提供了测试分类和标签模块的指南，现在完全基于自动化测试流程。

## 🛠️ 环境准备

请确保以下环境已准备就绪：

1. **数据库**：MySQL数据库已启动并已执行最新的`blog_system.sql`脚本，确保包含分类和标签相关表。
2. **后端服务**：Spring Boot应用可正常编译。
3. **前端应用**：Vue应用可正常启动。

## 🧪 后端API自动化测试

分类和标签模块的后端API功能由 `src/test/java/com/blog/` 下的自动化测试用例保证 (例如 `CategoryControllerTest.java` 和 `TagControllerTest.java`，待创建)。

### 计划覆盖的API功能点
- **分类CRUD**: 验证创建、获取、更新、删除分类的接口功能。
- **标签CRUD**: 验证创建、获取、更新、删除标签的接口功能。
- **文章关联**: 
    - 创建文章时能否成功关联分类和标签。
    - 更新文章时能否修改分类和标签。
    - 获取文章列表时，能否按分类或标签进行筛选。

### 如何运行
进入后端项目根目录，执行以下命令即可运行所有相关测试：

```bash
mvn -f blog-server/pom.xml test
```

## 🖥️ 前端端到端(E2E)测试

前端的关键业务流程已通过Cypress自动化测试来保障。这能模拟真实用户在浏览器中的操作，确保前后端系统协同工作顺畅。

### 如何运行

1. 确保您的前端开发服务器正在运行 (`npm run dev`)。
2. 确保后端服务器正在运行。
3. 打开Cypress测试运行器：
   ```bash
   cd blog-web
   npx cypress open
   ```
4. 在测试列表（Specs）中，点击您想要运行的测试文件。

*如果您是首次运行，请参照第一阶段指南中的 "3.1 首次运行与配置" 完成初始化。*

### 分类与标签管理测试

我们已经创建了以下E2E测试文件，覆盖了分类和标签管理的核心功能：

#### 1. 分类管理测试 (`category-management.cy.js`)

此测试文件覆盖以下场景：
- **查看分类列表**：验证管理员能否查看分类列表。
- **新增分类**：验证管理员能否成功添加新分类。
- **编辑分类**：验证管理员能否成功编辑已有分类。
- **删除分类**：验证管理员能否成功删除未被引用的分类。
- **删除被引用分类**：验证当尝试删除被文章引用的分类时，系统会显示友好的错误提示。

#### 2. 标签管理测试 (`tag-management.cy.js`)

此测试文件覆盖以下场景：
- **查看标签列表**：验证管理员能否查看标签列表。
- **新增标签**：验证管理员能否成功添加新标签。
- **编辑标签**：验证管理员能否成功编辑已有标签。
- **删除标签**：验证管理员能否成功删除未被引用的标签。
- **删除被引用标签**：验证当尝试删除被文章引用的标签时，系统会显示友好的错误提示。

#### 3. 文章关联分类和标签测试 (`article-category-tag.cy.js`)

此测试文件覆盖以下场景：
- **创建文章时选择分类和标签**：验证普通用户在创建新文章时能否成功选择分类和标签（普通用户可以发布文章）。
- **编辑文章时修改分类和标签**：验证用户在编辑自己文章时能否成功修改分类和标签。

**权限说明**：
- 分类和标签的创建、编辑、删除：仅管理员可操作
- 文章发布和编辑：任何登录用户都可以操作自己的文章

### 测试最佳实践

1. **数据隔离**：每个测试用例都会创建唯一的测试数据，避免测试之间的相互干扰。
2. **自动登录**：使用自定义命令 `cy.loginAsAdmin()` 和 `cy.getAndStoreAdminToken()` 简化测试中的登录操作。
3. **数据属性选择器**：使用 `data-cy` 属性作为元素选择器，提高测试的稳定性和可维护性。
4. **API创建测试数据**：在适当的场景下，通过API直接创建测试数据，提高测试效率。

## 🔍 常见问题排查

1. **测试失败 - 元素未找到**：
   - 检查页面上是否存在具有指定 `data-cy` 属性的元素。
   - 检查元素是否被正确渲染（可能受到异步加载的影响）。

2. **测试失败 - 无法创建或删除数据**：
   - 检查管理员登录是否成功。
   - 检查API请求中的令牌是否正确。
   - 检查后端服务是否正常运行。

3. **测试失败 - 删除被引用的分类或标签时未显示错误提示**：
   - 确保测试数据正确创建，包括分类/标签与文章的关联关系。
   - 检查前端是否正确处理409状态码的响应。 

## 🐞 单元测试故障排查

### 权限相关测试失败

如果您遇到控制器测试中的权限问题（403 Forbidden），特别是在 `CategoryControllerTest` 和 `TagControllerTest` 中，可以检查以下几点：

#### 1. 角色名称大小写问题

**症状**：使用 `@WithMockUser` 的测试方法返回 403 Forbidden 状态码。

**解决方案**：
- 确保角色名使用大写形式，例如：
  ```java
  @WithMockUser(roles = "ADMIN")  // 正确，使用大写
  @WithMockUser(roles = "admin")  // 错误，使用小写
  ```
- Spring Security 内部使用大写角色名，并自动添加 "ROLE_" 前缀。

#### 2. 测试断言与实际响应不匹配

**症状**：测试断言失败，例如预期API返回数字ID，但实际返回了完整对象。

**解决方案**：
- 检查并调整断言，使其与API实际返回的数据结构匹配，例如：
  ```java
  // 修改前（期望直接返回ID值）
  .andExpect(jsonPath("$.data").isNumber());
  
  // 修改后（期望返回包含id属性的对象）
  .andExpect(jsonPath("$.data").isMap())
  .andExpect(jsonPath("$.data.id").isNumber());
  ```
- 请始终参考API文档或控制器代码，确认响应格式。

#### 3. 测试数据隔离问题

**症状**：有时测试通过，有时失败，表现不一致。

**解决方案**：
- 确保使用 `@Transactional` 注解，以便每个测试结束后回滚事务。
- 为每个测试创建唯一的测试数据，不要依赖其他测试创建的数据。
- 在测试方法中清理掉可能影响其他测试的数据。

### 数据库连接问题

如果遇到数据库连接问题，可以参考以下建议：

- 确保测试使用的是正确的数据库配置（application-test.properties）。
- 检查数据库连接参数是否正确（URL、用户名、密码）。
- 如果使用H2内存数据库，确保schema已正确初始化。

### JUnit测试日志调整

如需查看更详细的测试日志以便排查问题：

1. 在 `src/test/resources/` 目录下创建或编辑 `logback-test.xml` 文件：
   ```xml
   <configuration>
     <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
       <encoder>
         <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
       </encoder>
     </appender>
     
     <!-- 启用SQL日志 -->
     <logger name="org.hibernate.SQL" level="DEBUG"/>
     <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
     
     <!-- Spring Security日志 -->
     <logger name="org.springframework.security" level="DEBUG"/>
     
     <root level="INFO">
       <appender-ref ref="CONSOLE"/>
     </root>
   </configuration>
   ```

2. 重新运行测试，查看详细日志以便定位问题。 