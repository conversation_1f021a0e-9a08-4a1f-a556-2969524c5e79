package com.blog.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 文章数据传输对象
 */
@Data
public class ArticleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    private Long id;

    /**
     * 文章标题
     */
    @NotBlank(message = "文章标题不能为空")
    private String title;

    /**
     * 文章摘要
     */
    private String summary;

    /**
     * 文章内容
     */
    @NotBlank(message = "文章内容不能为空")
    private String content;

    /**
     * 封面图片URL
     */
    private String coverImage;

    /**
     * 文章分类ID
     */
    private Long categoryId;

    /**
     * 标签ID列表，用于新增或修改文章时关联标签
     */
    private List<Long> tagIds;

    /**
     * 文章状态（0-草稿，1-已发布）
     */
    @NotNull(message = "文章状态不能为空")
    private Integer status;

    /**
     * 是否置顶（0-否，1-是）
     */
    private Integer isTop;

    /**
     * 是否允许评论（0-否，1-是）
     */
    private Integer allowComment;
} 