package com.blog.security;

import com.blog.common.utils.JwtTokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT登录授权过滤器
 */
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtAuthenticationTokenFilter.class);
    
    @Resource
    private UserDetailsService userDetailsService;
    
    @Resource
    private JwtTokenUtil jwtTokenUtil;
    
    @Value("${jwt.tokenHeader}")
    private String tokenHeader;
    
    @Value("${jwt.tokenHead}")
    private String tokenHead;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain chain) throws ServletException, IOException {
        // 从请求头中获取Authorization头
        String authHeader = request.getHeader(this.tokenHeader);
        
        // 判断是否存在token
        if (authHeader != null) {
            String authToken = null;
            
            // 处理各种可能的token格式
            if (authHeader.startsWith(this.tokenHead + " ")) {
                // 标准格式：Bearer token（有空格）
                authToken = authHeader.substring((this.tokenHead + " ").length());
            } else if (authHeader.startsWith(this.tokenHead)) {
                // 处理没有空格的情况：Bearertoken
                authToken = authHeader.substring(this.tokenHead.length());
            } else {
                // 其他格式：可能直接是token
                authToken = authHeader;
            }
            
            // 去除可能存在的前后空格
            authToken = authToken != null ? authToken.trim() : null;
            
            // 从token中获取用户名
            String username = jwtTokenUtil.getUserNameFromToken(authToken);
            LOGGER.info("checking username:{}", username);
            
            // 如果能够正确解析用户名并且当前SecurityContext中没有认证信息
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 从数据库中加载用户信息
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);
                
                // 验证token是否有效
                if (jwtTokenUtil.validateToken(authToken, userDetails)) {
                    // 将用户信息存入SecurityContext
                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    LOGGER.info("authenticated user:{}", username);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        }
        
        // 继续执行过滤器链
        chain.doFilter(request, response);
    }
} 