<template>
  <div class="email-template-container">
    <div class="page-header">
      <h2>📧 邮件模板管理</h2>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        添加模板
      </el-button>
    </div>

    <!-- 模板列表 -->
    <el-card class="template-list">
      <el-table :data="templateList" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="templateName" label="模板名称" width="200" />
        <el-table-column prop="templateCode" label="模板代码" width="150" />
        <el-table-column prop="subject" label="邮件主题" min-width="200" />
        <el-table-column prop="templateType" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.templateType === 'HTML' ? 'success' : 'info'">
              {{ scope.row.templateType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="updateStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="previewTemplate(scope.row)">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button size="small" type="primary" @click="editTemplate(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteTemplate(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑模板对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="80%"
      :before-close="handleClose"
    >
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板代码" prop="templateCode">
              <el-input v-model="templateForm.templateCode" placeholder="请输入模板代码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="邮件主题" prop="subject">
          <el-input v-model="templateForm.subject" placeholder="请输入邮件主题，支持变量如{{USER_NAME}}" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板类型" prop="templateType">
              <el-select v-model="templateForm.templateType" style="width: 100%">
                <el-option label="HTML" value="HTML" />
                <el-option label="TEXT" value="TEXT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-switch
                v-model="templateForm.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述">
          <el-input
            v-model="templateForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="邮件内容" prop="content">
          <div class="content-editor">
            <el-tabs v-model="activeTab" class="editor-tabs">
              <el-tab-pane label="编辑" name="edit">
                <el-input
                  v-model="templateForm.content"
                  type="textarea"
                  :rows="12"
                  placeholder="请输入邮件内容，支持HTML格式和变量如{{USER_NAME}}"
                  class="content-textarea"
                />
              </el-tab-pane>
              <el-tab-pane label="预览" name="preview">
                <div class="content-preview" v-html="previewContent"></div>
              </el-tab-pane>
            </el-tabs>
            <div class="variable-helper">
              <el-text size="small" type="info">
                支持的变量：{{USER_NAME}}, {{ARTICLE_TITLE}}, {{COMMENT_CONTENT}}, {{ARTICLE_URL}}, {{SMTP_SERVER}}, {{SMTP_PORT}}, {{TEST_TIME}}
              </el-text>
            </div>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewCurrentTemplate">预览效果</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog title="📧 邮件预览" v-model="previewVisible" width="60%">
      <div class="preview-container">
        <div class="preview-header">
          <h3>{{ previewData.subject }}</h3>
          <el-tag>{{ previewData.templateType }}</el-tag>
        </div>
        <div class="preview-content" v-html="previewData.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, View } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const loading = ref(false)
const templateList = ref([])
const dialogVisible = ref(false)
const previewVisible = ref(false)
const dialogTitle = ref('添加邮件模板')
const templateFormRef = ref()
const activeTab = ref('edit')

// 表单数据
const templateForm = reactive({
  id: null,
  templateName: '',
  templateCode: '',
  subject: '',
  content: '',
  templateType: 'HTML',
  description: '',
  status: 1
})

// 预览数据
const previewData = reactive({
  subject: '',
  content: '',
  templateType: 'HTML'
})

// 表单验证规则
const templateRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入模板代码', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ]
}

// 预览内容计算属性
const previewContent = computed(() => {
  const variables = {
    USER_NAME: '张三',
    ARTICLE_TITLE: '示例文章标题',
    COMMENT_CONTENT: '这是一条示例评论内容',
    ARTICLE_URL: 'http://localhost:3000/article/1',
    SMTP_SERVER: 'smtp.163.com',
    SMTP_PORT: '25',
    TEST_TIME: new Date().toLocaleString()
  }

  let content = templateForm.content || ''

  // 简单的变量替换
  Object.keys(variables).forEach(key => {
    const placeholder = `{{${key}}}`
    content = content.replace(new RegExp(placeholder, 'g'), variables[key])
  })

  return content
})

// 加载模板列表
const loadTemplateList = async () => {
  loading.value = true
  try {
    const result = await request.get('/email-template/list')
    templateList.value = result.data || []
  } catch (error) {
    console.error('加载模板列表失败', error)
    ElMessage.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

// 显示添加对话框
const showAddDialog = () => {
  dialogTitle.value = '添加邮件模板'
  resetForm()
  dialogVisible.value = true
}

// 编辑模板
const editTemplate = (template) => {
  dialogTitle.value = '编辑邮件模板'
  Object.assign(templateForm, template)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(templateForm, {
    id: null,
    templateName: '',
    templateCode: '',
    subject: '',
    content: '',
    templateType: 'HTML',
    description: '',
    status: 1
  })
  if (templateFormRef.value) {
    templateFormRef.value.resetFields()
  }
}

// 保存模板
const saveTemplate = async () => {
  if (!templateFormRef.value) return

  try {
    await templateFormRef.value.validate()

    if (templateForm.id) {
      // 更新模板
      await request.put('/email-template', templateForm)
      ElMessage.success('模板更新成功')
    } else {
      // 添加模板
      await request.post('/email-template', templateForm)
      ElMessage.success('模板添加成功')
    }

    dialogVisible.value = false
    loadTemplateList()
  } catch (error) {
    console.error('保存模板失败', error)
    ElMessage.error('保存失败')
  }
}

// 删除模板
const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.templateName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await request.delete(`/email-template/${template.id}`)
    ElMessage.success('删除成功')
    loadTemplateList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败', error)
      ElMessage.error('删除失败')
    }
  }
}

// 更新状态
const updateStatus = async (template) => {
  try {
    await request.put(`/email-template/${template.id}/status?status=${template.status}`)
    ElMessage.success(template.status === 1 ? '模板已启用' : '模板已禁用')
  } catch (error) {
    console.error('更新状态失败', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    template.status = template.status === 1 ? 0 : 1
  }
}

// 预览模板
const previewTemplate = async (template) => {
  try {
    const variables = {
      USER_NAME: '张三',
      ARTICLE_TITLE: '示例文章标题',
      COMMENT_CONTENT: '这是一条示例评论内容',
      ARTICLE_URL: 'http://localhost:3000/article/1',
      SMTP_SERVER: 'smtp.163.com',
      SMTP_PORT: '25',
      TEST_TIME: new Date().toLocaleString()
    }

    const result = await request.post(`/email-template/preview?templateCode=${template.templateCode}`, variables)
    Object.assign(previewData, result.data)
    previewVisible.value = true
  } catch (error) {
    console.error('预览模板失败', error)
    ElMessage.error('预览失败')
  }
}

// 预览当前编辑的模板
const previewCurrentTemplate = () => {
  const variables = {
    USER_NAME: '张三',
    ARTICLE_TITLE: '示例文章标题',
    COMMENT_CONTENT: '这是一条示例评论内容',
    ARTICLE_URL: 'http://localhost:3000/article/1',
    SMTP_SERVER: 'smtp.163.com',
    SMTP_PORT: '25',
    TEST_TIME: new Date().toLocaleString()
  }
  
  let content = templateForm.content
  let subject = templateForm.subject
  
  // 简单的变量替换
  Object.keys(variables).forEach(key => {
    const placeholder = `{{${key}}}`
    content = content.replace(new RegExp(placeholder, 'g'), variables[key])
    subject = subject.replace(new RegExp(placeholder, 'g'), variables[key])
  })
  
  Object.assign(previewData, {
    subject,
    content,
    templateType: templateForm.templateType
  })
  
  previewVisible.value = true
}

// 关闭对话框
const handleClose = (done) => {
  ElMessageBox.confirm('确定要关闭吗？未保存的更改将丢失。')
    .then(() => {
      done()
    })
    .catch(() => {})
}

// 页面加载时获取数据
onMounted(() => {
  loadTemplateList()
})
</script>

<style scoped>
.email-template-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.template-list {
  margin-bottom: 20px;
}

.preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  background-color: #f5f7fa;
  padding: 15px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-header h3 {
  margin: 0;
  color: #303133;
}

.preview-content {
  padding: 20px;
  min-height: 200px;
  background-color: white;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.content-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-tabs {
  margin: 0;
}

.editor-tabs .el-tabs__header {
  margin: 0;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.content-textarea {
  border: none;
}

.content-textarea .el-textarea__inner {
  border: none;
  border-radius: 0;
  resize: vertical;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.content-preview {
  min-height: 300px;
  padding: 15px;
  background-color: white;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow-y: auto;
}

.variable-helper {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 12px;
  color: #6c757d;
}
</style>
