<template>
  <div class="modern-layout">
    <!-- 🎨 现代化导航栏 -->
    <header class="modern-header">
      <div class="header-backdrop"></div>
      <div class="container">
        <div class="header-content">
          <!-- Logo 区域 -->
          <div class="logo-section">
            <router-link to="/" class="logo-link" data-cy="system-title">
              <div class="logo-container">
                <img v-if="siteLogo" :src="siteLogo" :alt="siteTitle" class="logo-img">
                <div v-else class="logo-text">
                  <span class="logo-title">{{ siteTitle }}</span>
                  <div class="logo-accent"></div>
                </div>
              </div>
            </router-link>
          </div>

          <!-- 导航菜单 -->
          <nav class="nav-menu">
            <div class="nav-links">
              <router-link to="/home" class="nav-link">
                <span class="nav-text">首页</span>
                <div class="nav-indicator"></div>
              </router-link>
              <router-link to="/archive" class="nav-link">
                <span class="nav-text">归档</span>
                <div class="nav-indicator"></div>
              </router-link>
              <router-link to="/search" class="nav-link">
                <span class="nav-text">搜索</span>
                <div class="nav-indicator"></div>
              </router-link>
            </div>
          </nav>

          <!-- 用户操作区域 -->
          <div class="user-section">
            <template v-if="!isLoggedIn">
              <router-link to="/login" class="btn btn-ghost btn-sm">登录</router-link>
              <router-link to="/register" class="btn btn-primary btn-sm">注册</router-link>
            </template>
            <template v-else>
              <!-- 管理员入口 -->
              <router-link
                v-if="isAdmin"
                to="/admin/dashboard"
                class="admin-badge"
                title="管理后台"
              >
                <el-icon><Setting /></el-icon>
                <span>管理</span>
              </router-link>

              <!-- 通知中心 -->
              <div class="notification-center" @click="goToNotifications">
                <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
                  <div class="notification-btn">
                    <el-icon :size="18"><Bell /></el-icon>
                  </div>
                </el-badge>
              </div>

              <!-- 用户菜单 -->
              <el-dropdown trigger="click" class="user-dropdown">
                <div class="user-avatar" data-cy="user-dropdown">
                  <div class="avatar-circle">
                    <span class="avatar-text">{{ username.charAt(0).toUpperCase() }}</span>
                  </div>
                  <span class="username">{{ username }}</span>
                  <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="modern-dropdown">
                    <el-dropdown-item class="dropdown-header">
                      <div class="user-info">
                        <div class="avatar-large">
                          <span>{{ username.charAt(0).toUpperCase() }}</span>
                        </div>
                        <div class="user-details">
                          <div class="user-name">{{ username }}</div>
                          <div class="user-role">{{ isAdmin ? '管理员' : '用户' }}</div>
                        </div>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <router-link to="/user/profile" class="dropdown-link">
                        <el-icon><User /></el-icon>
                        <span>个人资料</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/notifications" class="dropdown-link">
                        <el-icon><Bell /></el-icon>
                        <span>消息中心</span>
                        <el-badge v-if="unreadCount > 0" :value="unreadCount" :max="99" class="notification-badge" />
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/collection" class="dropdown-link">
                        <el-icon><Star /></el-icon>
                        <span>我的收藏</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/comment" class="dropdown-link">
                        <el-icon><ChatDotRound /></el-icon>
                        <span>我的评论</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="logout" data-cy="logout-button" class="logout-item">
                      <div class="dropdown-link logout-link">
                        <el-icon><SwitchButton /></el-icon>
                        <span>退出登录</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </div>
      </div>
    </header>

    <!-- 🎯 主内容区域 -->
    <main class="modern-main">
      <div class="main-container">
        <div class="content-wrapper animate-fade-in">
          <router-view></router-view>
        </div>
      </div>
    </main>

    <!-- 🎨 现代化页脚 -->
    <footer class="modern-footer">
      <div class="footer-backdrop"></div>
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <div class="footer-logo">
              <span class="footer-title">{{ siteTitle }}</span>
              <div class="footer-accent"></div>
            </div>
            <p class="footer-text">
              &copy; 2025 {{ siteTitle }} 版权所有
              <span v-if="icpNumber" class="icp-info">| {{ icpNumber }}</span>
            </p>
          </div>
          <div class="footer-links">
            <a href="#" class="footer-link">关于我们</a>
            <a href="#" class="footer-link">联系方式</a>
            <a href="#" class="footer-link">隐私政策</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getToken, removeToken, getUserInfo } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { getUnreadCount } from '@/api/notification'
import {
  Bell,
  Setting,
  ArrowDown,
  User,
  Star,
  ChatDotRound,
  SwitchButton
} from '@element-plus/icons-vue'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'

export default {
  name: 'Layout',
  components: {
    Bell,
    Setting,
    ArrowDown,
    User,
    Star,
    ChatDotRound,
    SwitchButton
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const username = ref('用户')
    const unreadCount = ref(0)
    let notificationTimer = null

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title)
    const siteLogo = computed(() => siteConfig.value.logo)
    const icpNumber = computed(() => siteConfig.value.icp)

    const isLoggedIn = computed(() => {
      return !!getToken()
    })

    const isAdmin = computed(() => {
      return userStore.isAdmin
    })

    // 初始化获取用户名
    if (isLoggedIn.value) {
      const userInfo = getUserInfo()
      if (userInfo) {
        username.value = userInfo.nickname || userInfo.username || '用户'
      }
    }

    // 获取未读通知数量
    const fetchUnreadCount = async () => {
      if (!isLoggedIn.value) return

      try {
        const res = await getUnreadCount()
        if (res.code === 200) {
          unreadCount.value = res.data.unreadCount
        }
      } catch (error) {
        console.error('获取未读通知数量失败', error)
      }
    }

    // 跳转到消息中心
    const goToNotifications = () => {
      router.push('/user/notifications')
    }

    // 定期更新未读通知数量
    const startNotificationPolling = () => {
      if (!isLoggedIn.value) return

      fetchUnreadCount() // 立即获取一次
      notificationTimer = setInterval(fetchUnreadCount, 30000) // 每30秒更新一次
    }

    const stopNotificationPolling = () => {
      if (notificationTimer) {
        clearInterval(notificationTimer)
        notificationTimer = null
      }
    }

    const logout = () => {
      stopNotificationPolling()
      userStore.logout()
    }

    onMounted(() => {
      fetchSiteConfig() // 获取完整的网站配置信息
      if (isLoggedIn.value) {
        startNotificationPolling()
      }
    })

    onUnmounted(() => {
      stopNotificationPolling()
    })

    return {
      isLoggedIn,
      isAdmin,
      username,
      unreadCount,
      siteTitle,
      siteLogo,
      icpNumber,
      goToNotifications,
      logout
    }
  }
}
</script>

<style scoped>
// 🎨 现代化布局样式
.modern-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--bg-secondary);
}

// ✨ 现代化导航栏
.modern-header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-normal);
}

.header-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  z-index: -1;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  padding: 0 var(--spacing-6);

  @media (max-width: 768px) {
    height: 64px;
    padding: 0 var(--spacing-4);
  }
}

// 🎯 Logo 区域
.logo-section {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all var(--transition-fast);

  &:hover {
    transform: translateY(-1px);
  }
}

.logo-container {
  position: relative;
}

.logo-img {
  height: 48px;
  max-width: 200px;
  object-fit: contain;
  border-radius: var(--radius-lg);
}

.logo-text {
  position: relative;
  padding: var(--spacing-2) var(--spacing-4);
}

.logo-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-accent {
  position: absolute;
  bottom: 0;
  left: var(--spacing-4);
  right: var(--spacing-4);
  height: 3px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.logo-link:hover .logo-accent {
  transform: scaleX(1);
}

// 🧭 导航菜单
.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;

  @media (max-width: 768px) {
    display: none;
  }
}

.nav-links {
  display: flex;
  gap: var(--spacing-8);
}

.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);

  &:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-1px);
  }

  &.router-link-active {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);

    .nav-indicator {
      transform: scaleX(1);
    }
  }
}

.nav-text {
  position: relative;
  z-index: 1;
}

.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: var(--spacing-4);
  right: var(--spacing-4);
  height: 2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

// 👤 用户操作区域
.user-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.admin-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

.notification-center {
  position: relative;
  cursor: pointer;
}

.notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  transition: all var(--transition-fast);

  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}
// 👤 用户下拉菜单
.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);

  &:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    transform: translateY(-1px);
  }
}

.avatar-circle {
  width: 32px;
  height: 32px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: white;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.username {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);

  @media (max-width: 640px) {
    display: none;
  }
}

.dropdown-arrow {
  color: var(--text-tertiary);
  transition: transform var(--transition-fast);
}

.user-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

// 📋 现代化下拉菜单
.modern-dropdown {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-2);
  min-width: 240px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dropdown-header {
  padding: var(--spacing-4) !important;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--spacing-2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.avatar-large {
  width: 48px;
  height: 48px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  width: 100%;
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);

  &:hover {
    color: var(--primary-color);
  }
}

.logout-item {
  margin-top: var(--spacing-2);
  border-top: 1px solid var(--border-light);
  padding-top: var(--spacing-2) !important;
}

.logout-link {
  color: var(--error-color) !important;

  &:hover {
    color: var(--error-light) !important;
  }
}

.notification-badge {
  margin-left: auto;
}

// 🎯 主内容区域
.modern-main {
  flex: 1;
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 144px);
}

.main-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);

  @media (max-width: 768px) {
    padding: 0 var(--spacing-4);
  }
}

.content-wrapper {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  min-height: 600px;
}

// 🎨 现代化页脚
.modern-footer {
  position: relative;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-8) 0 var(--spacing-6);
  margin-top: auto;
}

.footer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.95) 100%);
  z-index: -1;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.footer-logo {
  position: relative;
  display: inline-block;
}

.footer-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-accent {
  height: 2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  margin-top: var(--spacing-1);
}

.footer-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.icp-info {
  color: var(--text-tertiary);
}

.footer-links {
  display: flex;
  gap: var(--spacing-6);

  @media (max-width: 768px) {
    gap: var(--spacing-4);
  }
}

.footer-link {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);

  &:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
  }
}

// 📱 响应式优化
@media (max-width: 768px) {
  .header-content {
    gap: var(--spacing-4);
  }

  .user-section {
    gap: var(--spacing-2);
  }

  .modern-main {
    padding: var(--spacing-6) 0;
  }

  .content-wrapper {
    border-radius: var(--radius-xl);
    margin: 0 var(--spacing-2);
  }
}

// 🎬 动画增强
.modern-header {
  animation: slideDown 0.3s ease-out;
}

.content-wrapper {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>