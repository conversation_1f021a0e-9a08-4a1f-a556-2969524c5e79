<template>
  <div class="layout">
    <header class="header">
      <div class="container">
        <div class="logo">
          <router-link to="/" data-cy="system-title">
            <img v-if="siteLogo" :src="siteLogo" :alt="siteTitle" class="logo-img">
            <span v-else>{{ siteTitle }}</span>
          </router-link>
        </div>
        <nav class="nav">
          <router-link to="/home">首页</router-link>
          <router-link to="/archive">归档</router-link>
          <router-link to="/search">搜索</router-link>
          <template v-if="!isLoggedIn">
            <router-link to="/login">登录</router-link>
            <router-link to="/register">注册</router-link>
          </template>
          <template v-else>
            <router-link v-if="isAdmin" to="/admin/dashboard" class="admin-link">管理后台</router-link>

            <!-- 消息通知图标 -->
            <div class="notification-icon" @click="goToNotifications">
              <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
                <el-icon :size="20">
                  <Bell />
                </el-icon>
              </el-badge>
            </div>

            <el-dropdown>
              <span class="user-dropdown" data-cy="user-dropdown">
                {{ username }}<i class="el-icon-arrow-down"></i>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <router-link to="/user/profile">个人资料</router-link>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <router-link to="/user/notifications">
                      消息中心
                      <el-badge v-if="unreadCount > 0" :value="unreadCount" :max="99" class="notification-badge" />
                    </router-link>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <router-link to="/user/collection">我的收藏</router-link>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <router-link to="/user/comment">我的评论</router-link>
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="logout" data-cy="logout-button">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </nav>
      </div>
    </header>
    
    <main class="main">
      <div class="container">
        <router-view></router-view>
      </div>
    </main>
    
    <footer class="footer">
      <div class="container">
        <p>&copy; 2025 {{ siteTitle }} 版权所有 <span v-if="icpNumber">| {{ icpNumber }}</span></p>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getToken, removeToken, getUserInfo } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { getUnreadCount } from '@/api/notification'
import { Bell } from '@element-plus/icons-vue'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'

export default {
  name: 'Layout',
  components: {
    Bell
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const username = ref('用户')
    const unreadCount = ref(0)
    let notificationTimer = null

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title)
    const siteLogo = computed(() => siteConfig.value.logo)
    const icpNumber = computed(() => siteConfig.value.icp)

    const isLoggedIn = computed(() => {
      return !!getToken()
    })

    const isAdmin = computed(() => {
      return userStore.isAdmin
    })

    // 初始化获取用户名
    if (isLoggedIn.value) {
      const userInfo = getUserInfo()
      if (userInfo) {
        username.value = userInfo.nickname || userInfo.username || '用户'
      }
    }

    // 获取未读通知数量
    const fetchUnreadCount = async () => {
      if (!isLoggedIn.value) return

      try {
        const res = await getUnreadCount()
        if (res.code === 200) {
          unreadCount.value = res.data.unreadCount
        }
      } catch (error) {
        console.error('获取未读通知数量失败', error)
      }
    }

    // 跳转到消息中心
    const goToNotifications = () => {
      router.push('/user/notifications')
    }

    // 定期更新未读通知数量
    const startNotificationPolling = () => {
      if (!isLoggedIn.value) return

      fetchUnreadCount() // 立即获取一次
      notificationTimer = setInterval(fetchUnreadCount, 30000) // 每30秒更新一次
    }

    const stopNotificationPolling = () => {
      if (notificationTimer) {
        clearInterval(notificationTimer)
        notificationTimer = null
      }
    }

    const logout = () => {
      stopNotificationPolling()
      userStore.logout()
    }

    onMounted(() => {
      fetchSiteConfig() // 获取完整的网站配置信息
      if (isLoggedIn.value) {
        startNotificationPolling()
      }
    })

    onUnmounted(() => {
      stopNotificationPolling()
    })

    return {
      isLoggedIn,
      isAdmin,
      username,
      unreadCount,
      siteTitle,
      siteLogo,
      icpNumber,
      goToNotifications,
      logout
    }
  }
}
</script>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.container {
  width: 1200px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 15px;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.logo a {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo-img {
  height: 40px;
  max-width: 200px;
  object-fit: contain;
}

.nav {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav a {
  color: #333;
  text-decoration: none;
}

.nav a.router-link-active {
  color: #409eff;
}

.admin-link {
  color: #F56C6C !important;
  font-weight: bold;
}

.notification-icon {
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon:hover {
  background-color: #f5f7fa;
}

.notification-badge {
  margin-left: 8px;
}

.user-dropdown {
  cursor: pointer;
}

.main {
  flex: 1;
  padding: 20px 0;
}

.footer {
  background-color: #f5f5f5;
  padding: 20px 0;
  text-align: center;
}
</style> 