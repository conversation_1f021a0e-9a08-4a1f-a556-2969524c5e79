package com.blog.config;

import com.blog.security.JwtAuthenticationTokenFilter;
import com.blog.security.RestAuthenticationEntryPoint;
import com.blog.security.RestfulAccessDeniedHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsUtils;

import javax.annotation.Resource;

/**
 * Spring Security配置类
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Resource
    private UserDetailsService userDetailsService;
    
    @Resource
    private RestfulAccessDeniedHandler restfulAccessDeniedHandler;
    
    @Resource
    private RestAuthenticationEntryPoint restAuthenticationEntryPoint;

    /**
     * 用于配置需要拦截的url路径、jwt过滤器及出异常后的处理器
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                // 启用CORS，禁用CSRF
                .cors().and().csrf().disable()
                // 不创建会话
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests()
                // 允许跨域预检请求
                .requestMatchers(CorsUtils::isPreFlightRequest).permitAll()
                // 允许对于网站静态资源的无授权访问
                .antMatchers(HttpMethod.GET,
                        "/",
                        "/*.html",
                        "/favicon.ico",
                        "/**/*.html",
                        "/**/*.css",
                        "/**/*.js",
                        "/swagger-resources/**",
                        "/v2/api-docs/**",
                        "/upload/**"  // 添加静态资源路径
                ).permitAll()
                // 对登录注册要允许匿名访问
                .antMatchers("/auth/login", "/auth/register").permitAll()
                // 测试接口允许匿名访问
                .antMatchers("/test/anonymous").permitAll()
                // 除了上面明确放行的, 其他 /test/ 开头的路径都需要认证
                .antMatchers("/test/**").authenticated()
                // 文件上传接口允许匿名访问 - 同时允许GET和POST请求
                .antMatchers(HttpMethod.GET, "/upload/**").permitAll()
                .antMatchers(HttpMethod.POST, "/upload/**").permitAll()
                // 对前台文章、分类、标签等接口允许匿名访问
                .antMatchers(HttpMethod.GET, "/articles/**", "/categories/**", "/tags/**").permitAll()
                // 对文章评论查询接口允许匿名访问
                .antMatchers(HttpMethod.GET, "/comments/article/**").permitAll()
                // 对文章互动状态查询接口允许匿名访问
                .antMatchers(HttpMethod.GET, "/interaction/status/**").permitAll()
                // 对系统配置的部分接口允许匿名访问（获取网站基本信息）
                .antMatchers(HttpMethod.GET, "/config/site", "/config/site_title", "/config/comment").permitAll()
                // 允许匿名访问评论相关接口（支持匿名评论）
                .antMatchers(HttpMethod.GET, "/comments/article/**").permitAll()
                .antMatchers(HttpMethod.POST, "/comments").permitAll()
                // 打印安全配置信息
                .and().headers().frameOptions().disable() // 允许iframe嵌入
                // 所有请求需要身份认证
                .and().authorizeRequests().anyRequest().authenticated();
        
        // 禁用缓存
        httpSecurity.headers().cacheControl();
        
        // 添加JWT filter
        httpSecurity.addFilterBefore(jwtAuthenticationTokenFilter(), UsernamePasswordAuthenticationFilter.class);
        
        // 添加自定义未授权和未登录结果返回
        httpSecurity.exceptionHandling()
                .accessDeniedHandler(restfulAccessDeniedHandler)
                .authenticationEntryPoint(restAuthenticationEntryPoint);
    }

    /**
     * 用于配置UserDetailsService及PasswordEncoder
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService)
                .passwordEncoder(passwordEncoder());
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * JWT过滤器
     */
    @Bean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return new JwtAuthenticationTokenFilter();
    }

    /**
     * 认证管理器
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
} 