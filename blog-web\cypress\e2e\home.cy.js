/**
 * describe 是一个测试套件，用来组织一组相关的测试。
 * 第一个参数是这个测试套件的描述，会显示在测试报告中。
 */
describe('首页冒烟测试', () => {

  /**
   * it 是一个具体的测试用例。
   * 第一个参数是这个测试用例的描述。
   */
  it('应能成功加载首页并显示标题', () => {
    // 1. 访问网站的根路径。
    // Cypress 会自动使用您在 cypress.config.js 中配置的 baseUrl。
    cy.visit('/');

    // 2. 在页面上查找包含"个人动态博客系统"文本的元素。
    // cy.contains() 是一个强大的命令，用于查找包含特定文本内容的元素。
    // .should('be.visible') 是一个断言，用来验证这个元素是可见的。
    // 如果找不到或该元素不可见，测试就会失败。
    cy.get('[data-cy=system-title]').should('be.visible');
  });

}); 