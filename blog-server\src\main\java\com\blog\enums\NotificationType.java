package com.blog.enums;

import lombok.Getter;

/**
 * 通知类型枚举
 */
@Getter
public enum NotificationType {
    LIKE("like", "点赞"),
    COMMENT("comment", "评论"),
    REPLY("reply", "回复"),
    FOLLOW("follow", "关注"),
    COLLECT("collect", "收藏");

    private final String code;
    private final String description;

    NotificationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static NotificationType fromCode(String code) {
        for (NotificationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
