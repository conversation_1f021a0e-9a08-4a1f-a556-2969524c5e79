package com.blog.common.exception;

/**
 * 请求参数错误异常
 */
public class BadRequestException extends BusinessException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造方法
     * @param message 错误消息
     */
    public BadRequestException(String message) {
        super(message);
    }

    /**
     * 构造方法
     * @param message 错误消息
     * @param cause 异常原因
     */
    public BadRequestException(String message, Throwable cause) {
        super(message, cause);
    }
} 