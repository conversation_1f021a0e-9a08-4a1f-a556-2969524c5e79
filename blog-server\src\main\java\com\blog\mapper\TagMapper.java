package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.Tag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.blog.vo.TagVO;

import java.util.List;

/**
 * 标签Mapper
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 根据文章ID查询标签列表
     *
     * @param articleId 文章ID
     * @return 标签列表
     */
    List<Tag> selectByArticleId(@Param("articleId") Long articleId);

    /**
     * 根据标签ID统计文章数量
     *
     * @param tagId 标签ID
     * @return 文章数量
     */
    int countArticleByTagId(@Param("tagId") Long tagId);

    /**
     * 查询热门标签
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<Tag> selectPopularTags(@Param("limit") int limit);

    /**
     * 查询所有标签及其文章数量
     * @return 标签VO列表
     */
    List<TagVO> selectTagWithArticleCount();

} 