package com.blog.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.blog.common.exception.BusinessException;
import com.blog.entity.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务测试类
 */
@SpringBootTest
@Transactional
public class UserServiceTest {

    @Resource
    private UserService userService;
    
    @Resource
    private PasswordEncoder passwordEncoder;

    @Test
    @DisplayName("测试用户注册")
    public void testRegister() {
        // 准备测试数据
        User user = new User();
        user.setUsername("testuser");
        user.setPassword("123456");
        user.setNickname("测试用户");
        user.setEmail("<EMAIL>");
        
        // 执行注册
        boolean result = userService.register(user);
        
        // 验证结果
        assertTrue(result);
        assertNotNull(user.getId());
        
        // 验证数据库中的用户
        User dbUser = userService.getUserByUsername("testuser");
        assertNotNull(dbUser);
        assertEquals("测试用户", dbUser.getNickname());
        assertEquals("<EMAIL>", dbUser.getEmail());
        assertEquals("user", dbUser.getRole());
        assertEquals(1, dbUser.getStatus());
        
        // 验证密码已加密
        assertTrue(passwordEncoder.matches("123456", dbUser.getPassword()));
    }
    
    @Test
    @DisplayName("测试重复用户名注册")
    public void testRegisterDuplicateUsername() {
        // 准备测试数据
        User user1 = new User();
        user1.setUsername("duplicateuser");
        user1.setPassword("123456");
        user1.setNickname("重复用户1");
        
        // 先注册一个用户
        userService.register(user1);
        
        // 再注册一个相同用户名的用户
        User user2 = new User();
        user2.setUsername("duplicateuser");
        user2.setPassword("654321");
        user2.setNickname("重复用户2");
        
        // 验证抛出业务异常
        assertThrows(BusinessException.class, () -> userService.register(user2));
    }
    
    @Test
    @DisplayName("测试登录")
    public void testLogin() {
        // 准备测试数据
        User user = new User();
        user.setUsername("loginuser");
        user.setPassword("123456");
        user.setNickname("登录用户");
        userService.register(user);
        
        // 执行登录
        String token = userService.login("loginuser", "123456");
        
        // 验证结果
        assertNotNull(token);
        assertTrue(token.length() > 10);
    }
    
    @Test
    @DisplayName("测试登录失败 - 用户名不存在")
    public void testLoginWithNonExistentUsername() {
        // 验证抛出业务异常
        assertThrows(BusinessException.class, () -> userService.login("nonexistent", "123456"));
    }
    
    @Test
    @DisplayName("测试登录失败 - 密码错误")
    public void testLoginWithWrongPassword() {
        // 准备测试数据
        User user = new User();
        user.setUsername("passworduser");
        user.setPassword("123456");
        user.setNickname("密码用户");
        userService.register(user);
        
        // 验证抛出业务异常
        assertThrows(BusinessException.class, () -> userService.login("passworduser", "wrongpassword"));
    }
    
    @Test
    @DisplayName("测试根据用户名获取用户")
    public void testGetUserByUsername() {
        // 准备测试数据
        User user = new User();
        user.setUsername("getuser");
        user.setPassword("123456");
        user.setNickname("获取用户");
        userService.register(user);
        
        // 执行获取
        User result = userService.getUserByUsername("getuser");
        
        // 验证结果
        assertNotNull(result);
        assertEquals("获取用户", result.getNickname());
    }
} 