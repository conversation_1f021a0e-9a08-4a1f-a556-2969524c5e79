package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.exception.ConflictException;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.CategoryDTO;
import com.blog.entity.Category;
import com.blog.mapper.CategoryMapper;
import com.blog.service.CategoryService;
import com.blog.vo.CategoryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类服务实现类
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    /**
     * 添加分类
     * @param categoryDTO 分类DTO
     * @return 新增分类的ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryVO addCategory(CategoryDTO categoryDTO) {
        // 检查分类名称是否已存在
        if (checkNameExist(categoryDTO.getName(), null)) {
            throw new BusinessException("分类名称已存在");
        }
        
        // 检查父分类是否存在
        if (categoryDTO.getParentId() != null && !exists(categoryDTO.getParentId())) {
            throw new BusinessException("父分类不存在");
        }
        
        // 转换DTO为实体
        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);
        
        // 保存分类
        boolean saved = save(category);
        if (!saved || category.getId() == null) {
            throw new BusinessException("分类保存失败");
        }

        // 在同一个事务中，创建完整的VO对象
        CategoryVO categoryVO = new CategoryVO();
        BeanUtils.copyProperties(category, categoryVO);
        
        // 设置父分类名称
        if (category.getParentId() != null) {
            Category parentCategory = getById(category.getParentId());
            if (parentCategory != null) {
                categoryVO.setParentName(parentCategory.getName());
            }
        }
        
        // 新创建的分类文章数量为0
        categoryVO.setArticleCount(0);
        
        return categoryVO;
    }

    /**
     * 更新分类
     * @param categoryDTO 分类DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(CategoryDTO categoryDTO) {
        // 检查分类是否存在
        Category existingCategory = getById(categoryDTO.getId());
        if (existingCategory == null) {
            throw new NotFoundException("分类不存在");
        }
        
        // 检查分类名称是否已存在
        if (checkNameExist(categoryDTO.getName(), categoryDTO.getId())) {
            throw new BusinessException("分类名称已存在");
        }
        
        // 检查父分类是否存在
        if (categoryDTO.getParentId() != null && !exists(categoryDTO.getParentId())) {
            throw new BusinessException("父分类不存在");
        }
        
        // 检查是否将自己设为父分类
        if (categoryDTO.getId().equals(categoryDTO.getParentId())) {
            throw new BusinessException("不能将自己设为父分类");
        }
        
        // 检查是否形成循环引用
        if (categoryDTO.getParentId() != null && isChildCategory(categoryDTO.getId(), categoryDTO.getParentId())) {
            throw new BusinessException("不能将子分类设为父分类");
        }
        
        // 转换DTO为实体
        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);
        
        // 更新分类
        return updateById(category);
    }

    /**
     * 删除分类
     * @param id 分类ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        if (!exists(id)) {
            throw new NotFoundException("分类不存在");
        }
        
        // 检查是否有子分类
        int childCount = baseMapper.countChildrenByCategoryId(id);
        if (childCount > 0) {
            throw new ConflictException("该分类下有子分类，无法删除");
        }
        
        // 检查是否有关联的文章
        int articleCount = baseMapper.countArticleByCategoryId(id);
        if (articleCount > 0) {
            throw new ConflictException("该分类下有关联的文章，无法删除");
        }
        
        // 删除分类
        return removeById(id);
    }

    /**
     * 获取分类详情
     * @param id 分类ID
     * @return 分类VO
     */
    @Override
    public CategoryVO getCategoryDetail(Long id) {
        // 查询分类
        Category category = getById(id);
        if (category == null) {
            throw new NotFoundException("分类不存在");
        }
        
        // 转换为VO
        CategoryVO categoryVO = new CategoryVO();
        BeanUtils.copyProperties(category, categoryVO);
        
        // 设置父分类名称
        if (category.getParentId() != null) {
            Category parentCategory = getById(category.getParentId());
            if (parentCategory != null) {
                categoryVO.setParentName(parentCategory.getName());
            }
        }
        
        // 查询文章数量
        int articleCount = baseMapper.countArticleByCategoryId(id);
        categoryVO.setArticleCount(articleCount);
        
        return categoryVO;
    }

    /**
     * 获取分类列表（平铺结构）
     * @return 分类VO列表
     */
    @Override
    public List<CategoryVO> getCategoryList() {
        // 查询所有分类
        List<Category> categoryList = list(new LambdaQueryWrapper<Category>()
                .orderByAsc(Category::getOrder));
        
        // 转换为VO
        return categoryList.stream().map(category -> {
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyProperties(category, categoryVO);
            
            // 设置父分类名称
            if (category.getParentId() != null) {
                Category parentCategory = getById(category.getParentId());
                if (parentCategory != null) {
                    categoryVO.setParentName(parentCategory.getName());
                }
            }
            
            // 查询文章数量
            int articleCount = baseMapper.countArticleByCategoryId(category.getId());
            categoryVO.setArticleCount(articleCount);
            
            return categoryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取分类树形结构
     * @return 分类树形结构
     */
    @Override
    public List<CategoryVO> getCategoryTree() {
        // 查询所有分类
        List<Category> allCategories = list(new LambdaQueryWrapper<Category>()
                .orderByAsc(Category::getOrder));
        
        // 转换为VO
        List<CategoryVO> allCategoryVOs = allCategories.stream().map(category -> {
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyProperties(category, categoryVO);
            
            // 查询文章数量
            int articleCount = baseMapper.countArticleByCategoryId(category.getId());
            categoryVO.setArticleCount(articleCount);
            
            return categoryVO;
        }).collect(Collectors.toList());
        
        // 构建分类树
        // 找出所有顶级分类
        List<CategoryVO> rootCategories = allCategoryVOs.stream()
                .filter(categoryVO -> categoryVO.getParentId() == null)
                .collect(Collectors.toList());
        
        // 为每个顶级分类设置子分类
        rootCategories.forEach(rootCategory -> setChildren(rootCategory, allCategoryVOs));
        
        return rootCategories;
    }

    /**
     * 检查分类是否存在
     * @param id 分类ID
     * @return 是否存在
     */
    @Override
    public boolean exists(Long id) {
        if (id == null) {
            return false;
        }
        return getById(id) != null;
    }

    /**
     * 检查分类名称是否已存在
     * @param name 分类名称
     * @param excludeId 排除的ID（更新时用）
     * @return 是否存在
     */
    private boolean checkNameExist(String name, Long excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<Category>()
                .eq(Category::getName, name);
        
        if (excludeId != null) {
            queryWrapper.ne(Category::getId, excludeId);
        }
        
        return count(queryWrapper) > 0;
    }

    /**
     * 检查是否是子分类
     * @param parentId 父分类ID
     * @param childId 子分类ID
     * @return 是否是子分类
     */
    private boolean isChildCategory(Long parentId, Long childId) {
        // 如果parentId与childId相同，则形成循环引用
        if (parentId.equals(childId)) {
            return true;
        }
        
        // 查询childId的所有子分类
        List<Category> children = baseMapper.selectByParentId(childId);
        
        // 递归检查每个子分类
        for (Category child : children) {
            if (isChildCategory(parentId, child.getId())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 递归设置子分类
     * @param parent 父分类
     * @param allCategories 所有分类
     */
    private void setChildren(CategoryVO parent, List<CategoryVO> allCategories) {
        List<CategoryVO> children = allCategories.stream()
                .filter(category -> parent.getId().equals(category.getParentId()))
                .collect(Collectors.toList());
        
        if (!children.isEmpty()) {
            parent.setChildren(children);
            
            // 为每个子分类设置父分类名称和子分类
            children.forEach(child -> {
                child.setParentName(parent.getName());
                setChildren(child, allCategories);
            });
        }
    }
} 