package com.blog.common.utils;

import com.blog.common.exception.BusinessException;
import com.blog.entity.User;
import com.blog.service.UserService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 安全工具类，用于获取当前登录用户信息
 */
@Component
public class SecurityUtils {

    @Resource
    private UserService userService;

    /**
     * 获取当前登录用户名
     * 
     * @return 当前登录用户名
     */
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BusinessException("用户未登录");
        }
        return authentication.getName();
    }
    
    /**
     * 获取当前登录用户ID
     * 
     * @return 当前登录用户ID
     */
    public Long getCurrentUserId() {
        String username = getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new BusinessException("未找到用户信息");
        }
        return user.getId();
    }
    
    /**
     * 获取当前登录用户信息
     * 
     * @return 当前登录用户信息
     */
    public User getCurrentUser() {
        String username = getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new BusinessException("未找到用户信息");
        }
        return user;
    }
    
    /**
     * 判断当前用户是否是管理员
     * 
     * @return 是否是管理员
     */
    public boolean isAdmin() {
        User user = getCurrentUser();
        return "admin".equalsIgnoreCase(user.getRole());
    }
} 