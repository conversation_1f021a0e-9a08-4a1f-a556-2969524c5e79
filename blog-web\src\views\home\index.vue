<template>
  <div class="home">
    <div class="site-header">
      <h1>欢迎访问{{ siteTitle }}</h1>
      <p v-if="siteDescription" class="site-description">{{ siteDescription }}</p>
      <div v-if="siteKeywords && siteKeywords.length" class="site-tags">
        <span class="tags-label">网站标签：</span>
        <el-tag
          v-for="keyword in siteKeywords"
          :key="keyword"
          size="small"
          class="keyword-tag">
          {{ keyword }}
        </el-tag>
      </div>
    </div>
    <div class="article-list" data-cy="article-list">
      <el-empty v-if="articles.length === 0" description="暂无文章"></el-empty>
      <div v-else>
        <div v-for="article in articles" :key="article.id" class="article-item">
          <h2 class="article-title">
            <router-link :to="`/article/${article.id}`" data-cy="article-link">{{ article.title }}</router-link>
          </h2>
          <div class="article-info">
            <span>作者: {{ article.author }}</span>
            <span>发布时间: {{ formatDate(article.createTime) }}</span>
            <span v-if="article.categoryName">分类: {{ article.categoryName }}</span>
            <div v-if="article.tags && article.tags.length" class="tag-group">
              <span>标签:</span>
              <el-tag
                v-for="tag in article.tags"
                :key="tag.id"
                size="small"
                class="article-tag"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </div>
          <div class="article-summary">{{ article.summary }}</div>
          <div class="article-footer">
            <router-link :to="`/article/${article.id}`">阅读全文</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { getArticles } from '@/api/article'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'

export default {
  name: 'Home',
  setup() {
    const articles = ref([])
    const loading = ref(false)

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title)
    const siteDescription = computed(() => siteConfig.value.description)
    const siteKeywords = computed(() => {
      if (siteConfig.value.keywords) {
        return siteConfig.value.keywords.split(',').map(k => k.trim()).filter(k => k)
      }
      return []
    })

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    }

    // 获取文章列表
    const fetchArticles = async () => {
      loading.value = true;
      try {
        const res = await getArticles({
          current: 1,
          size: 10,
          status: 1 // 已发布的文章
        });
        
        if (res.code === 200 && res.data) {
          articles.value = res.data.records || [];
        }
      } catch (error) {
        console.error('获取文章列表失败', error);
      } finally {
        loading.value = false;
      }
    }

    onMounted(() => {
      fetchSiteConfig() // 获取完整的网站配置信息
      fetchArticles()
    })

    return {
      articles,
      loading,
      siteTitle,
      siteDescription,
      siteKeywords,
      formatDate
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.site-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.site-header h1 {
  margin: 0 0 10px 0;
  color: #333;
}

.site-description {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.6;
}

.site-tags {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.tags-label {
  color: #666;
  font-size: 14px;
  margin-right: 5px;
}

.keyword-tag {
  margin: 2px;
}

.article-list {
  margin-top: 20px;
}

.article-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.article-title {
  margin-bottom: 10px;
}

.article-title a {
  color: #333;
  text-decoration: none;
}

.article-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
  align-items: center;
}

.tag-group {
  display: inline-flex;
  gap: 5px;
  align-items: center;
}

.article-summary {
  margin-bottom: 15px;
  line-height: 1.6;
}

.article-footer a {
  color: #409eff;
  text-decoration: none;
}
</style> 