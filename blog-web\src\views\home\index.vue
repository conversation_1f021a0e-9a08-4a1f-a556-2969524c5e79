<template>
  <div class="modern-home">
    <!-- 🎨 英雄区域 -->
    <section class="hero-section">
      <div class="hero-backdrop"></div>
      <div class="hero-content">
        <div class="hero-text animate-fade-in">
          <h1 class="hero-title">
            欢迎访问
            <span class="title-highlight">{{ siteTitle }}</span>
          </h1>
          <p v-if="siteDescription" class="hero-description">
            {{ siteDescription }}
          </p>
          <div v-if="siteKeywords && siteKeywords.length" class="hero-tags">
            <div class="tags-container">
              <el-tag
                v-for="keyword in siteKeywords"
                :key="keyword"
                class="hero-tag"
                effect="plain"
              >
                {{ keyword }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="hero-decoration">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>
    </section>

    <!-- 📚 文章列表区域 -->
    <section class="articles-section">
      <div class="section-header">
        <h2 class="section-title">
          <span class="title-text">最新文章</span>
          <div class="title-underline"></div>
        </h2>
        <p class="section-subtitle">探索最新的思考与分享</p>
      </div>

      <div class="articles-container" data-cy="article-list">
        <!-- 空状态 -->
        <div v-if="articles.length === 0" class="empty-state">
          <div class="empty-icon">
            <el-icon :size="64"><Document /></el-icon>
          </div>
          <h3 class="empty-title">暂无文章</h3>
          <p class="empty-description">还没有发布任何文章，敬请期待精彩内容</p>
        </div>

        <!-- 文章网格 -->
        <div v-else class="articles-grid">
          <article
            v-for="(article, index) in articles"
            :key="article.id"
            class="article-card animate-slide-up"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <!-- 文章头部 -->
            <div class="article-header">
              <div class="article-meta">
                <div class="author-info">
                  <div class="author-avatar">
                    <span>{{ article.author.charAt(0).toUpperCase() }}</span>
                  </div>
                  <div class="author-details">
                    <span class="author-name">{{ article.author }}</span>
                    <time class="publish-date">{{ formatDate(article.createTime) }}</time>
                  </div>
                </div>
                <div v-if="article.categoryName" class="category-badge">
                  {{ article.categoryName }}
                </div>
              </div>
            </div>

            <!-- 文章内容 -->
            <div class="article-content">
              <h3 class="article-title">
                <router-link
                  :to="`/article/${article.id}`"
                  class="title-link"
                  data-cy="article-link"
                >
                  {{ article.title }}
                </router-link>
              </h3>

              <p class="article-summary">{{ article.summary }}</p>

              <!-- 标签 -->
              <div v-if="article.tags && article.tags.length" class="article-tags">
                <el-tag
                  v-for="tag in article.tags"
                  :key="tag.id"
                  size="small"
                  class="article-tag"
                  effect="plain"
                >
                  {{ tag.name }}
                </el-tag>
              </div>
            </div>

            <!-- 文章底部 -->
            <div class="article-footer">
              <router-link
                :to="`/article/${article.id}`"
                class="read-more-btn"
              >
                <span>阅读全文</span>
                <el-icon class="read-more-icon"><ArrowRight /></el-icon>
              </router-link>

              <div class="article-stats">
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>{{ article.viewCount || 0 }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  <span>{{ article.commentCount || 0 }}</span>
                </div>
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { getArticles } from '@/api/article'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'
import { Document, ArrowRight, View, ChatDotRound } from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    Document,
    ArrowRight,
    View,
    ChatDotRound
  },
  setup() {
    const articles = ref([])
    const loading = ref(false)

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title)
    const siteDescription = computed(() => siteConfig.value.description)
    const siteKeywords = computed(() => {
      if (siteConfig.value.keywords) {
        return siteConfig.value.keywords.split(',').map(k => k.trim()).filter(k => k)
      }
      return []
    })

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    }

    // 获取文章列表
    const fetchArticles = async () => {
      loading.value = true;
      try {
        const res = await getArticles({
          current: 1,
          size: 10,
          status: 1 // 已发布的文章
        });

        if (res.code === 200 && res.data) {
          articles.value = res.data.records || [];
        }
      } catch (error) {
        console.error('获取文章列表失败', error);
      } finally {
        loading.value = false;
      }
    }

    onMounted(() => {
      fetchSiteConfig() // 获取完整的网站配置信息
      fetchArticles()
    })

    return {
      articles,
      loading,
      siteTitle,
      siteDescription,
      siteKeywords,
      formatDate
    }
  }
}
</script>

<style scoped>
// 🎨 现代化首页样式
.modern-home {
  min-height: 100vh;
  background: var(--bg-secondary);
}

// ✨ 英雄区域
.hero-section {
  position: relative;
  padding: var(--spacing-16) 0 var(--spacing-12);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-6)) var(--spacing-8);

  @media (max-width: 768px) {
    padding: var(--spacing-12) 0 var(--spacing-8);
    margin: calc(-1 * var(--spacing-6)) calc(-1 * var(--spacing-4)) var(--spacing-6);
  }
}

.hero-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: 0 var(--spacing-4);
  }
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: white;
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-tight);

  @media (max-width: 768px) {
    font-size: var(--font-size-3xl);
  }
}

.title-highlight {
  background: linear-gradient(45deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-description {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-6);
  line-height: var(--line-height-relaxed);

  @media (max-width: 768px) {
    font-size: var(--font-size-base);
  }
}

.hero-tags {
  margin-bottom: var(--spacing-4);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  justify-content: flex-start;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.hero-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all var(--transition-fast);

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }
}

// 🎭 装饰元素
.hero-decoration {
  position: relative;
  width: 300px;
  height: 300px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    display: none;
  }
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 20px;
  left: 20px;
  animation-delay: 0s;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 100px;
  right: 40px;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 40px;
  left: 80px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
// 📚 文章列表区域
.articles-section {
  padding: var(--spacing-8) var(--spacing-6);
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: var(--spacing-6) var(--spacing-4);
  }
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.section-title {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-3);
}

.title-text {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-underline {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

// 📄 空状态
.empty-state {
  text-align: center;
  padding: var(--spacing-16) var(--spacing-8);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-light);
}

.empty-icon {
  margin-bottom: var(--spacing-4);
  color: var(--text-tertiary);
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.empty-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

// 📰 文章网格
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--spacing-6);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}

// 📝 文章卡片
.article-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-medium);

    &::before {
      transform: scaleX(1);
    }
  }
}

// 📋 文章头部
.article-header {
  margin-bottom: var(--spacing-4);
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.author-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.author-avatar {
  width: 40px;
  height: 40px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.author-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.publish-date {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.category-badge {
  padding: var(--spacing-1) var(--spacing-3);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--border-light);
}
// 📄 文章内容
.article-content {
  margin-bottom: var(--spacing-6);
}

.article-title {
  margin-bottom: var(--spacing-3);
}

.title-link {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-decoration: none;
  line-height: var(--line-height-tight);
  transition: all var(--transition-fast);

  &:hover {
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.article-summary {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-4);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.article-tag {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);

  &:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
  }
}

// 🔗 文章底部
.article-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-light);
}

.read-more-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background: var(--primary-gradient);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);

    .read-more-icon {
      transform: translateX(4px);
    }
  }
}

.read-more-icon {
  transition: transform var(--transition-fast);
}

.article-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);

  .el-icon {
    font-size: var(--font-size-sm);
  }
}

// 🎬 动画效果
.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 📱 响应式优化
@media (max-width: 640px) {
  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .hero-description {
    font-size: var(--font-size-sm);
  }

  .articles-grid {
    grid-template-columns: 1fr;
  }

  .article-card {
    padding: var(--spacing-4);
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .article-footer {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }

  .article-stats {
    align-self: stretch;
    justify-content: space-around;
  }
}
</style>