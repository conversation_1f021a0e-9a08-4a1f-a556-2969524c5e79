describe('用户关注功能测试', () => {
  const testUser = {
    username: 'testuser',
    password: '123456'
  }
  
  beforeEach(() => {
    // 访问首页
    cy.visit('/')
    // 清除本地存储
    cy.clearLocalStorage()
  })
  
  it('未登录用户尝试关注作者应跳转到登录页', () => {
    // 访问文章详情页
    cy.visit('/article/1')
    cy.wait(1000)
    
    // 点击关注按钮
    cy.get('.article-author .follow-btn').click()
    
    // 应跳转到登录页
    cy.url().should('include', '/login')
  })
  
  it('用户可以关注和取消关注作者', () => {
    // 登录
    cy.visit('/login')
    cy.get('input[name="username"]').type(testUser.username)
    cy.get('input[name="password"]').type(testUser.password)
    cy.get('button[type="submit"]').click()
    cy.wait(1000)
    
    // 访问文章详情页
    cy.visit('/article/1')
    cy.wait(1000)
    
    // 获取作者关注按钮状态
    cy.get('.article-author .follow-btn').then(($btn) => {
      const isFollowing = $btn.text().includes('已关注')
      
      if (isFollowing) {
        // 如果已关注，则先取消关注
        cy.get('.article-author .follow-btn').click()
        cy.wait(500)
        cy.get('.article-author .follow-btn').should('contain', '关注')
        
        // 再次关注
        cy.get('.article-author .follow-btn').click()
        cy.wait(500)
        cy.get('.article-author .follow-btn').should('contain', '已关注')
      } else {
        // 如果未关注，则关注
        cy.get('.article-author .follow-btn').click()
        cy.wait(500)
        cy.get('.article-author .follow-btn').should('contain', '已关注')
        
        // 再次取消关注
        cy.get('.article-author .follow-btn').click()
        cy.wait(500)
        cy.get('.article-author .follow-btn').should('contain', '关注')
      }
    })
  })
  
  it('用户可以查看自己的关注列表', () => {
    // 登录
    cy.visit('/login')
    cy.get('input[name="username"]').type(testUser.username)
    cy.get('input[name="password"]').type(testUser.password)
    cy.get('button[type="submit"]').click()
    cy.wait(1000)
    
    // 访问我的关注页面
    cy.visit('/user/follow')
    cy.wait(1000)
    
    // 检查页面标题
    cy.get('.page-title').should('contain', '我的关注')
    
    // 检查标签页
    cy.get('.el-tabs__item').should('have.length', 2)
    cy.get('.el-tabs__item').eq(0).should('contain', '我关注的人')
    cy.get('.el-tabs__item').eq(1).should('contain', '关注我的人')
    
    // 切换到粉丝列表
    cy.get('.el-tabs__item').eq(1).click()
    cy.wait(1000)
  })
  
  it('用户可以在关注列表中取消关注', () => {
    // 登录
    cy.visit('/login')
    cy.get('input[name="username"]').type(testUser.username)
    cy.get('input[name="password"]').type(testUser.password)
    cy.get('button[type="submit"]').click()
    cy.wait(1000)
    
    // 访问我的关注页面
    cy.visit('/user/follow')
    cy.wait(1000)
    
    // 检查是否有关注的用户
    cy.get('body').then(($body) => {
      // 如果有关注的用户
      if ($body.find('.user-card').length > 0) {
        // 获取关注用户数量
        cy.get('.user-card').then(($cards) => {
          const count = $cards.length
          
          // 取消关注第一个用户
          cy.get('.user-card').eq(0).find('button').click()
          cy.get('.el-message-box__wrapper').find('button.el-button--primary').click()
          cy.wait(500)
          
          // 检查列表是否减少了一个用户
          cy.get('.user-card').should('have.length', count - 1)
        })
      } else {
        // 如果没有关注的用户，应显示空列表提示
        cy.get('.empty-list').should('be.visible')
      }
    })
  })
}) 