package com.blog.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息视图对象
 */
@Data
@ApiModel(value = "用户信息视图对象", description = "用户信息视图对象")
public class UserInfoVO {
    
    /**
     * ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;
    
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;
    
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像URL")
    private String avatar;
    
    /**
     * 角色
     */
    @ApiModelProperty(value = "角色编码")
    private String role;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态：0-禁用，1-正常")
    private Integer status;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
} 