package com.blog.common.exception;

/**
 * 409 - Conflict 异常
 * <p>
 * 当请求与服务器当前状态冲突，导致无法完成请求时（例如，尝试删除一个非空的资源），抛出此异常。
 * HTTP状态码将由 GlobalExceptionHandler 统一处理。
 */
public class ConflictException extends BusinessException {

    /**
     * 构造函数
     *
     * @param message 异常信息
     */
    public ConflictException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param cause   原始异常
     */
    public ConflictException(String message, Throwable cause) {
        super(message, cause);
    }
} 