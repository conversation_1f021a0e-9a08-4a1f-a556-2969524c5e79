package com.blog.service;

import com.blog.dto.CategoryDTO;
import com.blog.entity.Category;
import com.blog.vo.CategoryVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService extends IService<Category> {
    
    /**
     * 添加分类
     * @param categoryDTO 分类DTO
     * @return 新增分类的VO对象
     */
    CategoryVO addCategory(CategoryDTO categoryDTO);
    
    /**
     * 更新分类
     * @param categoryDTO 分类DTO
     * @return 是否更新成功
     */
    boolean updateCategory(CategoryDTO categoryDTO);
    
    /**
     * 删除分类
     * @param id 分类ID
     * @return 是否删除成功
     */
    boolean deleteCategory(Long id);
    
    /**
     * 获取分类详情
     * @param id 分类ID
     * @return 分类VO
     */
    CategoryVO getCategoryDetail(Long id);
    
    /**
     * 获取分类列表（平铺结构）
     * @return 分类VO列表
     */
    List<CategoryVO> getCategoryList();
    
    /**
     * 获取分类树形结构
     * @return 分类树形结构
     */
    List<CategoryVO> getCategoryTree();
    
    /**
     * 检查分类是否存在
     * @param id 分类ID
     * @return 是否存在
     */
    boolean exists(Long id);
} 