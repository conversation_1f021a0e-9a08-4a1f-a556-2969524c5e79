package com.blog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.EmailTemplateDTO;
import com.blog.entity.EmailTemplate;
import com.blog.mapper.EmailTemplateMapper;
import com.blog.service.EmailTemplateService;
import com.blog.vo.EmailTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 邮件模板服务实现类
 */
@Slf4j
@Service
public class EmailTemplateServiceImpl extends ServiceImpl<EmailTemplateMapper, EmailTemplate> implements EmailTemplateService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTemplate(EmailTemplateDTO templateDTO) {
        // 检查模板代码是否已存在
        if (baseMapper.countByTemplateCode(templateDTO.getTemplateCode(), null) > 0) {
            throw new BusinessException("模板代码已存在");
        }

        // 转换DTO为实体
        EmailTemplate template = new EmailTemplate();
        BeanUtils.copyProperties(templateDTO, template);
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());

        // 保存模板
        save(template);
        return template.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplate(EmailTemplateDTO templateDTO) {
        // 检查模板是否存在
        if (getById(templateDTO.getId()) == null) {
            throw new NotFoundException("邮件模板不存在");
        }

        // 检查模板代码是否已存在（排除当前模板）
        if (baseMapper.countByTemplateCode(templateDTO.getTemplateCode(), templateDTO.getId()) > 0) {
            throw new BusinessException("模板代码已存在");
        }

        // 转换DTO为实体
        EmailTemplate template = new EmailTemplate();
        BeanUtils.copyProperties(templateDTO, template);
        template.setUpdateTime(LocalDateTime.now());

        // 更新模板
        return updateById(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long id) {
        // 检查模板是否存在
        EmailTemplate template = getById(id);
        if (template == null) {
            throw new NotFoundException("邮件模板不存在");
        }

        // 检查是否为系统默认模板（可以根据需要添加此逻辑）
        if ("TEST_EMAIL".equals(template.getTemplateCode()) || 
            "COMMENT_NOTIFICATION".equals(template.getTemplateCode()) ||
            "LIKE_NOTIFICATION".equals(template.getTemplateCode())) {
            throw new BusinessException("系统默认模板不能删除");
        }

        // 删除模板
        return removeById(id);
    }

    @Override
    public EmailTemplateVO getTemplateDetail(Long id) {
        // 查询模板
        EmailTemplate template = getById(id);
        if (template == null) {
            throw new NotFoundException("邮件模板不存在");
        }

        // 转换为VO
        EmailTemplateVO templateVO = new EmailTemplateVO();
        BeanUtils.copyProperties(template, templateVO);
        templateVO.setStatusText(template.getStatus() == 1 ? "启用" : "禁用");

        return templateVO;
    }

    @Override
    public List<EmailTemplateVO> getTemplateList() {
        // 查询所有模板
        List<EmailTemplate> templates = list();

        // 转换为VO列表
        return templates.stream().map(template -> {
            EmailTemplateVO templateVO = new EmailTemplateVO();
            BeanUtils.copyProperties(template, templateVO);
            templateVO.setStatusText(template.getStatus() == 1 ? "启用" : "禁用");
            return templateVO;
        }).collect(Collectors.toList());
    }

    @Override
    public EmailTemplate getTemplateByCode(String templateCode) {
        return baseMapper.selectByTemplateCode(templateCode);
    }

    @Override
    public String renderTemplate(String templateCode, Map<String, String> variables) {
        // 获取模板
        EmailTemplate template = getTemplateByCode(templateCode);
        if (template == null) {
            throw new NotFoundException("邮件模板不存在: " + templateCode);
        }

        // 渲染模板内容
        String content = template.getContent();
        if (variables != null && !variables.isEmpty()) {
            for (Map.Entry<String, String> entry : variables.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                content = content.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
            }
        }

        return content;
    }

    @Override
    public Map<String, String> previewTemplate(String templateCode, Map<String, String> variables) {
        // 获取模板
        EmailTemplate template = getTemplateByCode(templateCode);
        if (template == null) {
            throw new NotFoundException("邮件模板不存在: " + templateCode);
        }

        // 渲染主题和内容
        String subject = template.getSubject();
        String content = template.getContent();

        if (variables != null && !variables.isEmpty()) {
            for (Map.Entry<String, String> entry : variables.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                String value = entry.getValue() != null ? entry.getValue() : "";
                subject = subject.replace(placeholder, value);
                content = content.replace(placeholder, value);
            }
        }

        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        result.put("templateType", template.getTemplateType());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplateStatus(Long id, Integer status) {
        // 检查模板是否存在
        EmailTemplate template = getById(id);
        if (template == null) {
            throw new NotFoundException("邮件模板不存在");
        }

        // 更新状态
        template.setStatus(status);
        template.setUpdateTime(LocalDateTime.now());

        return updateById(template);
    }
}
