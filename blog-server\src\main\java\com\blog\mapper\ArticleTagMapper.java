package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.ArticleTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文章标签关联Mapper接口
 */
@Mapper
public interface ArticleTagMapper extends BaseMapper<ArticleTag> {
    
    /**
     * 批量插入文章标签关联
     * @param articleId 文章ID
     * @param tagIds 标签ID列表
     * @return 影响的行数
     */
    int insertBatch(@Param("articleId") Long articleId, @Param("tagIds") List<Long> tagIds);
    
    /**
     * 根据文章ID删除所有关联
     * @param articleId 文章ID
     * @return 影响的行数
     */
    int deleteByArticleId(@Param("articleId") Long articleId);
    
    /**
     * 根据标签ID删除所有关联
     * @param tagId 标签ID
     * @return 影响的行数
     */
    int deleteByTagId(@Param("tagId") Long tagId);
} 