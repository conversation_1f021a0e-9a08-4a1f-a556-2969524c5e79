package com.blog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.entity.Article;
import com.blog.entity.Comment;
import com.blog.entity.Notification;
import com.blog.entity.User;
import com.blog.enums.NotificationType;
import com.blog.enums.ResourceType;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.CommentMapper;
import com.blog.mapper.NotificationMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.EmailService;
import com.blog.service.EmailTemplateService;
import com.blog.service.NotificationService;
import com.blog.vo.NotificationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知服务实现类
 */
@Slf4j
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> implements NotificationService {

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendLikeNotification(Long articleId, Long fromUserId) {
        // 获取文章信息
        Article article = articleMapper.selectById(articleId);
        if (article == null || article.getAuthorId().equals(fromUserId)) {
            // 文章不存在或者是自己点赞自己的文章，不发送通知
            return;
        }

        // 获取点赞用户信息
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            return;
        }

        String title = fromUser.getNickname() + " 点赞了你的文章";
        String content = "《" + article.getTitle() + "》";

        createNotification(article.getAuthorId(), fromUserId, NotificationType.LIKE.getCode(),
                title, content, articleId, ResourceType.ARTICLE.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendCollectNotification(Long articleId, Long fromUserId) {
        // 获取文章信息
        Article article = articleMapper.selectById(articleId);
        if (article == null || article.getAuthorId().equals(fromUserId)) {
            // 文章不存在或者是自己收藏自己的文章，不发送通知
            return;
        }

        // 获取收藏用户信息
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            return;
        }

        String title = fromUser.getNickname() + " 收藏了你的文章";
        String content = "《" + article.getTitle() + "》";

        createNotification(article.getAuthorId(), fromUserId, NotificationType.COLLECT.getCode(),
                title, content, articleId, ResourceType.ARTICLE.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendCommentNotification(Long articleId, Long commentId, Long fromUserId) {
        // 获取文章信息
        Article article = articleMapper.selectById(articleId);
        if (article == null || article.getAuthorId().equals(fromUserId)) {
            // 文章不存在或者是自己评论自己的文章，不发送通知
            return;
        }

        // 获取评论用户信息
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            return;
        }

        String title = fromUser.getNickname() + " 评论了你的文章";
        String content = "《" + article.getTitle() + "》";

        // 创建站内通知
        createNotification(article.getAuthorId(), fromUserId, NotificationType.COMMENT.getCode(),
                title, content, commentId, ResourceType.COMMENT.getCode());

        // 发送邮件通知
        sendCommentEmailNotification(article, fromUser, commentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendReplyNotification(Long commentId, Long replyId, Long fromUserId) {
        // 获取被回复的评论信息
        Comment comment = commentMapper.selectById(commentId);
        if (comment == null || comment.getUserId().equals(fromUserId)) {
            // 评论不存在或者是自己回复自己的评论，不发送通知
            return;
        }

        // 获取回复用户信息
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            return;
        }

        // 获取文章信息用于显示
        Article article = articleMapper.selectById(comment.getArticleId());
        String articleTitle = article != null ? article.getTitle() : "文章";

        String title = fromUser.getNickname() + " 回复了你的评论";
        String content = "在文章《" + articleTitle + "》中";

        // 创建站内通知
        createNotification(comment.getUserId(), fromUserId, NotificationType.REPLY.getCode(),
                title, content, comment.getArticleId(), ResourceType.ARTICLE.getCode());

        // 发送邮件通知
        sendReplyEmailNotification(article, comment, fromUser, replyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendFollowNotification(Long followedUserId, Long fromUserId) {
        if (followedUserId.equals(fromUserId)) {
            // 不能关注自己
            return;
        }

        // 获取关注用户信息
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            return;
        }

        String title = fromUser.getNickname() + " 关注了你";
        String content = "快去看看TA的动态吧！";

        createNotification(followedUserId, fromUserId, NotificationType.FOLLOW.getCode(),
                title, content, fromUserId, ResourceType.USER.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createNotification(Long userId, Long fromUserId, String type, String title,
                                    String content, Long resourceId, String resourceType) {
        try {
            // 检查是否存在相同的通知（防止重复通知）
            int existingCount = notificationMapper.countSimilarNotifications(
                    userId, fromUserId, type, resourceId, resourceType);
            if (existingCount > 0) {
                log.info("相同通知已存在，跳过创建: userId={}, fromUserId={}, type={}", userId, fromUserId, type);
                return true;
            }

            // 创建通知
            Notification notification = Notification.builder()
                    .userId(userId)
                    .fromUserId(fromUserId)
                    .type(type)
                    .title(title)
                    .content(content)
                    .resourceId(resourceId)
                    .resourceType(resourceType)
                    .isRead(0)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

            return save(notification);
        } catch (Exception e) {
            log.error("创建通知失败: userId={}, fromUserId={}, type={}", userId, fromUserId, type, e);
            return false;
        }
    }

    @Override
    public List<NotificationVO> getUserNotifications(Long userId, Integer isRead, Integer limit) {
        return notificationMapper.selectUserNotifications(userId, isRead, limit);
    }

    @Override
    public int getUnreadCount(Long userId) {
        return notificationMapper.countUnreadNotifications(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long userId, List<Long> notificationIds) {
        try {
            int updatedCount = notificationMapper.markNotificationsAsRead(userId, notificationIds);
            return updatedCount > 0;
        } catch (Exception e) {
            log.error("标记通知已读失败: userId={}, notificationIds={}", userId, notificationIds, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Long userId) {
        try {
            int updatedCount = notificationMapper.markAllNotificationsAsRead(userId);
            return updatedCount >= 0; // 即使没有未读通知也算成功
        } catch (Exception e) {
            log.error("标记所有通知已读失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotifications(Long userId, List<Long> notificationIds) {
        try {
            int deletedCount = notificationMapper.deleteUserNotifications(userId, notificationIds);
            return deletedCount > 0;
        } catch (Exception e) {
            log.error("删除通知失败: userId={}, notificationIds={}", userId, notificationIds, e);
            return false;
        }
    }

    /**
     * 发送评论邮件通知
     * @param article 文章信息
     * @param fromUser 评论用户
     * @param commentId 评论ID
     */
    private void sendCommentEmailNotification(Article article, User fromUser, Long commentId) {
        try {
            // 获取文章作者信息
            User author = userMapper.selectById(article.getAuthorId());
            if (author == null || author.getEmail() == null || author.getEmail().trim().isEmpty()) {
                log.info("文章作者邮箱为空，跳过邮件通知: articleId={}", article.getId());
                return;
            }

            // 获取评论内容
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                log.warn("评论不存在，跳过邮件通知: commentId={}", commentId);
                return;
            }

            // 准备邮件模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("USER_NAME", fromUser.getNickname());
            variables.put("ARTICLE_TITLE", article.getTitle());
            variables.put("COMMENT_CONTENT", comment.getContent());
            variables.put("ARTICLE_URL", "http://localhost:3000/article/" + article.getId());

            // 优先使用数据库模板，失败时降级使用硬编码模板
            String subject;
            String content;
            try {
                // 尝试使用数据库模板
                Map<String, String> preview = emailTemplateService.previewTemplate("COMMENT_NOTIFICATION", variables);
                subject = preview.get("subject");
                content = preview.get("content");
                log.info("使用数据库邮件模板发送评论通知: templateCode=COMMENT_NOTIFICATION");
            } catch (Exception e) {
                // 降级使用硬编码模板
                log.warn("使用数据库邮件模板失败，降级使用硬编码模板: {}", e.getMessage());
                subject = "您收到了新评论 - " + article.getTitle();
                content = buildCommentNotificationContent(variables);
            }

            // 发送HTML邮件
            boolean success = emailService.sendHtmlMail(author.getEmail(), subject, content);

            if (success) {
                log.info("评论邮件通知发送成功: to={}, articleId={}, commentId={}",
                    author.getEmail(), article.getId(), commentId);
            } else {
                log.warn("评论邮件通知发送失败: to={}, articleId={}, commentId={}",
                    author.getEmail(), article.getId(), commentId);
            }
        } catch (Exception e) {
            log.error("发送评论邮件通知异常: articleId={}, commentId={}", article.getId(), commentId, e);
        }
    }

    /**
     * 构建评论通知邮件内容
     * @param variables 模板变量
     * @return 邮件内容
     */
    private String buildCommentNotificationContent(Map<String, String> variables) {
        String template = "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">" +
                "<div style=\"background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">" +
                "<h1 style=\"color: white; margin: 0; font-size: 24px;\">💬 您收到了新评论</h1>" +
                "</div>" +
                "<div style=\"background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">" +
                "<p style=\"font-size: 16px;\">您好！</p>" +
                "<p style=\"font-size: 16px;\"><strong>{{USER_NAME}}</strong> 在您的文章《<strong>{{ARTICLE_TITLE}}</strong>》中发表了评论：</p>" +
                "<div style=\"background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;\">" +
                "<p style=\"font-style: italic; color: #495057;\">\"{{COMMENT_CONTENT}}\"</p>" +
                "</div>" +
                "<div style=\"text-align: center; margin: 30px 0;\">" +
                "<a href=\"{{ARTICLE_URL}}\" style=\"background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">查看评论</a>" +
                "</div>" +
                "<p style=\"color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;\">" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</div>";

        // 替换变量
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            template = template.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
        }

        return template;
    }

    /**
     * 发送回复邮件通知
     * @param article 文章信息
     * @param originalComment 被回复的评论
     * @param fromUser 回复用户
     * @param replyId 回复ID
     */
    private void sendReplyEmailNotification(Article article, Comment originalComment, User fromUser, Long replyId) {
        try {
            // 获取被回复评论的作者信息
            User replyToUser = userMapper.selectById(originalComment.getUserId());
            if (replyToUser == null || replyToUser.getEmail() == null || replyToUser.getEmail().trim().isEmpty()) {
                log.info("被回复用户邮箱为空，跳过邮件通知: userId={}", originalComment.getUserId());
                return;
            }

            // 获取回复内容
            Comment replyComment = commentMapper.selectById(replyId);
            if (replyComment == null) {
                log.warn("回复评论不存在，跳过邮件通知: replyId={}", replyId);
                return;
            }

            // 准备邮件模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("USER_NAME", fromUser.getNickname());
            variables.put("ARTICLE_TITLE", article.getTitle());
            variables.put("ORIGINAL_COMMENT", originalComment.getContent());
            variables.put("REPLY_CONTENT", replyComment.getContent());
            variables.put("ARTICLE_URL", "http://localhost:3000/article/" + article.getId());

            // 优先使用数据库模板，失败时降级使用硬编码模板
            String subject;
            String content;
            try {
                // 尝试使用数据库模板
                Map<String, String> preview = emailTemplateService.previewTemplate("REPLY_NOTIFICATION", variables);
                subject = preview.get("subject");
                content = preview.get("content");
                log.info("使用数据库邮件模板发送回复通知: templateCode=REPLY_NOTIFICATION");
            } catch (Exception e) {
                // 降级使用硬编码模板
                log.warn("使用数据库邮件模板失败，降级使用硬编码模板: {}", e.getMessage());
                subject = "您的评论收到了回复 - " + article.getTitle();
                content = buildReplyNotificationContent(variables);
            }

            // 发送HTML邮件
            boolean success = emailService.sendHtmlMail(replyToUser.getEmail(), subject, content);

            if (success) {
                log.info("回复邮件通知发送成功: to={}, articleId={}, replyId={}",
                    replyToUser.getEmail(), article.getId(), replyId);
            } else {
                log.warn("回复邮件通知发送失败: to={}, articleId={}, replyId={}",
                    replyToUser.getEmail(), article.getId(), replyId);
            }
        } catch (Exception e) {
            log.error("发送回复邮件通知异常: articleId={}, replyId={}", article.getId(), replyId, e);
        }
    }

    /**
     * 构建回复通知邮件内容
     * @param variables 模板变量
     * @return 邮件内容
     */
    private String buildReplyNotificationContent(Map<String, String> variables) {
        String template = "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">" +
                "<div style=\"background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">" +
                "<h1 style=\"color: white; margin: 0; font-size: 24px;\">💬 您的评论收到了回复</h1>" +
                "</div>" +
                "<div style=\"background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">" +
                "<p style=\"font-size: 16px;\">您好！</p>" +
                "<p style=\"font-size: 16px;\"><strong>{{USER_NAME}}</strong> 在文章《<strong>{{ARTICLE_TITLE}}</strong>》中回复了您的评论：</p>" +
                "<div style=\"background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #2196f3;\">" +
                "<p style=\"margin: 0; color: #1976d2; font-weight: bold;\">您的评论：</p>" +
                "<p style=\"font-style: italic; color: #424242; margin: 5px 0 0 0;\">\"{{ORIGINAL_COMMENT}}\"</p>" +
                "</div>" +
                "<div style=\"background-color: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ff9800;\">" +
                "<p style=\"margin: 0; color: #f57c00; font-weight: bold;\">回复内容：</p>" +
                "<p style=\"font-style: italic; color: #424242; margin: 5px 0 0 0;\">\"{{REPLY_CONTENT}}\"</p>" +
                "</div>" +
                "<div style=\"text-align: center; margin: 30px 0;\">" +
                "<a href=\"{{ARTICLE_URL}}\" style=\"background-color: #e91e63; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">查看回复</a>" +
                "</div>" +
                "<p style=\"color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;\">" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</div>";

        // 替换变量
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            template = template.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
        }

        return template;
    }
}
