package com.blog.service;

import com.blog.enums.FollowResultType;
import com.blog.vo.UserVO;

import java.util.List;

/**
 * 关注服务接口
 */
public interface FollowService {
    
    /**
     * 关注用户
     * @param followerId 关注者ID（当前用户）
     * @param followedId 被关注者ID
     * @return 操作结果，包含成功、用户不存在、已关注等状态
     */
    FollowResultType follow(Long followerId, Long followedId);
    
    /**
     * 取消关注用户
     * @param followerId 关注者ID（当前用户）
     * @param followedId 被关注者ID
     * @return 操作结果，包含成功、用户不存在、未关注等状态
     */
    FollowResultType unfollow(Long followerId, Long followedId);
    
    /**
     * 检查是否已关注用户
     * @param followerId 关注者ID（当前用户）
     * @param followedId 被关注者ID
     * @return 已关注返回true，否则返回false
     */
    boolean isFollowing(Long followerId, Long followedId);
    
    /**
     * 获取用户关注列表
     * @param userId 用户ID
     * @return 关注的用户列表
     */
    List<UserVO> getFollowingList(Long userId);
    
    /**
     * 获取用户粉丝列表
     * @param userId 用户ID
     * @return 粉丝用户列表
     */
    List<UserVO> getFollowerList(Long userId);
    
    /**
     * 获取用户关注数量
     * @param userId 用户ID
     * @return 关注数量
     */
    int getFollowingCount(Long userId);
    
    /**
     * 获取用户粉丝数量
     * @param userId 用户ID
     * @return 粉丝数量
     */
    int getFollowerCount(Long userId);
} 