/**
 * 应用全局配置
 */

// 获取环境变量，如果不存在则使用默认值
const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
const apiPrefix = import.meta.env.VITE_API_PREFIX || '/api';

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getBaseUrl() {
  return apiUrl;
}

/**
 * 获取API前缀
 * @returns {string} API前缀
 */
export function getApiPrefix() {
  return apiPrefix;
}

/**
 * 获取完整的API URL
 * @returns {string} 完整的API URL
 */
export function getApiUrl() {
  return `${apiUrl}${apiPrefix}`;
}

/**
 * 构建资源URL
 * @param {string} path 资源路径
 * @returns {string} 完整的资源URL
 */
export function buildResourceUrl(path) {
  if (!path) return '';
  
  // 如果是完整URL（以http或https开头），直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // 如果是相对路径（以/开头）
  if (path.startsWith('/')) {
    // 判断路径是否已包含API前缀
    if (path.startsWith(apiPrefix)) {
      return `${apiUrl}${path}`;
    } else {
      return `${apiUrl}${apiPrefix}${path}`;
    }
  }
  
  // 其他情况，假设是相对路径但没有/开头
  return `${apiUrl}${apiPrefix}/${path}`;
}

export default {
  apiUrl,
  apiPrefix,
  getBaseUrl,
  getApiPrefix,
  getApiUrl,
  buildResourceUrl
}; 