/**
 * 日期时间格式化工具
 */

/**
 * 将ISO格式的日期时间字符串 (例如 "2023-08-08T10:30:00") 格式化为 "YYYY-MM-DD HH:mm:ss"
 * @param {string} isoString - ISO格式的日期时间字符串
 * @returns {string} 格式化后的日期时间字符串，如果输入无效则返回空字符串
 */
export function formatDateTime(isoString) {
  if (!isoString) {
    return '';
  }
  
  try {
    // 将 'T' 替换为空格，并移除毫秒部分（如果存在）
    return isoString.replace('T', ' ').substring(0, 19);
  } catch (error) {
    console.error('日期格式化失败:', error);
    return ''; // 出错时返回空字符串
  }
} 