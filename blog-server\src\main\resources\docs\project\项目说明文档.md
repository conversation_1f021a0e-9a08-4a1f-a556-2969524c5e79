# 个人动态博客系统

一个功能完善、体验优秀、个性化的动态博客网站系统。

## 项目结构

```
blog-system/
├── blog-server/        # 后端服务
│   ├── src/            # 源代码
│   └── pom.xml         # Maven配置
└── blog-web/           # 前端应用
```

## 后端技术栈

- Spring Boot 2.7.x
- Spring Security + JWT
- MyBatis Plus
- MySQL 8.x
- Redis
- Lombok

## 前端技术栈

- Vue 3
- Pinia
- Vue Router
- Element Plus
- Axios
- Markdown编辑器

## 功能特性

### 核心功能
- **用户认证与授权**：注册登录、权限管理、用户状态控制
- **文章管理**：发布编辑、分类标签、状态管理、浏览统计
- **分类与标签管理**：文章分类、标签系统、关联管理
- **评论系统**：评论回复、状态管理、审核功能
- **点赞与收藏**：用户互动、数据统计
- **后台管理**：全面的管理功能和权限控制

### 仪表盘功能
- **数据统计与分析**：实时系统数据统计
- **总体统计**：文章总数、用户总数、评论总数、总浏览量
- **今日统计**：今日新增文章、用户、评论、浏览量
- **最近数据**：最近发布的文章和评论列表
- **权限控制**：仅管理员可访问

### 状态字段说明
- **用户状态**：status=1表示正常用户，status=0表示禁用用户
- **文章状态**：status=1表示已发布，status=0表示草稿
- **评论状态**：status=1表示已审核，status=0表示待审核

## 快速开始

### 后端服务

1. 创建MySQL数据库并导入SQL脚本：
   ```
   mysql -u root -p < blog-server/src/main/resources/db/blog_system.sql
   ```

2. 修改数据库配置：
   ```
   blog-server/src/main/resources/application.yml
   ```

3. 启动后端服务：
   ```
   cd blog-server
   mvn spring-boot:run
   ```

### 前端应用

1. 安装依赖：
   ```
   cd blog-web
   npm install
   ```

2. 启动开发服务器：
   ```
   npm run dev
   ```

## API文档

启动后端服务后，访问：
```
http://localhost:8080/api/swagger-ui/index.html
```

## 默认账户

- 管理员账户：admin
- 密码：123456 