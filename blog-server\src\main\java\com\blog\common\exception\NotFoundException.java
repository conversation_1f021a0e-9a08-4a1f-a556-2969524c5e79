package com.blog.common.exception;

/**
 * 404 - Not Found 异常
 * <p>
 * 当尝试访问的资源不存在时抛出此异常。
 * HTTP状态码将由 GlobalExceptionHandler 统一处理。
 */
public class NotFoundException extends BusinessException {

    /**
     * 构造函数
     *
     * @param message 异常信息
     */
    public NotFoundException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param cause   原始异常
     */
    public NotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
} 