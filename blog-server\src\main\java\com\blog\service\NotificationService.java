package com.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.entity.Notification;
import com.blog.vo.NotificationVO;

import java.util.List;

/**
 * 通知服务接口
 */
public interface NotificationService extends IService<Notification> {

    /**
     * 发送点赞通知
     * @param articleId 文章ID
     * @param fromUserId 点赞用户ID
     */
    void sendLikeNotification(Long articleId, Long fromUserId);

    /**
     * 发送收藏通知
     * @param articleId 文章ID
     * @param fromUserId 收藏用户ID
     */
    void sendCollectNotification(Long articleId, Long fromUserId);

    /**
     * 发送评论通知
     * @param articleId 文章ID
     * @param commentId 评论ID
     * @param fromUserId 评论用户ID
     */
    void sendCommentNotification(Long articleId, Long commentId, Long fromUserId);

    /**
     * 发送回复通知
     * @param commentId 被回复的评论ID
     * @param replyId 回复评论ID
     * @param fromUserId 回复用户ID
     */
    void sendReplyNotification(Long commentId, Long replyId, Long fromUserId);

    /**
     * 发送关注通知
     * @param followedUserId 被关注用户ID
     * @param fromUserId 关注用户ID
     */
    void sendFollowNotification(Long followedUserId, Long fromUserId);

    /**
     * 创建通知
     * @param userId 接收通知的用户ID
     * @param fromUserId 发送通知的用户ID
     * @param type 通知类型
     * @param title 通知标题
     * @param content 通知内容
     * @param resourceId 关联资源ID
     * @param resourceType 关联资源类型
     * @return 是否创建成功
     */
    boolean createNotification(Long userId, Long fromUserId, String type, String title, 
                             String content, Long resourceId, String resourceType);

    /**
     * 获取用户通知列表
     * @param userId 用户ID
     * @param isRead 是否已读（null表示查询全部）
     * @param limit 限制数量
     * @return 通知列表
     */
    List<NotificationVO> getUserNotifications(Long userId, Integer isRead, Integer limit);

    /**
     * 获取用户未读通知数量
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int getUnreadCount(Long userId);

    /**
     * 标记通知为已读
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 是否操作成功
     */
    boolean markAsRead(Long userId, List<Long> notificationIds);

    /**
     * 标记所有通知为已读
     * @param userId 用户ID
     * @return 是否操作成功
     */
    boolean markAllAsRead(Long userId);

    /**
     * 删除通知
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 是否操作成功
     */
    boolean deleteNotifications(Long userId, List<Long> notificationIds);
}
