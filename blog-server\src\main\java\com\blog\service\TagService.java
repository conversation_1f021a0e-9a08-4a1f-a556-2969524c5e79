package com.blog.service;

import com.blog.dto.TagDTO;
import com.blog.entity.Tag;
import com.blog.vo.TagVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标签服务接口
 */
public interface TagService extends IService<Tag> {
    
    /**
     * 添加标签
     * @param tagDTO 标签DTO
     * @return 新增标签的ID
     */
    Long addTag(TagDTO tagDTO);
    
    /**
     * 更新标签
     * @param tagDTO 标签DTO
     * @return 是否更新成功
     */
    boolean updateTag(TagDTO tagDTO);
    
    /**
     * 删除标签
     * @param id 标签ID
     * @return 是否删除成功
     */
    boolean deleteTag(Long id);
    
    /**
     * 获取标签详情
     * @param id 标签ID
     * @return 标签VO
     */
    TagVO getTagDetail(Long id);
    
    /**
     * 获取标签列表
     * @return 标签VO列表
     */
    List<TagVO> getTagList();
    
    /**
     * 获取文章的标签列表
     * @param articleId 文章ID
     * @return 标签列表
     */
    List<TagVO> getTagsByArticleId(Long articleId);
    
    /**
     * 获取热门标签（使用频率最高的标签）
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<TagVO> getPopularTags(int limit);
    
    /**
     * 检查标签是否存在
     * @param id 标签ID
     * @return 是否存在
     */
    boolean exists(Long id);
} 