package com.blog.service;

import com.blog.vo.ArticleVO;

import java.util.List;

/**
 * 收藏服务接口
 */
public interface CollectionService {
    
    /**
     * 收藏文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 收藏成功返回true，否则返回false
     */
    boolean collectArticle(Long articleId, Long userId);
    
    /**
     * 取消收藏文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 取消收藏成功返回true，否则返回false
     */
    boolean uncollectArticle(Long articleId, Long userId);
    
    /**
     * 检查用户是否已收藏文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 已收藏返回true，否则返回false
     */
    boolean hasCollectedArticle(Long articleId, Long userId);
    
    /**
     * 获取用户收藏的文章列表
     * @param userId 用户ID
     * @return 收藏的文章列表
     */
    List<ArticleVO> getUserCollections(Long userId);
    
    /**
     * 获取文章收藏数量
     * @param articleId 文章ID
     * @return 收藏数量
     */
    int getArticleCollectionCount(Long articleId);
} 