package com.blog.controller;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TestController 的集成测试类
 * @SpringBootTest: 这是一个核心注解，用于启动一个完整的Spring应用程序上下文，以便进行集成测试。
 * @AutoConfigureMockMvc: 这个注解会自动配置MockMvc对象，它是我们模拟HTTP请求的关键工具。
 */
@SpringBootTest
@AutoConfigureMockMvc
@DisplayName("TestController 集成测试")
class TestControllerTest {

    /**
     * 注入MockMvc实例。
     * Spring容器会自动创建并注入这个对象，我们可以用它来对Controller执行HTTP请求，而无需启动一个真实的Web服务器。
     */
    @Autowired
    private MockMvc mockMvc;

    /**
     * 测试用例1：测试匿名访问端点。
     * @DisplayName: 为测试方法提供一个可读的名称，会显示在测试报告中。
     * @Test: 声明这是一个JUnit 5的测试方法。
     */
    @Test
    @DisplayName("测试匿名访问端点 - 应成功")
    void testAnonymousEndpoint_ShouldSucceed() throws Exception {
        // 使用mockMvc执行一个HTTP GET请求，目标是"/test/anonymous"
        mockMvc.perform(get("/test/anonymous"))
                // andExpect用于添加一个"期望"的验证，这里我们验证HTTP响应状态码。
                // isOk() 验证状态码是否为 200 (OK)
                .andExpect(status().isOk())
                // 我们还可以深入验证响应的JSON内容。
                // jsonPath("$.code") 使用JSONPath表达式来提取JSON响应体中的'code'字段。
                .andExpect(jsonPath("$.code").value(200))
                // 验证'message'字段的值是否为"操作成功"
                .andExpect(jsonPath("$.message").value("操作成功"))
                // 验证'data'字段的值是否为"匿名访问成功"
                .andExpect(jsonPath("$.data").value("匿名访问成功"));
    }

    /**
     * 测试用例2：在未提供认证信息的情况下，访问需要认证的端点。
     */
    @Test
    @DisplayName("测试需认证的端点(未认证访问) - 应返回401未授权")
    void testAuthenticatedEndpoint_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // 模拟向受保护的端点 /test/authenticated 发送一个GET请求
        mockMvc.perform(get("/test/authenticated"))
                // 期望的结果是HTTP 401 Unauthorized。
                // 这是Spring Security的标准行为：当一个未认证的用户试图访问受保护资源时，会返回401。
                .andExpect(status().isUnauthorized());
    }
} 