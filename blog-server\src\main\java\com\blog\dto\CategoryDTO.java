package com.blog.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分类数据传输对象，用于接收前端传来的分类数据
 */
@Data
public class CategoryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 分类ID，添加时不需要，更新时必填
     */
    private Long id;
    
    /**
     * 分类名称，必填
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;
    
    /**
     * 分类描述，可选
     */
    private String description;
    
    /**
     * 排序值，必填，值越小排序越靠前
     */
    @NotNull(message = "排序值不能为空")
    private Integer order;
    
    /**
     * 父分类ID，可选，顶级分类为null
     */
    private Long parentId;
} 