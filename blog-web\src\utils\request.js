import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import router from '@/router'

// 获取API基础URL的函数
export function getApiBaseUrl() {
  // 获取当前页面的来源（协议 + 主机名 + 端口）
  const origin = window.location.origin;
  // 获取API前缀，与后端context-path对应
  const apiPrefix = '/api';
  // 返回完整的API基础URL
  return `${origin}${apiPrefix}`;
}

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // API的base_url
  timeout: 15000 // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
  config => {
    // 如果存在token，请求头携带token
    const token = getToken()
    if (token) {
      // 确保 Bearer 和 token 之间有空格
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// response拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果返回的状态码不是200，说明接口请求失败
    if (res.code !== 200) {
      // 401: Token过期或未登录
      if (res.code === 401) {
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          router.push('/login')
        })
      } else {
        ElMessage({
          message: res.message || '系统异常',
          type: 'error',
          duration: 5 * 1000
        })
      }
      return Promise.reject(new Error(res.message || '系统异常'))
    } else {
      return res
    }
  },
  error => {
    console.log('请求错误: ' + error)
    let message = error.message
    if (error.response && error.response.status) {
      switch (error.response.status) {
        case 400:
          // 优先使用后端返回的具体错误信息
          if (error.response.data && error.response.data.message) {
            message = error.response.data.message;
          } else if (error.config && error.config.url && error.config.url.includes('/auth/login')) {
            message = '登录失败，用户名或密码错误'
          } else {
            message = '请求参数错误';
          }
          break;
        case 401:
          // 检查是否是文章详情页的请求
          if (error.config && error.config.url && 
             (error.config.url.includes('/articles/') || 
              error.config.url.includes('/interaction/status/'))) {
            // 不跳转，只显示提示信息
            message = '未登录，部分功能可能受限'
            ElMessage({
              message: message,
              type: 'info',
              duration: 3 * 1000
            })
            return Promise.reject(error) // 已显示消息，直接返回
          } else {
            // 其他接口的401错误，跳转到登录页
            message = '登录状态已过期，请重新登录'
            router.push('/login')
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求错误，未找到该资源'
          break
        case 409:
          // 处理资源冲突错误，通常是因为存在关联数据
          if (error.response.data && error.response.data.message) {
            message = error.response.data.message
            // 针对评论删除的特殊提示
            if (message.includes("评论有回复") || message.includes("已被其他用户回复")) {
              ElMessage({
                message: message,
                type: 'warning',
                duration: 5 * 1000
              })
              return Promise.reject(error) // 已显示消息，直接返回
            }
          } else {
            message = '该资源正在被使用，无法删除'
          }
          break
        case 500:
          // 检查是否是登录相关的错误
          if (error.config && error.config.url && error.config.url.includes('/auth/login')) {
            message = '登录失败，请检查用户名和密码'
          } else {
            message = '服务器端出错'
          }
          break
        default:
          message = error.message
      }
    }
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service 