<template>
  <div class="user-comments" data-cy="user-comments">
    <h1 class="page-title">我的评论</h1>
    
    <div class="comment-list" v-loading="loading">
      <div v-if="comments.length === 0" class="empty-tip">
        <el-empty description="暂无评论" />
      </div>
      
      <div v-else>
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <div class="comment-content">{{ comment.content }}</div>
          
          <div class="comment-meta">
            <div class="article-info">
              评论于文章: 
              <router-link v-if="!comment.articleDeleted" :to="`/article/${comment.articleId}`">
                {{ comment.articleTitle }}
              </router-link>
              <span v-else class="deleted-article-title">
                {{ comment.articleTitle }}
              </span>
            </div>
            
            <div class="comment-time">{{ formatDateTime(comment.createTime) }}</div>
            
            <div class="comment-status">
              <el-tag :type="comment.status === 1 ? 'success' : 'info'" size="small">
                {{ comment.status === 1 ? '已发布' : '审核中' }}
              </el-tag>
            </div>
          </div>
          
          <div class="comment-actions">
            <el-button 
              v-if="!comment.articleDeleted"
              type="text" 
              size="small" 
              @click="navigateToArticle(comment.articleId)"
              data-cy="view-article-btn"
            >
              查看文章
            </el-button>
            
            <el-button 
              type="text" 
              size="small"
              class="delete-btn"
              @click="handleDelete(comment.id)"
              data-cy="delete-comment-btn"
            >
              删除评论
            </el-button>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next, total"
            :total="total"
            v-model:current-page="currentPage"
            :page-size="pageSize"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentUserComments, deleteComment } from '@/api/comment'
import { formatDateTime } from '@/utils/format'

export default {
  name: 'UserComments',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const comments = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    const fetchUserComments = async () => {
      loading.value = true
      try {
        // 注意：理想情况下，API应该支持分页参数
        const res = await getCurrentUserComments({ 
          page: currentPage.value, 
          size: pageSize.value 
        })
        if (res.code === 200) {
          // 如果API返回分页数据，则使用data.records；否则，直接使用data
          comments.value = res.data.records || res.data || []
          total.value = res.data.total || res.data.length || 0
        }
      } catch (error) {
        console.error('获取评论列表失败', error)
        // 全局错误处理会显示消息，这里可以不显示
      } finally {
        loading.value = false
      }
    }
    
    const navigateToArticle = (articleId) => {
      router.push(`/article/${articleId}`)
    }
    
    const handleDelete = (commentId) => {
      ElMessageBox.confirm('确定要删除这条评论吗？此操作不可撤销', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteComment(commentId)
          ElMessage.success('评论删除成功')
          // 如果删除的是当前页的最后一条，需要考虑返回上一页
          if (comments.value.length === 1 && currentPage.value > 1) {
            currentPage.value--
          }
          fetchUserComments()
        } catch (error) {
          console.error('删除评论失败', error)
        }
      }).catch(() => {})
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
      fetchUserComments()
    }
    
    onMounted(() => {
      fetchUserComments()
    })
    
    return {
      loading,
      comments,
      total,
      currentPage,
      pageSize,
      navigateToArticle,
      handleDelete,
      handleCurrentChange,
      formatDateTime,
    }
  }
}
</script>

<style scoped>
.user-comments {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 30px;
}

.comment-item {
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.comment-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.comment-content {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.comment-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.article-info {
  flex-grow: 1;
}

.article-info a {
  color: #409EFF;
  text-decoration: none;
}

.article-info a:hover {
  text-decoration: underline;
}

.deleted-article-title {
  color: #999;
  font-style: italic;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.delete-btn {
  color: #F56C6C;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-tip {
  margin-top: 50px;
}
</style> 