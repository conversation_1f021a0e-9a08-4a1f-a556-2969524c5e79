/**
 * 测试套件：文章浏览流程
 * 描述：这个套件覆盖了从首页到文章详情页的核心用户浏览路径。
 */
describe('文章浏览流程', () => {

  // beforeEach 是一个"钩子"，在当前 describe 块中的每个 it 测试用例运行之前，都会先执行它。
  // 我们在这里准备测试数据，确保测试环境的一致性。
  beforeEach(() => {
    // 使用 cy.request() 直接与后端API交互，绕过UI，快速创建测试数据。
    // 步骤1: 以管理员身份登录
    cy.request({
      method: 'POST',
      url: '/api/auth/login', // 假设您的API基础路径是/api
      body: {
        username: 'admin',
        password: 'admin123'  // 根据您的信息，此为正确的密码
      },
      failOnStatusCode: false // 允许我们处理非2xx的响应码
    }).then(loginResponse => {
      // 增加健壮性断言：确保登录请求本身是成功的，并且返回了数据
      expect(loginResponse.status).to.eq(200);
      expect(loginResponse.body.data, '登录API未返回有效的data对象，请检查用户名和密码').to.not.be.null;

      // 登录成功后，从响应体中获取 token 和 tokenHead
      const token = loginResponse.body.data.token;
      const tokenHead = loginResponse.body.data.tokenHead;

      // 步骤2: 使用获取到的 token 创建一篇新文章
      const articleTitle = `Cypress测试文章 ${Date.now()}`;
      cy.request({
        method: 'POST',
        url: '/api/articles',
        headers: {
          // 拼接 tokenHead 和 token，组成完整的 Authorization 请求头
          'Authorization': `${tokenHead}${token}`
        },
        body: {
          title: articleTitle,
          content: '这是由Cypress自动化测试创建的文章内容。',
          summary: '测试摘要',
          status: 1 // 1 通常代表"已发布"
        }
      });
    });
  });

  /**
   * 测试用例：用户应能从首页点击文章并成功进入详情页
   */
  it('用户应能从首页点击文章并成功进入详情页', () => {
    // 步骤1：访问首页
    cy.visit('/');

    // 步骤2：等待并验证文章列表已加载
    // 我们首先确保包含文章列表的容器是可见的，这间接证明了API请求已成功
    cy.get('[data-cy=article-list]').should('be.visible');

    // 步骤3：获取第一篇文章的标题，然后点击它
    // .first() 用于获取集合中的第一个元素
    // .invoke('text') 用于获取元素的文本内容
    // .as('articleTitle') 将获取到的文本保存为一个别名，方便后续引用
    cy.get('[data-cy=article-link]').first().invoke('text').as('articleTitle');
    cy.get('[data-cy=article-link]').first().click();

    // 步骤4：验证URL是否已正确跳转
    // .should('include', ...) 用于断言URL包含了特定字符串
    cy.url().should('include', '/article/');

    // 步骤5：验证详情页的标题是否与首页一致
    // @articleTitle 用于引用之前保存的别名
    // this.articleTitle 的方式来访问它
    cy.get('[data-cy=article-title]').should('be.visible').then(function(titleElement) {
      // 这里的 function() 不能是箭头函数，因为我们需要 Cypress 的 this 上下文
      expect(titleElement.text()).to.equal(this.articleTitle);
    });
  });
}); 