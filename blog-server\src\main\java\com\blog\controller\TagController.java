package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.TagDTO;
import com.blog.service.TagService;
import com.blog.vo.TagVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签控制器
 */
@RestController
@RequestMapping("/tags")
@Api(tags = "标签管理接口")
public class TagController {

    @Autowired
    private TagService tagService;

    /**
     * 添加标签
     * @param tagDTO 标签数据
     * @return 新增标签的ID
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "添加标签", notes = "需要管理员权限")
    public Result<Long> addTag(@Validated @RequestBody TagDTO tagDTO) {
        Long tagId = tagService.addTag(tagDTO);
        return Result.success(tagId);
    }

    /**
     * 更新标签
     * @param id 标签ID
     * @param tagDTO 标签数据
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "更新标签", notes = "需要管理员权限")
    public Result<Boolean> updateTag(@PathVariable Long id, @Validated @RequestBody TagDTO tagDTO) {
        tagDTO.setId(id);
        boolean result = tagService.updateTag(tagDTO);
        return Result.success(result);
    }

    /**
     * 删除标签
     * @param id 标签ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "删除标签", notes = "需要管理员权限")
    public Result<Boolean> deleteTag(@PathVariable Long id) {
        boolean result = tagService.deleteTag(id);
        return Result.success(result);
    }

    /**
     * 获取标签详情
     * @param id 标签ID
     * @return 标签详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取标签详情")
    public Result<TagVO> getTagDetail(@PathVariable Long id) {
        TagVO tagVO = tagService.getTagDetail(id);
        return Result.success(tagVO);
    }

    /**
     * 获取标签列表
     * @return 标签列表
     */
    @GetMapping
    @ApiOperation(value = "获取标签列表")
    public Result<List<TagVO>> getTagList() {
        List<TagVO> tagList = tagService.getTagList();
        return Result.success(tagList);
    }

    /**
     * 获取文章的标签列表
     * @param articleId 文章ID
     * @return 标签列表
     */
    @GetMapping("/article/{articleId}")
    @ApiOperation(value = "获取文章的标签列表")
    public Result<List<TagVO>> getTagsByArticleId(@PathVariable Long articleId) {
        List<TagVO> tagList = tagService.getTagsByArticleId(articleId);
        return Result.success(tagList);
    }

    /**
     * 获取热门标签
     * @param limit 限制数量，默认10
     * @return 热门标签列表
     */
    @GetMapping("/popular")
    @ApiOperation(value = "获取热门标签")
    public Result<List<TagVO>> getPopularTags(@RequestParam(defaultValue = "10") int limit) {
        List<TagVO> popularTags = tagService.getPopularTags(limit);
        return Result.success(popularTags);
    }
} 