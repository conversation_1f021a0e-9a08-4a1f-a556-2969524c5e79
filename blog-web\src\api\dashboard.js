import request from '@/utils/request'

// 获取仪表盘统计数据
export function getDashboardStats() {
  return request({
    url: '/dashboard/stats',
    method: 'get'
  })
}

// 获取最近文章列表
export function getRecentArticles(limit = 10) {
  return request({
    url: '/dashboard/recent-articles',
    method: 'get',
    params: { limit }
  })
}

// 获取最近评论列表
export function getRecentComments(limit = 10) {
  return request({
    url: '/dashboard/recent-comments',
    method: 'get',
    params: { limit }
  })
}

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}
