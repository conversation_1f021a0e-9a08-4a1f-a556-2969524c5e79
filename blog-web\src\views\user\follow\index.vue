<template>
  <div class="user-follow">
    <h1 class="page-title">我的关注</h1>
    
    <div class="tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="我关注的人" name="following">
          <div v-if="loadingFollowing" class="loading">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="followingList.length === 0" class="empty-list">
            <el-empty description="暂无关注的用户" />
          </div>
          <div v-else class="follow-list">
            <el-card v-for="user in followingList" :key="user.id" class="user-card" shadow="hover">
              <div class="user-info">
                <el-avatar :size="50" :src="user.avatar || '/img/default-avatar.png'" class="user-avatar"></el-avatar>
                <div class="user-details">
                  <router-link :to="`/user/${user.id}`" class="user-name">{{ user.nickname }}</router-link>
                  <div class="user-stats">
                    <span>关注: {{ user.followingCount }}</span>
                    <span>粉丝: {{ user.followerCount }}</span>
                  </div>
                  <div class="user-bio" v-if="user.bio">{{ user.bio }}</div>
                </div>
              </div>
              <div class="user-actions">
                <el-button 
                  size="small" 
                  type="primary"
                  @click="unfollowUser(user.id)"
                >取消关注</el-button>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="关注我的人" name="followers">
          <div v-if="loadingFollowers" class="loading">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="followerList.length === 0" class="empty-list">
            <el-empty description="暂无粉丝" />
          </div>
          <div v-else class="follow-list">
            <el-card v-for="user in followerList" :key="user.id" class="user-card" shadow="hover">
              <div class="user-info">
                <el-avatar :size="50" :src="user.avatar || '/img/default-avatar.png'" class="user-avatar"></el-avatar>
                <div class="user-details">
                  <router-link :to="`/user/${user.id}`" class="user-name">{{ user.nickname }}</router-link>
                  <div class="user-stats">
                    <span>关注: {{ user.followingCount }}</span>
                    <span>粉丝: {{ user.followerCount }}</span>
                  </div>
                  <div class="user-bio" v-if="user.bio">{{ user.bio }}</div>
                </div>
              </div>
              <div class="user-actions">
                <el-button 
                  size="small" 
                  :type="user.isFollowed ? 'primary' : 'default'"
                  @click="toggleFollow(user)"
                >{{ user.isFollowed ? '已关注' : '关注' }}</el-button>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMyFollowingList, getMyFollowerList, followUser, unfollowUser } from '@/api/follow'

export default {
  name: 'UserFollow',
  setup() {
    const router = useRouter()
    const activeTab = ref('following')
    const followingList = ref([])
    const followerList = ref([])
    const loadingFollowing = ref(true)
    const loadingFollowers = ref(true)
    
    // 获取关注列表
    const fetchFollowingList = async () => {
      loadingFollowing.value = true
      try {
        const res = await getMyFollowingList()
        if (res.code === 200) {
          followingList.value = res.data || []
        }
      } catch (error) {
        console.error('获取关注列表失败', error)
      } finally {
        loadingFollowing.value = false
      }
    }
    
    // 获取粉丝列表
    const fetchFollowerList = async () => {
      loadingFollowers.value = true
      try {
        const res = await getMyFollowerList()
        if (res.code === 200) {
          followerList.value = res.data || []
        }
      } catch (error) {
        console.error('获取粉丝列表失败', error)
      } finally {
        loadingFollowers.value = false
      }
    }
    
    // 取消关注用户
    const unfollowUser = (userId) => {
      ElMessageBox.confirm('确定要取消关注该用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await unfollowUser(userId)
          if (res.code === 200) {
            ElMessage.success('已取消关注')
            // 更新关注列表
            followingList.value = followingList.value.filter(user => user.id !== userId)
            // 更新粉丝列表中的关注状态
            const followerIndex = followerList.value.findIndex(user => user.id === userId)
            if (followerIndex > -1) {
              followerList.value[followerIndex].isFollowed = false
            }
          }
        } catch (error) {
          console.error('取消关注失败', error)
        }
      }).catch(() => {})
    }
    
    // 关注/取消关注用户
    const toggleFollow = async (user) => {
      try {
        if (user.isFollowed) {
          // 取消关注
          const res = await unfollowUser(user.id)
          if (res.code === 200) {
            user.isFollowed = false
            ElMessage.success('已取消关注')
          }
        } else {
          // 关注
          const res = await followUser(user.id)
          if (res.code === 200) {
            user.isFollowed = true
            ElMessage.success('关注成功')
          }
        }
      } catch (error) {
        console.error('操作失败', error)
      }
    }
    
    // 处理标签页切换
    const handleTabChange = (tab) => {
      if (tab.name === 'following' && followingList.value.length === 0 && !loadingFollowing.value) {
        fetchFollowingList()
      } else if (tab.name === 'followers' && followerList.value.length === 0 && !loadingFollowers.value) {
        fetchFollowerList()
      }
    }
    
    onMounted(() => {
      // 初始加载关注列表
      fetchFollowingList()
    })
    
    return {
      activeTab,
      followingList,
      followerList,
      loadingFollowing,
      loadingFollowers,
      handleTabChange,
      unfollowUser,
      toggleFollow
    }
  }
}
</script>

<style scoped>
.user-follow {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
}

.tabs {
  margin-top: 20px;
}

.loading, .empty-list {
  padding: 30px 0;
  text-align: center;
}

.follow-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-card {
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  margin-right: 15px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  margin-bottom: 5px;
  display: block;
}

.user-name:hover {
  color: #409EFF;
  text-decoration: underline;
}

.user-stats {
  font-size: 13px;
  color: #999;
  margin-bottom: 5px;
  display: flex;
  gap: 15px;
}

.user-bio {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  margin-left: 20px;
}
</style> 