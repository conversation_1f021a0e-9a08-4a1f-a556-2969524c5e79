package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.entity.SystemSetting;
import com.blog.service.ConfigService;
import com.blog.service.EmailService;
import com.blog.service.SystemSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统设置控制器
 */
@RestController
@RequestMapping("/system")
@Api(tags = "系统设置接口")
public class SystemSettingController {

    @Autowired
    private SystemSettingService systemSettingService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private EmailService emailService;

    /**
     * 获取所有系统设置
     * @return 系统设置Map
     */
    @GetMapping("/settings")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "获取所有系统设置", notes = "需要管理员权限")
    public Result<Map<String, String>> getAllSettings() {
        Map<String, String> settings = systemSettingService.getAllSettingsMap();
        return Result.success(settings);
    }

    /**
     * 根据分组获取设置
     * @param group 分组名
     * @return 设置列表
     */
    @GetMapping("/settings/{group}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "根据分组获取设置", notes = "需要管理员权限")
    public Result<List<SystemSetting>> getSettingsByGroup(@PathVariable String group) {
        List<SystemSetting> settings = systemSettingService.getSettingsByGroup(group);
        return Result.success(settings);
    }

    /**
     * 批量保存基本设置
     * @param settings 设置Map
     * @return 操作结果
     */
    @PostMapping("/settings/basic")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "保存基本设置", notes = "需要管理员权限")
    public Result<?> saveBasicSettings(@RequestBody Map<String, String> settings) {
        boolean success = systemSettingService.batchSetSettings(settings, "basic");
        if (success) {
            return Result.success();
        }
        return Result.failed("保存设置失败");
    }

    /**
     * 批量保存评论设置
     * @param settings 设置Map
     * @return 操作结果
     */
    @PostMapping("/settings/comment")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "保存评论设置", notes = "需要管理员权限")
    public Result<?> saveCommentSettings(@RequestBody Map<String, String> settings) {
        boolean success = systemSettingService.batchSetSettings(settings, "comment");
        if (success) {
            return Result.success();
        }
        return Result.failed("保存设置失败");
    }

    /**
     * 批量保存邮件设置
     * @param settings 设置Map
     * @return 操作结果
     */
    @PostMapping("/settings/email")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "保存邮件设置", notes = "需要管理员权限")
    public Result<?> saveEmailSettings(@RequestBody Map<String, String> settings) {
        boolean success = systemSettingService.batchSetSettings(settings, "email");
        if (success) {
            return Result.success();
        }
        return Result.failed("保存设置失败");
    }

    /**
     * 测试邮件设置
     * @return 测试结果
     */
    @PostMapping("/settings/email/test")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "测试邮件设置", notes = "需要管理员权限")
    public Result<?> testEmailSettings() {
        try {
            // 获取当前的邮件配置
            Map<String, String> emailConfig = configService.getEmailConfig();

            // 使用EmailService进行邮件测试
            String testResult = emailService.testEmailConfig(emailConfig);

            return Result.success(testResult);
        } catch (Exception e) {
            return Result.failed("邮件测试失败: " + e.getMessage());
        }
    }
}
