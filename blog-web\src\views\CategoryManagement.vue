<template>
  <div class="category-management-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>分类管理</span>
        </div>
      </template>

      <div class="toolbar">
        <el-button type="primary" :icon="Plus" @click="handleOpenDialog()">新增分类</el-button>
      </div>

      <el-table :data="categoryList" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" :icon="Edit" circle @click="handleOpenDialog(scope.row)" />
            <el-button type="danger" :icon="Delete" circle @click="handleDelete(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="30%"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="categoryForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getCategories, addCategory, updateCategory, deleteCategory } from '@/api/category';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';

// 加载状态
const loading = ref(true);

// 表格数据
const categoryList = ref([]);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');

// 表单相关
const formRef = ref(null);
const categoryForm = reactive({
  id: null,
  name: '',
});
const formRules = reactive({
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
});

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true;
  try {
    const res = await getCategories();
    categoryList.value = res.data;
  } catch (error) {
    console.error('获取分类列表失败', error);
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  fetchCategories();
});

// 重置表单
const resetForm = () => {
  categoryForm.id = null;
  categoryForm.name = '';
  formRef.value?.resetFields();
};

// 打开新增/编辑对话框
const handleOpenDialog = (category) => {
  resetForm();
  if (category && category.id) {
    // 编辑
    dialogTitle.value = '编辑分类';
    Object.assign(categoryForm, category);
  } else {
    // 新增
    dialogTitle.value = '新增分类';
  }
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (category) => {
  ElMessageBox.confirm(`确定要删除分类 "${category.name}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteCategory(category.id);
      ElMessage.success('删除成功');
      fetchCategories(); // 重新加载数据
    } catch (error) {
      console.error('删除失败', error);
      // 后端返回的业务异常信息会由request拦截器统一处理并提示
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 处理表单提交
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const isEdit = !!categoryForm.id;
      try {
        if (isEdit) {
          await updateCategory(categoryForm.id, categoryForm);
        } else {
          await addCategory(categoryForm);
        }
        ElMessage.success(isEdit ? '更新成功' : '新增成功');
        dialogVisible.value = false;
        fetchCategories();
      } catch (error) {
        console.error(isEdit ? '更新失败' : '新增失败', error);
        // 业务异常提示由request拦截器处理
      }
    }
  });
};
</script>

<style scoped>
.category-management-container {
  padding: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.toolbar {
  margin-bottom: 20px;
}
</style> 