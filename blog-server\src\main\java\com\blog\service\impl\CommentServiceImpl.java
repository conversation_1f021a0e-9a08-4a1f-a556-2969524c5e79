package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.exception.ConflictException;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.CommentDTO;
import com.blog.entity.Article;
import com.blog.entity.Comment;
import com.blog.entity.User;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.CommentMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.CommentService;
import com.blog.service.ConfigService;
import com.blog.service.NotificationService;
import com.blog.vo.CommentVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评论服务实现类
 */
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    @Resource
    private ArticleMapper articleMapper;
    
    @Resource
    private UserMapper userMapper;

    @Resource
    private NotificationService notificationService;

    @Autowired
    private ConfigService configService;

    /**
     * 创建评论
     *
     * @param commentDTO 评论信息
     * @param userId 当前登录用户ID（匿名评论时为null）
     * @return 评论ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createComment(CommentDTO commentDTO, Long userId) {
        // 验证文章是否存在
        Article article = articleMapper.selectById(commentDTO.getArticleId());
        if (article == null) {
            throw new NotFoundException("文章不存在");
        }

        // 验证文章是否允许评论
        if (article.getAllowComment() != null && article.getAllowComment() == 0) {
            throw new ConflictException("该文章不允许评论");
        }

        // 判断是否为匿名评论
        boolean isAnonymous = userId == null;

        // 如果是匿名评论，检查是否允许匿名评论
        if (isAnonymous) {
            String allowAnonymous = configService.getValueByKey("allow_anonymous_comment");
            if (!"true".equals(allowAnonymous)) {
                throw new ConflictException("系统不允许匿名评论，请先登录");
            }

            // 验证匿名用户信息
            if (commentDTO.getAnonymousNickname() == null || commentDTO.getAnonymousNickname().trim().isEmpty()) {
                throw new ConflictException("匿名评论必须填写昵称");
            }
            if (commentDTO.getAnonymousEmail() == null || commentDTO.getAnonymousEmail().trim().isEmpty()) {
                throw new ConflictException("匿名评论必须填写邮箱");
            }
        }
        
        // 如果是回复评论，验证父评论是否存在
        if (commentDTO.getParentId() != null) {
            Comment parentComment = this.getById(commentDTO.getParentId());
            if (parentComment == null) {
                throw new NotFoundException("回复的评论不存在");
            }
            
            // 验证父评论是否属于同一篇文章
            if (!parentComment.getArticleId().equals(commentDTO.getArticleId())) {
                throw new ConflictException("不能跨文章回复评论");
            }
            
            // 验证回复用户是否存在
            if (commentDTO.getToUserId() != null) {
                User toUser = userMapper.selectById(commentDTO.getToUserId());
                if (toUser == null) {
                    throw new NotFoundException("回复的用户不存在");
                }
            } else {
                // 如果没有明确指定回复用户，则默认回复父评论的作者
                commentDTO.setToUserId(parentComment.getUserId());
            }
        }
        
        // 创建评论实体
        Comment comment = new Comment();
        comment.setContent(commentDTO.getContent());
        comment.setArticleId(commentDTO.getArticleId());
        comment.setUserId(userId);
        comment.setParentId(commentDTO.getParentId());
        comment.setToUserId(commentDTO.getToUserId());

        // 设置匿名评论相关字段
        if (isAnonymous) {
            comment.setIsAnonymous(1);
            comment.setAnonymousNickname(commentDTO.getAnonymousNickname().trim());
            comment.setAnonymousEmail(commentDTO.getAnonymousEmail().trim());
        } else {
            comment.setIsAnonymous(0);
        }

        // 根据系统设置决定评论状态
        String commentAudit = configService.getValueByKey("comment_audit");
        if ("true".equals(commentAudit)) {
            comment.setStatus(0); // 需要审核，状态为待审核
        } else {
            comment.setStatus(1); // 不需要审核，状态为已发布
        }
        
        // 保存评论
        this.save(comment);
        
        // 只有评论状态为已发布(1)时才更新文章评论数
        if (comment.getStatus() == 1) {
            article.setCommentCount(article.getCommentCount() + 1);
            articleMapper.updateById(article);

            // 发送通知
            if (commentDTO.getParentId() != null && commentDTO.getParentId() > 0) {
                // 这是回复评论，发送回复通知
                notificationService.sendReplyNotification(commentDTO.getParentId(), comment.getId(), userId);
            } else {
                // 这是直接评论文章，发送评论通知
                notificationService.sendCommentNotification(commentDTO.getArticleId(), comment.getId(), userId);
            }
        }

        return comment.getId();
    }
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 当前登录用户ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Long commentId, Long userId) {
        // 查询评论
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new NotFoundException("评论不存在");
        }
        
        // 验证权限：只有评论作者或管理员可以删除评论
        User user = userMapper.selectById(userId);
        boolean isAdmin = user != null && "admin".equalsIgnoreCase(user.getRole());
        if (!comment.getUserId().equals(userId) && !isAdmin) {
            throw new BusinessException("没有权限删除该评论");
        }
        
        // 查询是否有子评论
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getParentId, commentId);
        long childCount = this.count(queryWrapper);
        
        // 如果有子评论，则不允许删除（或者进行级联删除，根据业务需要）
        if (childCount > 0 && !isAdmin) {
            throw new ConflictException("该评论有回复，无法删除");
        }

        // 检查该评论是否是某条评论的回复（即有父评论）
        if (comment.getParentId() != null && comment.getParentId() > 0) {
            // 检查是否有其他评论引用了这条评论（即回复了这条评论）
            LambdaQueryWrapper<Comment> replyCheckWrapper = new LambdaQueryWrapper<>();
            replyCheckWrapper.eq(Comment::getParentId, comment.getParentId())  // 同一父评论下
                            .eq(Comment::getToUserId, comment.getUserId())  // 回复目标是当前评论的用户
                            .ne(Comment::getId, commentId);  // 排除自身
            
            long replyCount = this.count(replyCheckWrapper);
            
            // 如果存在针对这条评论的回复且不是管理员，则不允许删除
            if (replyCount > 0 && !isAdmin) {
                throw new ConflictException("该评论已被其他用户回复，无法删除");
            }
        }
        
        // 删除本条评论及其所有子评论（如果是管理员）
        boolean success;
        if (isAdmin && childCount > 0) {
            // 管理员删除评论及其所有子评论
            LambdaQueryWrapper<Comment> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(Comment::getId, commentId)
                    .or()
                    .eq(Comment::getParentId, commentId);
            
            // 计算已发布状态评论的数量，只有这些才会影响文章评论计数
            long publishedCount = 0;
            if (comment.getStatus() == 1) {
                publishedCount++; // 计入当前评论
            }
            
            // 查询所有子评论，计算已发布状态的子评论数
            LambdaQueryWrapper<Comment> publishedQuery = new LambdaQueryWrapper<>();
            publishedQuery.eq(Comment::getParentId, commentId).eq(Comment::getStatus, 1);
            long publishedChildCount = this.count(publishedQuery);
            publishedCount += publishedChildCount;
            
            success = this.remove(deleteWrapper);
            
            // 更新文章评论数，仅计算已发布状态的评论
            if (publishedCount > 0) {
                Article article = articleMapper.selectById(comment.getArticleId());
                if (article != null) {
                    int newCommentCount = Math.max(0, article.getCommentCount() - (int)(publishedCount));
                    article.setCommentCount(newCommentCount);
                    articleMapper.updateById(article);
                }
            }
        } else {
            // 普通用户或无子评论的管理员，只删除当前评论
            success = this.removeById(commentId);
            
            // 只有删除已发布状态的评论才需要更新计数
            if (comment.getStatus() == 1) {
                Article article = articleMapper.selectById(comment.getArticleId());
                if (article != null) {
                    int newCommentCount = Math.max(0, article.getCommentCount() - 1);
                    article.setCommentCount(newCommentCount);
                    articleMapper.updateById(article);
                }
            }
        }
        
        return success;
    }
    
    /**
     * 获取文章评论列表（树形结构）
     * 
     * @param articleId 文章ID
     * @return 评论树形列表
     */
    @Override
    public List<CommentVO> getArticleComments(Long articleId) {
        // 验证文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            throw new NotFoundException("文章不存在");
        }
        
        // 查询所有该文章的评论
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getArticleId, articleId)
                .eq(Comment::getStatus, 1) // 只查询已发布的评论
                .orderByDesc(Comment::getCreateTime);
        List<Comment> comments = this.list(queryWrapper);
        
        if (comments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取所有评论用户ID（排除匿名评论）
        List<Long> userIds = comments.stream()
                .filter(comment -> comment.getUserId() != null) // 排除匿名评论
                .map(Comment::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 获取所有回复用户ID（排除匿名评论）
        List<Long> toUserIds = comments.stream()
                .filter(comment -> comment.getToUserId() != null)
                .map(Comment::getToUserId)
                .distinct()
                .collect(Collectors.toList());

        // 合并用户ID列表并去重
        userIds.addAll(toUserIds);
        userIds = userIds.stream().distinct().collect(Collectors.toList());
        
        // 批量查询用户信息
        Map<Long, User> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
            userQueryWrapper.in(User::getId, userIds);
            List<User> users = userMapper.selectList(userQueryWrapper);

            // 用户信息映射，方便后续快速查找
            for (User user : users) {
                userMap.put(user.getId(), user);
            }
        }
        
        // 将评论转换为VO
        List<CommentVO> commentVOList = comments.stream().map(comment -> {
            CommentVO vo = new CommentVO();
            BeanUtils.copyProperties(comment, vo);
            
            // 设置评论用户信息
            if (comment.getIsAnonymous() != null && comment.getIsAnonymous() == 1) {
                // 匿名评论
                vo.setNickname(comment.getAnonymousNickname());
                vo.setAvatar(null); // 匿名用户没有头像
                vo.setUsername(null);
            } else {
                // 注册用户评论
                User user = userMap.get(comment.getUserId());
                if (user != null) {
                    vo.setUsername(user.getUsername());
                    vo.setNickname(user.getNickname());
                    vo.setAvatar(user.getAvatar());
                }
            }
            
            // 设置回复用户信息
            if (comment.getToUserId() != null) {
                User toUser = userMap.get(comment.getToUserId());
                if (toUser != null) {
                    vo.setToNickname(toUser.getNickname());
                }
            }
            
            // 设置文章标题
            vo.setArticleTitle(article.getTitle());
            vo.setArticleDeleted(false);

            // 默认设置空的子评论列表
            vo.setChildren(new ArrayList<>());
            return vo;
        }).collect(Collectors.toList());
        
        // 构建评论树
        List<CommentVO> rootComments = new ArrayList<>();
        Map<Long, CommentVO> commentMap = new HashMap<>();
        
        // 先将所有评论放入map
        for (CommentVO comment : commentVOList) {
            commentMap.put(comment.getId(), comment);
        }
        
        // 构建树形结构
        for (CommentVO comment : commentVOList) {
            if (comment.getParentId() == null) {
                // 根评论
                rootComments.add(comment);
            } else {
                // 子评论
                CommentVO parentComment = commentMap.get(comment.getParentId());
                if (parentComment != null) {
                    parentComment.getChildren().add(comment);
                }
            }
        }
        
        return rootComments;
    }

    /**
     * 获取用户评论列表
     * @param userId 用户ID
     * @return 评论列表
     */
    @Override
    public List<CommentVO> getUserComments(Long userId) {
        // 1. 根据用户ID查询所有评论
        LambdaQueryWrapper<Comment> commentQuery = new LambdaQueryWrapper<>();
        commentQuery.eq(Comment::getUserId, userId).orderByDesc(Comment::getCreateTime);
        List<Comment> comments = this.list(commentQuery);

        if (comments.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 提取所有关联的文章ID
        List<Long> articleIds = comments.stream()
                .map(Comment::getArticleId)
                .distinct()
                .collect(Collectors.toList());

        // 3. 批量查询文章信息
        Map<Long, Article> articleMap = new HashMap<>();
        if (!articleIds.isEmpty()) {
            LambdaQueryWrapper<Article> articleQuery = new LambdaQueryWrapper<>();
            articleQuery.in(Article::getId, articleIds);
            List<Article> articles = articleMapper.selectList(articleQuery);
            for (Article article : articles) {
                articleMap.put(article.getId(), article);
            }
        }
        
        // 批量查询用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }

        // 4. 组装CommentVO列表
        return comments.stream().map(comment -> {
            CommentVO vo = new CommentVO();
            BeanUtils.copyProperties(comment, vo);
            
            // 设置用户信息
            vo.setUsername(user.getUsername());
            vo.setNickname(user.getNickname());

            // 5. 填充文章信息
            Article article = articleMap.get(comment.getArticleId());
            if (article != null) {
                vo.setArticleTitle(article.getTitle());
                vo.setArticleDeleted(false);
            } else {
                vo.setArticleTitle("(文章已删除)");
                vo.setArticleDeleted(true);
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 更新评论状态（审核/取消审核）
     * 
     * @param commentId 评论ID
     * @param status 状态值
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommentStatus(Long commentId, Integer status) {
        // 查询评论
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new NotFoundException("评论不存在");
        }
        
        // 获取原始状态
        int oldStatus = comment.getStatus();
        
        // 更新评论状态
        comment.setStatus(status);
        boolean success = this.updateById(comment);
        
        // 更新文章评论计数
        if (success && oldStatus != status) {
            Article article = articleMapper.selectById(comment.getArticleId());
            if (article != null) {
                // 如果由未审核变为已发布，则增加评论计数
                if (oldStatus == 0 && status == 1) {
                    article.setCommentCount(article.getCommentCount() + 1);
                    articleMapper.updateById(article);
                } 
                // 如果由已发布变为未审核，则减少评论计数
                else if (oldStatus == 1 && status == 0) {
                    article.setCommentCount(Math.max(0, article.getCommentCount() - 1));
                    articleMapper.updateById(article);
                }
            }
        }
        
        return success;
    }
    
    @Override
    public List<CommentVO> getAllComments() {
        List<Comment> comments = this.list(new LambdaQueryWrapper<Comment>().orderByDesc(Comment::getCreateTime));
        if (comments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取所有评论用户ID
        List<Long> userIds = comments.stream()
                .map(Comment::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        // 获取所有文章ID
        List<Long> articleIds = comments.stream()
                .map(Comment::getArticleId)
                .distinct()
                .collect(Collectors.toList());
        
        // 批量查询用户信息
        Map<Long, User> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
            userQueryWrapper.in(User::getId, userIds);
            List<User> users = userMapper.selectList(userQueryWrapper);
            for (User user : users) {
                userMap.put(user.getId(), user);
            }
        }
        
        // 批量查询文章信息
        Map<Long, Article> articleMap = new HashMap<>();
        if (!articleIds.isEmpty()) {
            LambdaQueryWrapper<Article> articleQueryWrapper = new LambdaQueryWrapper<>();
            articleQueryWrapper.in(Article::getId, articleIds);
            List<Article> articles = articleMapper.selectList(articleQueryWrapper);
            for (Article article : articles) {
                articleMap.put(article.getId(), article);
            }
        }
        
        // 转换为VO
        List<CommentVO> commentVOList = new ArrayList<>();
        for (Comment comment : comments) {
            CommentVO vo = new CommentVO();
            BeanUtils.copyProperties(comment, vo);
            
            // 设置用户信息
            User user = userMap.get(comment.getUserId());
            if (user != null) {
                vo.setUsername(user.getUsername());
                vo.setNickname(user.getNickname());
                vo.setAvatar(user.getAvatar());
            }
            
            // 添加文章信息
            Article article = articleMap.get(comment.getArticleId());
            if (article != null) {
                vo.setArticleTitle(article.getTitle());
                vo.setArticleDeleted(false);
            } else {
                vo.setArticleTitle("(文章已删除)");
                vo.setArticleDeleted(true);
            }
            
            // 设置子评论列表（默认为空）
            vo.setChildren(new ArrayList<>());
            
            commentVOList.add(vo);
        }
        
        return commentVOList;
    }

    /**
     * 将评论实体转换为VO
     *
     * @param comment 评论实体
     * @return 评论VO
     */
    @Override
    public CommentVO convertToVO(Comment comment) {
        if (comment == null) {
            return null;
        }

        CommentVO vo = new CommentVO();
        BeanUtils.copyProperties(comment, vo);

        // 设置用户信息
        User user = userMapper.selectById(comment.getUserId());
        if (user != null) {
            vo.setUsername(user.getUsername());
            vo.setNickname(user.getNickname());
            vo.setAvatar(user.getAvatar());
        } else {
            vo.setUsername("(用户已删除)");
            vo.setNickname("(用户已删除)");
        }

        // 设置回复用户信息
        if (comment.getToUserId() != null) {
            User toUser = userMapper.selectById(comment.getToUserId());
            if (toUser != null) {
                vo.setToNickname(toUser.getNickname());
            } else {
                vo.setToNickname("(用户已删除)");
            }
        }

        // 设置文章信息
        Article article = articleMapper.selectById(comment.getArticleId());
        if (article != null) {
            vo.setArticleTitle(article.getTitle());
            vo.setArticleDeleted(false);
        } else {
            vo.setArticleTitle("(文章已删除)");
            vo.setArticleDeleted(true);
        }

        // 设置子评论列表（默认为空）
        vo.setChildren(new ArrayList<>());

        return vo;
    }
}