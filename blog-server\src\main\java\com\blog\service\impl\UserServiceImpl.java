package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.utils.JwtTokenUtil;
import com.blog.entity.User;
import com.blog.mapper.UserMapper;
import com.blog.service.UserService;
import com.blog.dto.PasswordUpdateDTO;
import com.blog.common.exception.BadRequestException;
import com.blog.common.exception.ConflictException;
import com.blog.common.exception.NotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private PasswordEncoder passwordEncoder;
    
    /**
     * 懒加载PasswordEncoder，避免循环依赖
     */
    private PasswordEncoder getPasswordEncoder() {
        if (passwordEncoder == null) {
            passwordEncoder = applicationContext.getBean(PasswordEncoder.class);
        }
        return passwordEncoder;
    }

    @Override
    public boolean register(User user) {
        // 查询用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, user.getUsername());
        if (this.baseMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException("用户名已存在");
        }
        
        // 设置默认值
        user.setPassword(getPasswordEncoder().encode(user.getPassword()));
        user.setRole("user");
        user.setStatus(1);
        
        // 保存用户
        return this.save(user);
    }

    @Override
    public String login(String username, String password) {
        String token = null;
        try {
            // 查询用户
            User user = getUserByUsername(username);
            if (user == null) {
                throw new BadRequestException("用户名不存在");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                throw new BadRequestException("账号已被禁用，请联系管理员");
            }

            // 验证密码
            if (!getPasswordEncoder().matches(password, user.getPassword())) {
                throw new BadRequestException("密码不正确");
            }
            
            // 创建用户详情
            org.springframework.security.core.userdetails.User userDetails = 
                new org.springframework.security.core.userdetails.User(
                    user.getUsername(), 
                    user.getPassword(), 
                    java.util.Collections.singletonList(
                        new org.springframework.security.core.authority.SimpleGrantedAuthority("ROLE_" + user.getRole().toUpperCase())
                    )
                );
            
            // 生成Authentication对象并设置到SecurityContext
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                    userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 生成token
            token = jwtTokenUtil.generateToken(userDetails);
        } catch (AuthenticationException e) {
            throw new BadRequestException("用户名或密码错误");
        }
        return token;
    }

    @Override
    public User getUserByUsername(String username) {
        return baseMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, username));
    }
    
    @Override
    public boolean updatePassword(Long userId, PasswordUpdateDTO passwordUpdateDTO) {
        // 验证新密码和确认密码是否一致
        if (!passwordUpdateDTO.getNewPassword().equals(passwordUpdateDTO.getConfirmPassword())) {
            throw new BadRequestException("新密码与确认密码不一致");
        }
        
        // 获取当前用户
        User user = this.getById(userId);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 验证当前密码是否正确
        if (!getPasswordEncoder().matches(passwordUpdateDTO.getCurrentPassword(), user.getPassword())) {
            throw new BadRequestException("当前密码不正确");
        }
        
        // 更新密码
        user.setPassword(getPasswordEncoder().encode(passwordUpdateDTO.getNewPassword()));
        // 更新时间
        user.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(user);
    }
}