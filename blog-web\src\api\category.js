import request from '@/utils/request';

/**
 * 获取所有分类列表
 * @returns {Promise}
 */
export function getCategories() {
    return request({
        url: '/categories',
        method: 'get'
    });
}

/**
 * 新增一个分类
 * @param {object} data - 分类信息，例如 { name: '新的分类' }
 * @returns {Promise}
 */
export function addCategory(data) {
    return request({
        url: '/categories',
        method: 'post',
        data
    });
}

/**
 * 更新一个分类
 * @param {number} id - 分类ID
 * @param {object} data - 更新的分类信息，例如 { id: 1, name: '更新后的分类' }
 * @returns {Promise}
 */
export function updateCategory(id, data) {
    return request({
        url: `/categories/${id}`,
        method: 'put',
        data
    });
}

/**
 * 删除一个分类
 * @param {number} id - 分类ID
 * @returns {Promise}
 */
export function deleteCategory(id) {
    return request({
        url: `/categories/${id}`,
        method: 'delete'
    });
} 