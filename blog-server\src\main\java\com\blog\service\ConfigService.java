package com.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.entity.Config;

import java.util.Map;

/**
 * 系统配置服务接口
 */
public interface ConfigService extends IService<Config> {

    /**
     * 根据配置键获取配置值
     * @param configKey 配置键
     * @return 配置值
     */
    String getValueByKey(String configKey);

    /**
     * 根据配置键更新配置值
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否更新成功
     */
    boolean updateValueByKey(String configKey, String configValue);

    /**
     * 获取所有配置（以Map形式返回）
     * @return 配置Map，key为配置键，value为配置值
     */
    Map<String, String> getAllConfigs();

    /**
     * 批量更新配置
     * @param configs 配置Map
     * @return 是否更新成功
     */
    boolean batchUpdateConfigs(Map<String, String> configs);

    /**
     * 获取网站基本信息配置
     * @return 网站基本信息Map
     */
    Map<String, String> getSiteInfo();

    /**
     * 获取评论相关配置
     * @return 评论配置Map
     */
    Map<String, String> getCommentConfig();

    /**
     * 获取邮件相关配置
     * @return 邮件配置Map
     */
    Map<String, String> getEmailConfig();
}
