package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.service.DashboardService;
import com.blog.vo.ArticleVO;
import com.blog.vo.CommentVO;
import com.blog.vo.DashboardStatsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仪表盘控制器
 */
@Slf4j
@RestController
@RequestMapping("/dashboard")
@Api(tags = "仪表盘管理接口")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * 获取仪表盘统计数据
     * @return 统计数据
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "获取仪表盘统计数据", notes = "需要管理员权限")
    public Result<DashboardStatsVO> getDashboardStats() {
        try {
            DashboardStatsVO stats = dashboardService.getDashboardStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            return Result.failed("获取统计数据失败");
        }
    }

    /**
     * 获取最近文章列表
     * @param limit 限制数量，默认10条
     * @return 最近文章列表
     */
    @GetMapping("/recent-articles")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "获取最近文章列表", notes = "需要管理员权限")
    public Result<List<ArticleVO>> getRecentArticles(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 限制查询数量，防止数据过多
            if (limit > 50) {
                limit = 50;
            }
            List<ArticleVO> articles = dashboardService.getRecentArticles(limit);
            return Result.success(articles);
        } catch (Exception e) {
            log.error("获取最近文章列表失败", e);
            return Result.failed("获取最近文章失败");
        }
    }

    /**
     * 获取最近评论列表
     * @param limit 限制数量，默认10条
     * @return 最近评论列表
     */
    @GetMapping("/recent-comments")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "获取最近评论列表", notes = "需要管理员权限")
    public Result<List<CommentVO>> getRecentComments(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 限制查询数量，防止数据过多
            if (limit > 50) {
                limit = 50;
            }
            List<CommentVO> comments = dashboardService.getRecentComments(limit);
            return Result.success(comments);
        } catch (Exception e) {
            log.error("获取最近评论列表失败", e);
            return Result.failed("获取最近评论失败");
        }
    }

    /**
     * 获取仪表盘完整数据（包含统计数据、最近文章、最近评论）
     * @return 仪表盘完整数据
     */
    @GetMapping("/overview")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "获取仪表盘概览数据", notes = "需要管理员权限")
    public Result<Map<String, Object>> getDashboardOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // 获取统计数据
            DashboardStatsVO stats = dashboardService.getDashboardStats();
            overview.put("stats", stats);
            
            // 获取最近文章（限制5条）
            List<ArticleVO> recentArticles = dashboardService.getRecentArticles(5);
            overview.put("recentArticles", recentArticles);
            
            // 获取最近评论（限制5条）
            List<CommentVO> recentComments = dashboardService.getRecentComments(5);
            overview.put("recentComments", recentComments);
            
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取仪表盘概览数据失败", e);
            return Result.failed("获取概览数据失败");
        }
    }
}
