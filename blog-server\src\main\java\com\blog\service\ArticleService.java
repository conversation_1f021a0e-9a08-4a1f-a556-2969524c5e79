package com.blog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.dto.ArticleDTO;
import com.blog.entity.Article;
import com.blog.vo.ArticleVO;

import java.util.List;
import java.util.Map;

/**
 * 文章服务接口
 */
public interface ArticleService extends IService<Article> {

    /**
     * 创建文章
     * @param articleDTO 文章DTO
     * @return 文章ID
     */
    Long createArticle(ArticleDTO articleDTO);

    /**
     * 更新文章
     * @param articleDTO 文章DTO
     * @return 是否更新成功
     */
    boolean updateArticle(ArticleDTO articleDTO);

    /**
     * 删除文章
     * @param id 文章ID
     * @return 是否删除成功
     */
    boolean deleteArticle(Long id);

    /**
     * 获取文章详情
     * @param id 文章ID
     * @return 文章VO
     */
    ArticleVO getArticleDetail(Long id);

    /**
     * 分页获取文章列表
     * @param current 当前页
     * @param size 每页大小
     * @param keyword 搜索关键字
     * @param categoryId 分类ID
     * @param tagId 标签ID
     * @param status 文章状态
     * @return 文章VO分页对象
     */
    IPage<ArticleVO> getArticleList(Integer current, Integer size, String keyword, Long categoryId, Long tagId, Integer status);

    /**
     * 根据用户名获取文章列表
     * @param current 当前页
     * @param size 每页大小
     * @param username 用户名
     * @param status 文章状态
     * @return 文章VO分页对象
     */
    IPage<ArticleVO> getArticleListByUsername(Integer current, Integer size, String username, Integer status);

    /**
     * 获取推荐文章列表
     * @param limit 限制数量
     * @return 文章VO列表
     */
    List<ArticleVO> getRecommendArticleList(Integer limit);

    /**
     * 获取热门文章列表
     * @param limit 限制数量
     * @return 文章VO列表
     */
    List<ArticleVO> getPopularArticleList(Integer limit);

    /**
     * 获取文章归档数据（按年月分组）
     * @return 归档数据Map，格式：{年份: {月份: [文章列表]}}
     */
    Map<String, Map<String, List<ArticleVO>>> getArchiveData();
}