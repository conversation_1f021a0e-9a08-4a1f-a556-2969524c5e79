<template>
  <div class="user-collections">
    <h1 class="page-title">我的收藏</h1>
    
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="collections.length === 0" class="empty-collections">
      <el-empty description="暂无收藏的文章" />
    </div>
    
    <div v-else class="collection-list">
      <el-card v-for="article in paginatedCollections" :key="article.id" class="article-card" shadow="hover">
        <div class="article-header">
          <div class="article-title">
            <router-link :to="`/article/${article.id}`" class="title-link">
              {{ article.title || `文章${article.id}` }}
            </router-link>
          </div>
          
          <div class="article-meta">
            <div class="author-info" v-if="article.author">
              <i class="el-icon-user"></i> 
              <span class="meta-label">作者：</span>
              <span class="meta-value">{{ article.author }}</span>
            </div>
            
            <div class="stats-info">
              <span class="meta-item">
                <i class="el-icon-view"></i> 
                <span class="meta-label">浏览：</span>
                <span class="meta-value">{{ article.viewCount || 0 }}</span>
              </span>
              
              <span class="meta-item">
                <i class="el-icon-thumb"></i> 
                <span class="meta-label">点赞：</span>
                <span class="meta-value">{{ article.likeCount || 0 }}</span>
              </span>
              
              <span class="meta-item">
                <i class="el-icon-chat-dot-round"></i> 
                <span class="meta-label">评论：</span>
                <span class="meta-value">{{ article.commentCount || 0 }}</span>
              </span>
              
              <span class="meta-item">
                <i class="el-icon-date"></i> 
                <span class="meta-label">发布于：</span>
                <span class="meta-value">{{ formatDate(article.createTime) }}</span>
              </span>
            </div>
          </div>
        </div>
        
        <div class="article-content">
          <div v-if="article.summary" class="article-summary">
            {{ article.summary }}
          </div>
          <div v-else class="article-summary">
            {{ extractSummary(article.content) }}
          </div>
          
          <div class="article-category" v-if="article.categoryName">
            <span class="category-label">分类：</span>
            <el-tag size="small" type="success">{{ article.categoryName }}</el-tag>
          </div>
        </div>
        
        <div class="article-actions">
          <router-link :to="`/article/${article.id}`" class="action-btn">
            <el-button size="small" type="primary">阅读全文</el-button>
          </router-link>
          <el-button size="small" type="danger" @click="uncollectArticle(article.id)">取消收藏</el-button>
        </div>
      </el-card>
    </div>
    
    <div class="pagination-container" v-if="collections.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        @current-change="handlePageChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserCollections } from '@/api/user'
import { cancelInteraction } from '@/api/interaction'

export default {
  name: 'UserCollection',
  setup() {
    const loading = ref(true)
    const collections = ref([])
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    // 计算当前页的收藏列表
    const paginatedCollections = computed(() => {
      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      return collections.value.slice(startIndex, endIndex)
    })
    
    // 获取用户收藏列表
    const fetchCollections = async () => {
      loading.value = true
      try {
        const res = await getUserCollections()
        if (res.code === 200) {
          collections.value = res.data || []
          total.value = collections.value.length
          console.log('收藏列表数据:', JSON.stringify(collections.value))
        }
      } catch (error) {
        console.error('获取收藏列表失败', error)
      } finally {
        loading.value = false
      }
    }
    
    // 处理页码变化
    const handlePageChange = (page) => {
      currentPage.value = page
      // 不需要重新获取数据，只需要更新当前页码
    }
    
    // 取消收藏
    const uncollectArticle = (articleId) => {
      ElMessageBox.confirm('确定要取消收藏这篇文章吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await cancelInteraction({ articleId, type: 'collect' })
          if (res.code === 200) {
            ElMessage.success('已取消收藏')
            // 从列表中移除
            collections.value = collections.value.filter(item => item.id !== articleId)
            // 更新总数
            total.value = collections.value.length
            
            // 处理当前页变更（如果当前页没有数据了，就回到上一页）
            if (paginatedCollections.value.length === 0 && currentPage.value > 1) {
              currentPage.value -= 1
            }
          }
        } catch (error) {
          console.error('取消收藏失败', error)
        }
      }).catch(() => {})
    }
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    // 从文章内容中提取摘要
    const extractSummary = (content) => {
      if (!content) return '暂无摘要';
      
      // 移除HTML标签
      const plainText = content.replace(/<[^>]+>/g, '');
      
      // 提取前100个字符作为摘要
      return plainText.length > 100 
        ? plainText.substring(0, 100) + '...' 
        : plainText;
    }
    
    onMounted(() => {
      fetchCollections()
    })
    
    return {
      loading,
      collections,
      paginatedCollections,
      currentPage,
      pageSize,
      total,
      uncollectArticle,
      formatDate,
      extractSummary,
      handlePageChange
    }
  }
}
</script>

<style scoped>
.user-collections {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.loading, .empty-collections {
  padding: 30px 0;
  text-align: center;
}

.collection-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.article-card {
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.article-header {
  margin-bottom: 15px;
}

.article-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.title-link {
  color: #333;
  text-decoration: none;
}

.title-link:hover {
  color: #409EFF;
  text-decoration: underline;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 13px;
  color: #666;
  margin-bottom: 12px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}

.stats-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.meta-label {
  color: #999;
}

.meta-value {
  color: #666;
  font-weight: 500;
}

.article-content {
  margin-bottom: 15px;
}

.article-summary {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
}

.article-category {
  margin-top: 10px;
}

.category-label {
  margin-right: 5px;
  color: #999;
}

.article-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  border-top: 1px dashed #eee;
  padding-top: 15px;
}

.action-btn {
  text-decoration: none;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 