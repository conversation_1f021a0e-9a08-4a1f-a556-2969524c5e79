package com.blog.enums;

import lombok.Getter;

/**
 * 资源类型枚举
 */
@Getter
public enum ResourceType {
    ARTICLE("article", "文章"),
    COMMENT("comment", "评论"),
    USER("user", "用户");

    private final String code;
    private final String description;

    ResourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static ResourceType fromCode(String code) {
        for (ResourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
