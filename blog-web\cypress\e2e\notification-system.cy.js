/**
 * 第五阶段：消息通知系统 E2E 测试
 */

describe('消息通知系统测试', () => {
  let testUser1, testUser2, adminUser
  let testArticleId

  before(() => {
    // 测试用户数据
    testUser1 = {
      username: 'testuser1',
      password: 'password123',
      nickname: '测试用户1',
      email: '<EMAIL>'
    }
    
    testUser2 = {
      username: 'testuser2', 
      password: 'password123',
      nickname: '测试用户2',
      email: '<EMAIL>'
    }

    adminUser = {
      username: 'admin',
      password: 'admin123'
    }
  })

  beforeEach(() => {
    // 清理通知数据
    cy.task('clearNotifications')
  })

  describe('通知发送功能', () => {
    it('应该在用户点赞文章时发送通知', () => {
      // 1. 用户1登录并发布文章
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/admin/article/add')
      
      cy.get('[data-cy="article-title"]').type('测试文章标题')
      cy.get('[data-cy="article-content"]').type('这是一篇测试文章的内容')
      cy.get('[data-cy="publish-btn"]').click()
      
      // 获取文章ID
      cy.url().should('include', '/article/')
      cy.url().then((url) => {
        testArticleId = url.split('/article/')[1]
      })
      
      // 2. 用户2登录并点赞文章
      cy.loginAs(testUser2.username, testUser2.password)
      cy.visit(`/article/${testArticleId}`)
      
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="like-btn"]').should('contain', '已点赞')
      
      // 3. 切换到用户1，检查通知
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/')
      
      // 检查导航栏通知图标
      cy.get('[data-cy="notification-icon"]').should('be.visible')
      cy.get('[data-cy="notification-badge"]').should('contain', '1')
      
      // 点击进入消息中心
      cy.get('[data-cy="notification-icon"]').click()
      cy.url().should('include', '/user/notifications')
      
      // 验证通知内容
      cy.get('[data-cy="notification-item"]').should('have.length', 1)
      cy.get('[data-cy="notification-title"]').should('contain', '测试用户2 点赞了你的文章')
      cy.get('[data-cy="notification-content"]').should('contain', '测试文章标题')
    })

    it('应该在用户收藏文章时发送通知', () => {
      // 1. 用户2收藏用户1的文章
      cy.loginAs(testUser2.username, testUser2.password)
      cy.visit(`/article/${testArticleId}`)
      
      cy.get('[data-cy="collect-btn"]').click()
      cy.get('[data-cy="collect-btn"]').should('contain', '已收藏')
      
      // 2. 用户1检查收藏通知
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      cy.get('[data-cy="notification-item"]').should('contain', '收藏了你的文章')
    })

    it('应该在用户评论文章时发送通知', () => {
      // 1. 用户2评论用户1的文章
      cy.loginAs(testUser2.username, testUser2.password)
      cy.visit(`/article/${testArticleId}`)
      
      cy.get('[data-cy="comment-input"]').type('这是一条测试评论')
      cy.get('[data-cy="comment-submit"]').click()
      
      // 2. 用户1检查评论通知
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      cy.get('[data-cy="notification-item"]').should('contain', '评论了你的文章')
    })

    it('应该在用户关注时发送通知', () => {
      // 1. 用户2关注用户1
      cy.loginAs(testUser2.username, testUser2.password)
      cy.visit(`/user/${testUser1.username}`)
      
      cy.get('[data-cy="follow-btn"]').click()
      cy.get('[data-cy="follow-btn"]').should('contain', '已关注')
      
      // 2. 用户1检查关注通知
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      cy.get('[data-cy="notification-item"]').should('contain', '关注了你')
    })

    it('应该防止重复通知', () => {
      // 1. 用户2多次点赞同一篇文章
      cy.loginAs(testUser2.username, testUser2.password)
      cy.visit(`/article/${testArticleId}`)
      
      // 点赞 -> 取消 -> 点赞 -> 取消 -> 点赞
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="like-btn"]').click()
      
      // 2. 用户1应该只收到一条通知
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      cy.get('[data-cy="notification-item"]')
        .filter(':contains("点赞了你的文章")')
        .should('have.length', 1)
    })

    it('自己操作自己的内容不应该发送通知', () => {
      // 1. 用户1对自己的文章进行操作
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit(`/article/${testArticleId}`)
      
      cy.get('[data-cy="like-btn"]').click()
      cy.get('[data-cy="collect-btn"]').click()
      cy.get('[data-cy="comment-input"]').type('自己的评论')
      cy.get('[data-cy="comment-submit"]').click()
      
      // 2. 检查消息中心，不应该有来自自己的通知
      cy.visit('/user/notifications')
      
      cy.get('[data-cy="notification-item"]')
        .filter(':contains("测试用户1")')
        .should('have.length', 0)
    })
  })

  describe('消息中心界面功能', () => {
    beforeEach(() => {
      // 准备测试通知数据
      cy.task('createTestNotifications', {
        userId: 1,
        count: 5
      })
    })

    it('应该正确显示通知列表', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 检查页面元素
      cy.get('[data-cy="notification-header"]').should('be.visible')
      cy.get('[data-cy="notification-tabs"]').should('be.visible')
      cy.get('[data-cy="notification-list"]').should('be.visible')
      
      // 检查通知项
      cy.get('[data-cy="notification-item"]').should('have.length.at.least', 1)
    })

    it('应该支持筛选功能', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 测试"全部"标签
      cy.get('[data-cy="tab-all"]').click()
      cy.get('[data-cy="notification-item"]').should('have.length.at.least', 1)
      
      // 测试"未读"标签
      cy.get('[data-cy="tab-unread"]').click()
      cy.get('[data-cy="notification-item"]').each(($item) => {
        cy.wrap($item).should('have.class', 'unread')
      })
      
      // 测试"已读"标签
      cy.get('[data-cy="tab-read"]').click()
      cy.get('[data-cy="notification-item"]').each(($item) => {
        cy.wrap($item).should('not.have.class', 'unread')
      })
    })

    it('应该支持标记单条通知为已读', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 选择第一条未读通知
      cy.get('[data-cy="notification-item"].unread').first().within(() => {
        cy.get('[data-cy="mark-read-btn"]').click()
      })
      
      // 验证通知状态改变
      cy.get('[data-cy="notification-item"]').first()
        .should('not.have.class', 'unread')
    })

    it('应该支持批量删除通知', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 选择多条通知
      cy.get('[data-cy="notification-checkbox"]').first().check()
      cy.get('[data-cy="notification-checkbox"]').eq(1).check()
      
      // 点击删除选中
      cy.get('[data-cy="delete-selected-btn"]').click()
      cy.get('[data-cy="confirm-delete"]').click()
      
      // 验证通知被删除
      cy.get('[data-cy="notification-item"]').should('have.length.lessThan', 5)
    })

    it('应该支持全部标记为已读', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 点击全部标记已读
      cy.get('[data-cy="mark-all-read-btn"]').click()
      
      // 验证所有通知都变为已读
      cy.get('[data-cy="notification-item"].unread').should('have.length', 0)
      cy.get('[data-cy="notification-badge"]').should('not.exist')
    })

    it('应该支持点击通知跳转', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 点击文章相关通知
      cy.get('[data-cy="notification-item"]')
        .filter(':contains("点赞了你的文章")')
        .first()
        .click()
      
      // 验证跳转到文章页面
      cy.url().should('include', '/article/')
    })
  })

  describe('个人资料页面优化', () => {
    it('应该显示现代化的个人资料页面', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/profile')
      
      // 检查页面布局
      cy.get('[data-cy="profile-header"]').should('be.visible')
      cy.get('[data-cy="profile-card"]').should('be.visible')
      cy.get('[data-cy="stats-card"]').should('be.visible')
      cy.get('[data-cy="actions-card"]').should('be.visible')
      
      // 检查统计数据
      cy.get('[data-cy="stat-item"]').should('have.length', 6)
      cy.get('[data-cy="stat-number"]').should('be.visible')
      cy.get('[data-cy="stat-label"]').should('be.visible')
    })

    it('应该支持编辑个人资料', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/profile')
      
      // 点击编辑资料按钮
      cy.get('[data-cy="edit-profile-btn"]').click()
      
      // 修改昵称和邮箱
      cy.get('[data-cy="edit-nickname"]').clear().type('新昵称')
      cy.get('[data-cy="edit-email"]').clear().type('<EMAIL>')
      
      // 保存修改
      cy.get('[data-cy="save-profile-btn"]').click()
      
      // 验证修改成功
      cy.get('[data-cy="user-nickname"]').should('contain', '新昵称')
    })

    it('应该支持快捷操作', () => {
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/profile')
      
      // 测试消息中心快捷按钮
      cy.get('[data-cy="quick-notifications"]').click()
      cy.url().should('include', '/user/notifications')
      
      cy.go('back')
      
      // 测试我的收藏快捷按钮
      cy.get('[data-cy="quick-collection"]').click()
      cy.url().should('include', '/user/collection')
      
      cy.go('back')
      
      // 测试我的评论快捷按钮
      cy.get('[data-cy="quick-comment"]').click()
      cy.url().should('include', '/user/comment')
    })
  })

  describe('移动端响应式测试', () => {
    it('消息中心在移动端应该正常显示', () => {
      cy.viewport('iphone-x')
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/notifications')
      
      // 检查移动端布局
      cy.get('[data-cy="notification-header"]').should('be.visible')
      cy.get('[data-cy="notification-list"]').should('be.visible')
      
      // 检查按钮大小适合触摸
      cy.get('[data-cy="mark-read-btn"]').should('have.css', 'min-height')
    })

    it('个人资料页面在移动端应该正常显示', () => {
      cy.viewport('iphone-x')
      cy.loginAs(testUser1.username, testUser1.password)
      cy.visit('/user/profile')
      
      // 检查移动端布局
      cy.get('[data-cy="profile-card"]').should('be.visible')
      cy.get('[data-cy="stats-card"]').should('be.visible')
      
      // 检查统计卡片在移动端的排列
      cy.get('[data-cy="stats-grid"]').should('have.css', 'grid-template-columns')
    })
  })

  describe('问题修复验证', () => {
    it('评论管理页面应该正确处理已删除文章', () => {
      // 1. 创建文章和评论
      cy.loginAs(testUser1.username, testUser1.password)
      cy.createTestArticle('待删除文章')
      
      cy.loginAs(testUser2.username, testUser2.password)
      cy.addCommentToArticle(testArticleId, '测试评论')
      
      // 2. 管理员删除文章
      cy.loginAs(adminUser.username, adminUser.password)
      cy.deleteArticle(testArticleId)
      
      // 3. 检查评论管理页面
      cy.visit('/admin/comment')
      
      cy.get('[data-cy="comment-item"]').should('contain', '(文章已删除)')
      cy.get('[data-cy="deleted-article-title"]')
        .should('have.css', 'color')
        .and('have.css', 'font-style', 'italic')
    })

    it('访问不存在的文章应该正确处理', () => {
      cy.visit('/article/99999')
      
      // 应该显示友好错误提示并跳转到404页面
      cy.get('[data-cy="error-message"]').should('contain', '文章不存在或已被删除')
      cy.url().should('include', '/404')
    })
  })
})

// 自定义命令
Cypress.Commands.add('loginAs', (username, password) => {
  cy.visit('/login')
  cy.get('[data-cy="username"]').type(username)
  cy.get('[data-cy="password"]').type(password)
  cy.get('[data-cy="login-btn"]').click()
  cy.url().should('not.include', '/login')
})

Cypress.Commands.add('createTestArticle', (title) => {
  cy.visit('/admin/article/add')
  cy.get('[data-cy="article-title"]').type(title)
  cy.get('[data-cy="article-content"]').type('测试文章内容')
  cy.get('[data-cy="publish-btn"]').click()
})

Cypress.Commands.add('addCommentToArticle', (articleId, content) => {
  cy.visit(`/article/${articleId}`)
  cy.get('[data-cy="comment-input"]').type(content)
  cy.get('[data-cy="comment-submit"]').click()
})

Cypress.Commands.add('deleteArticle', (articleId) => {
  cy.visit('/admin/article')
  cy.get(`[data-cy="delete-article-${articleId}"]`).click()
  cy.get('[data-cy="confirm-delete"]').click()
})
