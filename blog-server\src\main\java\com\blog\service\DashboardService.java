package com.blog.service;

import com.blog.vo.ArticleVO;
import com.blog.vo.CommentVO;
import com.blog.vo.DashboardStatsVO;

import java.util.List;

/**
 * 仪表盘服务接口
 */
public interface DashboardService {

    /**
     * 获取仪表盘统计数据
     * @return 统计数据
     */
    DashboardStatsVO getDashboardStats();

    /**
     * 获取最近文章列表
     * @param limit 限制数量
     * @return 最近文章列表
     */
    List<ArticleVO> getRecentArticles(int limit);

    /**
     * 获取最近评论列表
     * @param limit 限制数量
     * @return 最近评论列表
     */
    List<CommentVO> getRecentComments(int limit);
}
