/*
 MariaDB Data Transfer (Converted from MySQL)

 Source Server         : 本地mysql
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:3306
 Source Schema         : blog_system

 Target Server Type    : MariaDB
 Target Server Version : 10.x
 File Encoding         : 65001
 Conversion Note       : utf8mb4_0900_ai_ci -> utf8mb4_unicode_ci

 Date: 15/07/2025 14:43:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章标题',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文章摘要',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章内容',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类ID',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片地址',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞量',
  `comment_count` int NULL DEFAULT 0 COMMENT '评论量',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '文章状态(0草稿,1已发布,2已删除)',
  `is_top` tinyint(1) NULL DEFAULT 0 COMMENT '是否置顶(0否,1是)',
  `allow_comment` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许评论(0否,1是)',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志(0存在,1删除)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_author_id`(`author_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article
-- ----------------------------
INSERT INTO `article` VALUES (1, 'Git安装配置使用详解', ' 最近开发环境更换升级了一个工作站，正好需要安装版本管理工具，顺手把安装的流程细节记录下来分享一下，希望能帮助大家快速的搭建开发环境。', '在最新的Git版本中，关于选择默认分支名称（Default Branch Name），有以下几个选项：\n  1.让Git决定（Let Git decide）： 这是Git 2.28版本之前的默认行为。即在创建新的仓库时，Git会使用默认的分支名称master。\n  2.覆盖新的默认分支名称（Override the default branch name for new repositories）： 由于技术和文化因素的考虑，Git 2.47版本引入了一个新的默认分支名称的选项。你可以将默认分支更改为其他名称（如main）。\n————————————————\n 版权声明：本文为博主原创文章，遵循 CC 4.0 BY-NC-SA 版权协议，转载请附上原文出处链接和本声明。\n \n原文链接：https://blog.csdn.net/fanyun\\_01/article/details/145350857', 1, 2, 'http://localhost:8080/api/upload/2025/07/15/7f524e24490945dcb56456d294c72ae6.png', 8, 0, 7, 1, 1, 1, 0, '2025-07-15 13:37:29', '2025-07-15 13:37:29');

-- ----------------------------
-- Table structure for article_collection
-- ----------------------------
DROP TABLE IF EXISTS `article_collection`;
CREATE TABLE `article_collection`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_article`(`user_id`, `article_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_collection
-- ----------------------------

-- ----------------------------
-- Table structure for article_like
-- ----------------------------
DROP TABLE IF EXISTS `article_like`;
CREATE TABLE `article_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_article`(`user_id`, `article_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_like
-- ----------------------------

-- ----------------------------
-- Table structure for article_tag
-- ----------------------------
DROP TABLE IF EXISTS `article_tag`;
CREATE TABLE `article_tag`  (
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`article_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_tag
-- ----------------------------
INSERT INTO `article_tag` VALUES (1, 1);
INSERT INTO `article_tag` VALUES (1, 2);

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类描述',
  `order` int NULL DEFAULT 0 COMMENT '排序',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父分类ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_order`(`order`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of category
-- ----------------------------
INSERT INTO `category` VALUES (2, 'Java技术', NULL, 0, NULL, '2025-07-15 13:34:09', '2025-07-15 13:34:09');

-- ----------------------------
-- Table structure for comment
-- ----------------------------
DROP TABLE IF EXISTS `comment`;
CREATE TABLE `comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父评论ID',
  `to_user_id` bigint NULL DEFAULT NULL COMMENT '回复目标用户ID',
  `anonymous_nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '匿名用户昵称',
  `anonymous_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '匿名用户邮箱',
  `is_anonymous` tinyint(1) NULL DEFAULT 0 COMMENT '是否匿名评论(0否,1是)',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(0待审核,1正常)',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志(0存在,1删除)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_article_id`(`article_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_anonymous_email`(`anonymous_email`) USING BTREE,
  INDEX `idx_is_anonymous`(`is_anonymous`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comment
-- ----------------------------
INSERT INTO `comment` VALUES (1, '沙发！', 3, 1, NULL, NULL, NULL, NULL, 0, 0, 1, 0, '2025-07-15 13:40:37', '2025-07-15 13:40:37');
INSERT INTO `comment` VALUES (2, '第二个评论', 3, 1, NULL, NULL, NULL, NULL, 0, 0, 1, 0, '2025-07-15 13:51:26', '2025-07-15 13:51:26');
INSERT INTO `comment` VALUES (3, '第三个评论', 3, 1, NULL, NULL, NULL, NULL, 0, 0, 1, 0, '2025-07-15 14:06:18', '2025-07-15 14:06:18');
INSERT INTO `comment` VALUES (4, '第四个评论', 3, 1, NULL, NULL, NULL, NULL, 0, 0, 1, 0, '2025-07-15 14:14:13', '2025-07-15 14:14:13');
INSERT INTO `comment` VALUES (5, '第四个评论的回复', 3, 1, 4, 3, NULL, NULL, 0, 0, 1, 0, '2025-07-15 14:14:32', '2025-07-15 14:14:32');
INSERT INTO `comment` VALUES (6, '管理员回复第四个评论', 1, 1, 4, 3, NULL, NULL, 0, 0, 1, 0, '2025-07-15 14:15:16', '2025-07-15 14:15:16');
INSERT INTO `comment` VALUES (7, '第五个评论', 3, 1, NULL, NULL, NULL, NULL, 0, 0, 1, 0, '2025-07-15 14:16:31', '2025-07-15 14:16:31');

-- ----------------------------
-- Table structure for comment_like
-- ----------------------------
DROP TABLE IF EXISTS `comment_like`;
CREATE TABLE `comment_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_comment`(`user_id`, `comment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comment_like
-- ----------------------------

-- ----------------------------
-- Table structure for config
-- ----------------------------
DROP TABLE IF EXISTS `config`;
CREATE TABLE `config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置值',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config
-- ----------------------------
INSERT INTO `config` VALUES (13, 'site_title', 'PURE程', 'Website Title', '2025-07-15 12:16:28', '2025-07-15 13:33:16');
INSERT INTO `config` VALUES (14, 'site_description', '一个学技术的网站', 'Website Description', '2025-07-15 12:16:28', '2025-07-15 13:33:16');
INSERT INTO `config` VALUES (15, 'site_keywords', '激情，认真，努力', 'Website Keywords', '2025-07-15 12:16:28', '2025-07-15 13:33:16');
INSERT INTO `config` VALUES (16, 'site_logo', '/api/upload/2025/07/15/2e03be4b009d46648ee4f5d86631b0c0.png', 'Website Logo', '2025-07-15 12:16:28', '2025-07-15 13:33:16');
INSERT INTO `config` VALUES (17, 'icp', '京123456A', 'ICP备案号', '2025-07-15 12:16:28', '2025-07-15 13:33:16');
INSERT INTO `config` VALUES (18, 'comment_audit', 'false', '评论审核开关', '2025-07-15 12:16:28', '2025-07-15 13:40:54');
INSERT INTO `config` VALUES (19, 'allow_anonymous_comment', 'false', '允许匿名评论', '2025-07-15 12:16:28', '2025-07-15 13:40:54');
INSERT INTO `config` VALUES (20, 'smtp_server', 'smtp.163.com', 'SMTP服务器地址', '2025-07-15 12:16:28', '2025-07-15 13:51:05');
INSERT INTO `config` VALUES (21, 'smtp_port', '25', 'SMTP端口', '2025-07-15 12:16:28', '2025-07-15 13:51:05');
INSERT INTO `config` VALUES (22, 'from_email', '<EMAIL>', '发件人邮箱', '2025-07-15 12:16:28', '2025-07-15 13:51:05');
INSERT INTO `config` VALUES (23, 'email_username', '<EMAIL>', '邮箱用户名', '2025-07-15 12:16:28', '2025-07-15 13:51:05');
INSERT INTO `config` VALUES (24, 'email_password', 'CHQNTsxXQQeEvJRa', '邮箱密码或授权码', '2025-07-15 12:16:28', '2025-07-15 13:51:05');

-- ----------------------------
-- Table structure for email_template
-- ----------------------------
DROP TABLE IF EXISTS `email_template`;
CREATE TABLE `email_template`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板代码',
  `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邮件主题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邮件内容',
  `template_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'HTML' COMMENT '模板类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '模板描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_template_code`(`template_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '邮件模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of email_template
-- ----------------------------
INSERT INTO `email_template` VALUES (1, '测试邮件模板', 'TEST_EMAIL', '博客系统邮件配置测试', '<p>邮件配置测试成功</p>', 'HTML', '邮件配置测试模板', 1, '2025-07-15 12:17:25', '2025-07-15 12:17:25');
INSERT INTO `email_template` VALUES (2, '评论通知模板', 'COMMENT_NOTIFICATION', '您收到了新评论 - {{ARTICLE_TITLE}}', '<div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\"><div style=\"background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\"><h1 style=\"color: white; margin: 0;\">您收到了新评论</h1></div><div style=\"background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;\"><p>您好！</p><p><strong>{{USER_NAME}}</strong> 在您的文章《<strong>{{ARTICLE_TITLE}}</strong>》中发表了评论：</p><div style=\"background: white; padding: 20px; margin: 20px 0; border-left: 4px solid #007bff;\"><p>{{COMMENT_CONTENT}}</p></div><div style=\"text-align: center; margin: 30px 0;\"><a href=\"{{ARTICLE_URL}}\" style=\"background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;\">查看评论</a></div></div></div>', 'HTML', '评论通知邮件模板', 1, '2025-07-15 12:17:25', '2025-07-15 14:12:27');
INSERT INTO `email_template` VALUES (3, '回复通知模板', 'REPLY_NOTIFICATION', '您的评论收到了回复 - {{ARTICLE_TITLE}}', '<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>回复通知</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 24px;\">💬 您的评论收到了回复</h1>\n    </div>\n    <div style=\"background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">\n        <p style=\"font-size: 16px;\">您好！</p>\n        <p style=\"font-size: 16px;\"><strong>{{USER_NAME}}</strong> 在文章《<strong>{{ARTICLE_TITLE}}</strong>》中回复了您的评论：</p>\n        <div style=\"background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #2196f3;\">\n            <p style=\"margin: 0; color: #1976d2; font-weight: bold;\">您的评论：</p>\n            <p style=\"margin: 10px 0 0 0; font-style: italic;\">\"{{ORIGINAL_COMMENT}}\"</p>\n        </div>\n        <div style=\"background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #e91e63;\">\n            <p style=\"margin: 0; color: #c2185b; font-weight: bold;\">回复内容：</p>\n            <p style=\"margin: 10px 0 0 0;\">\"{{REPLY_CONTENT}}\"</p>\n        </div>\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"{{ARTICLE_URL}}\" style=\"background-color: #e91e63; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">查看回复</a>\n        </div>\n        <p style=\"color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;\">\n            此邮件由个人动态博客系统自动发送，请勿回复。\n        </p>\n    </div>\n</body>\n</html>', 'HTML', '回复通知邮件模板', 1, '2025-07-15 12:17:25', '2025-07-15 14:11:16');
INSERT INTO `email_template` VALUES (4, '点赞通知模板', 'LIKE_NOTIFICATION', '您的文章获得了点赞', '<p>您的文章获得了点赞</p>', 'HTML', '点赞通知邮件模板', 1, '2025-07-15 12:17:25', '2025-07-15 12:17:25');
INSERT INTO `email_template` VALUES (5, '关注通知模板', 'FOLLOW_NOTIFICATION', '您有新的关注者', '<p>您有新的关注者</p>', 'HTML', '关注通知邮件模板', 1, '2025-07-15 12:17:25', '2025-07-15 12:17:25');
INSERT INTO `email_template` VALUES (6, '欢迎邮件模板', 'WELCOME_EMAIL', '欢迎加入博客系统', '<p>欢迎加入博客系统</p>', 'HTML', '欢迎邮件模板', 1, '2025-07-15 12:17:25', '2025-07-15 12:17:25');

-- ----------------------------
-- Table structure for notification
-- ----------------------------
DROP TABLE IF EXISTS `notification`;
CREATE TABLE `notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint NOT NULL COMMENT '接收用户ID',
  `from_user_id` bigint NULL DEFAULT NULL COMMENT '发送用户ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知标题',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知内容',
  `resource_id` bigint NULL DEFAULT NULL COMMENT '资源ID',
  `resource_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源类型',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notification
-- ----------------------------
INSERT INTO `notification` VALUES (1, 1, 3, 'comment', '程英雄 评论了你的文章', '《Git安装配置使用详解》', 2, 'comment', 0, '2025-07-15 13:51:27', '2025-07-15 13:51:27');
INSERT INTO `notification` VALUES (2, 1, 3, 'comment', '程英雄 评论了你的文章', '《Git安装配置使用详解》', 3, 'comment', 0, '2025-07-15 14:06:19', '2025-07-15 14:06:19');
INSERT INTO `notification` VALUES (3, 1, 3, 'comment', '程英雄 评论了你的文章', '《Git安装配置使用详解》', 4, 'comment', 0, '2025-07-15 14:14:13', '2025-07-15 14:14:13');
INSERT INTO `notification` VALUES (4, 3, 1, 'reply', 'Admin 回复了你的评论', '在文章《Git安装配置使用详解》中', 1, 'article', 1, '2025-07-15 14:15:16', '2025-07-15 14:16:53');
INSERT INTO `notification` VALUES (5, 1, 3, 'comment', '程英雄 评论了你的文章', '《Git安装配置使用详解》', 7, 'comment', 0, '2025-07-15 14:16:32', '2025-07-15 14:16:32');

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人',
  `operation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作类型',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求方法',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '返回结果',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态(0失败,1成功)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for system_setting
-- ----------------------------
DROP TABLE IF EXISTS `system_setting`;
CREATE TABLE `system_setting`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置键',
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '设置值',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设置描述',
  `setting_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设置分组',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_setting_key`(`setting_key`) USING BTREE,
  INDEX `idx_setting_group`(`setting_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_setting
-- ----------------------------
INSERT INTO `system_setting` VALUES (1, 'comment_audit', 'false', '评论审核开关', 'comment', '2025-07-15 12:16:44', '2025-07-15 12:16:44');
INSERT INTO `system_setting` VALUES (2, 'comment_anonymous', 'true', '允许匿名评论', 'comment', '2025-07-15 12:16:44', '2025-07-15 12:16:44');
INSERT INTO `system_setting` VALUES (3, 'email_enabled', 'true', '邮件功能开关', 'email', '2025-07-15 12:16:44', '2025-07-15 13:46:01');
INSERT INTO `system_setting` VALUES (4, 'email_smtp_host', 'smtp.163.com', 'SMTP服务器地址', 'email', '2025-07-15 12:16:44', '2025-07-15 13:46:01');
INSERT INTO `system_setting` VALUES (5, 'email_smtp_port', '25', 'SMTP端口', 'email', '2025-07-15 12:16:44', '2025-07-15 13:46:01');
INSERT INTO `system_setting` VALUES (6, 'email_username', '<EMAIL>', '邮箱用户名', 'email', '2025-07-15 12:16:44', '2025-07-15 13:46:01');
INSERT INTO `system_setting` VALUES (7, 'email_password', 'CHQNTsxXQQeEvJRa', '邮箱密码或授权码', 'email', '2025-07-15 12:16:44', '2025-07-15 13:46:01');
INSERT INTO `system_setting` VALUES (8, 'email_from_name', 'Personal Blog', '发件人名称', 'email', '2025-07-15 12:16:44', '2025-07-15 12:16:44');

-- ----------------------------
-- Table structure for tag
-- ----------------------------
DROP TABLE IF EXISTS `tag`;
CREATE TABLE `tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tag
-- ----------------------------
INSERT INTO `tag` VALUES (1, 'SpringBoot', '2025-07-15 13:34:18', '2025-07-15 13:34:18');
INSERT INTO `tag` VALUES (2, 'Mysql', '2025-07-15 13:34:28', '2025-07-15 13:34:28');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像地址',
  `bio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '个人简介',
  `role` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'user' COMMENT '角色(admin/user)',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(0禁用,1正常)',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志(0存在,1删除)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2a$10$klNo.X/TSC3PPGHTDNc70e1/RZF4/xg23XWKAfzx6.MTiqVoVAVda', '天幕啊', '<EMAIL>', '/api/upload/avatar/7f0f265e8f7b42d094ee157605073df3.jpg', NULL, 'admin', 1, NULL, NULL, 0, '2025-07-15 12:15:16', '2025-07-15 14:16:13');
INSERT INTO `user` VALUES (3, 'chlingyu', '$2a$10$mTDTA3EAsihIlFxS6qbFJOEWDVDsnKfdZjbD1kApL/2ac3s/MzxTC', '程英雄', '<EMAIL>', NULL, NULL, 'user', 1, NULL, NULL, 0, '2025-07-15 13:40:19', '2025-07-15 14:17:43');

-- ----------------------------
-- Table structure for user_follow
-- ----------------------------
DROP TABLE IF EXISTS `user_follow`;
CREATE TABLE `user_follow`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `follower_id` bigint NOT NULL COMMENT '关注者ID',
  `followed_id` bigint NOT NULL COMMENT '被关注者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_follower_followed`(`follower_id`, `followed_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户关注表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_follow
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
