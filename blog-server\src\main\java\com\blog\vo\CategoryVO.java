package com.blog.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分类视图对象，用于返回给前端展示的分类数据
 */
@Data
public class CategoryVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 排序值，值越小排序越靠前
     */
    private Integer order;
    
    /**
     * 父分类ID，顶级分类为null
     */
    private Long parentId;
    
    /**
     * 父分类名称，顶级分类为null
     */
    private String parentName;
    
    /**
     * 子分类列表，用于构建树形结构
     */
    private List<CategoryVO> children;
    
    /**
     * 文章数量
     */
    private Integer articleCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 