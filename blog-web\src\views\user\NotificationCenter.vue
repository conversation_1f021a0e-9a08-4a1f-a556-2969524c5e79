<template>
  <div class="notification-center">
    <div class="header">
      <h2>消息中心</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          size="small" 
          @click="markAllAsRead"
          :disabled="unreadCount === 0"
        >
          全部标记已读
        </el-button>
        <el-button 
          type="danger" 
          size="small" 
          @click="deleteSelected"
          :disabled="selectedNotifications.length === 0"
        >
          删除选中
        </el-button>
      </div>
    </div>

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部" name="all">
          <template #label>
            全部 <el-badge :value="totalCount" :hidden="totalCount === 0" />
          </template>
        </el-tab-pane>
        <el-tab-pane label="未读" name="unread">
          <template #label>
            未读 <el-badge :value="unreadCount" :hidden="unreadCount === 0" />
          </template>
        </el-tab-pane>
        <el-tab-pane label="已读" name="read">
          <template #label>
            已读
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list" v-loading="loading">
      <el-checkbox-group v-model="selectedNotifications" v-if="notifications.length > 0">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': notification.isRead === 0 }"
        >
          <el-checkbox :label="notification.id" class="notification-checkbox" />
          
          <div class="notification-content" @click="handleNotificationClick(notification)">
            <div class="notification-header">
              <div class="user-info">
                <el-avatar 
                  :src="notification.fromAvatar" 
                  :size="32"
                  class="user-avatar"
                >
                  {{ notification.fromNickname?.charAt(0) }}
                </el-avatar>
                <span class="user-name">{{ notification.fromNickname }}</span>
                <span class="notification-type">{{ getTypeDescription(notification.type) }}</span>
              </div>
              <div class="notification-time">
                {{ formatTime(notification.createTime) }}
              </div>
            </div>
            
            <div class="notification-body">
              <!-- 对于文章相关通知，显示文章标题 -->
              <div class="notification-title" v-if="notification.resourceTitle && (notification.type === 'like' || notification.type === 'comment' || notification.type === 'collect' || notification.type === 'reply')">
                <span class="article-title">《{{ notification.resourceTitle }}》</span>
              </div>
              <!-- 对于其他通知，显示通知内容 -->
              <div class="notification-desc" v-else-if="notification.content">
                {{ notification.content }}
              </div>
            </div>
          </div>
          
          <div class="notification-actions">
            <el-button 
              v-if="notification.isRead === 0"
              type="text" 
              size="small" 
              @click="markSingleAsRead(notification.id)"
            >
              标记已读
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="deleteSingle(notification.id)"
              class="delete-btn"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-checkbox-group>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无通知" />
      </div>
    </div>

    <!-- 加载更多 -->
    <div class="load-more" v-if="hasMore && notifications.length > 0">
      <el-button @click="loadMore" :loading="loadingMore">
        加载更多
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { 
  getNotifications, 
  getUnreadCount, 
  markAsRead, 
  markAllAsRead as markAllAsReadApi,
  deleteNotifications 
} from '@/api/notification'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const activeTab = ref('all')
const notifications = ref([])
const selectedNotifications = ref([])
const unreadCount = ref(0)
const totalCount = ref(0)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 20

// 计算属性
const currentFilter = computed(() => {
  switch (activeTab.value) {
    case 'unread': return 0
    case 'read': return 1
    default: return null
  }
})

// 方法
const fetchNotifications = async (reset = false) => {
  if (reset) {
    currentPage.value = 1
    notifications.value = []
    hasMore.value = true
  }
  
  loading.value = reset
  loadingMore.value = !reset
  
  try {
    const params = {
      isRead: currentFilter.value,
      limit: pageSize
    }
    
    const res = await getNotifications(params)
    if (res.code === 200) {
      if (reset) {
        notifications.value = res.data
      } else {
        notifications.value.push(...res.data)
      }
      
      hasMore.value = res.data.length === pageSize
      totalCount.value = notifications.value.length
    }
  } catch (error) {
    console.error('获取通知列表失败', error)
    ElMessage.error('获取通知列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const fetchUnreadCount = async () => {
  try {
    const res = await getUnreadCount()
    if (res.code === 200) {
      unreadCount.value = res.data.unreadCount
    }
  } catch (error) {
    console.error('获取未读数量失败', error)
  }
}

const handleTabChange = (tab) => {
  activeTab.value = tab
  selectedNotifications.value = []
  fetchNotifications(true)
}

const loadMore = () => {
  currentPage.value++
  fetchNotifications(false)
}

const markSingleAsRead = async (notificationId) => {
  try {
    const res = await markAsRead([notificationId])
    if (res.code === 200) {
      // 更新本地状态
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = 1
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      ElMessage.success('标记成功')
    }
  } catch (error) {
    console.error('标记已读失败', error)
    ElMessage.error('标记失败')
  }
}

const markAllAsRead = async () => {
  try {
    const res = await markAllAsReadApi()
    if (res.code === 200) {
      // 更新本地状态
      notifications.value.forEach(n => n.isRead = 1)
      unreadCount.value = 0
      ElMessage.success('全部标记成功')
    }
  } catch (error) {
    console.error('标记全部已读失败', error)
    ElMessage.error('标记失败')
  }
}

const deleteSingle = async (notificationId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '确认删除', {
      type: 'warning'
    })
    
    const res = await deleteNotifications([notificationId])
    if (res.code === 200) {
      // 从列表中移除
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index > -1) {
        const notification = notifications.value[index]
        if (notification.isRead === 0) {
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
        notifications.value.splice(index, 1)
        totalCount.value--
      }
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败', error)
      ElMessage.error('删除失败')
    }
  }
}

const deleteSelected = async () => {
  if (selectedNotifications.value.length === 0) {
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedNotifications.value.length} 条通知吗？`, '确认删除', {
      type: 'warning'
    })
    
    const res = await deleteNotifications(selectedNotifications.value)
    if (res.code === 200) {
      // 从列表中移除选中的通知
      selectedNotifications.value.forEach(id => {
        const index = notifications.value.findIndex(n => n.id === id)
        if (index > -1) {
          const notification = notifications.value[index]
          if (notification.isRead === 0) {
            unreadCount.value = Math.max(0, unreadCount.value - 1)
          }
          notifications.value.splice(index, 1)
          totalCount.value--
        }
      })
      
      selectedNotifications.value = []
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除通知失败', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleNotificationClick = (notification) => {
  // 如果未读，先标记为已读
  if (notification.isRead === 0) {
    markSingleAsRead(notification.id)
  }

  // 根据通知类型跳转到相应页面
  if (notification.resourceType === 'article' && notification.resourceId) {
    // 文章相关通知（包括回复通知），直接跳转到文章页面
    router.push(`/article/${notification.resourceId}`)
  } else if (notification.resourceType === 'comment' && notification.type === 'reply') {
    // 旧的回复通知，暂时显示提示信息
    ElMessage.info('请点击文章标题跳转到对应文章')
  } else if (notification.resourceType === 'user' && notification.fromUserId) {
    // 用户相关通知，跳转到用户页面
    router.push(`/user/${notification.fromUserId}`)
  }
}

const getTypeDescription = (type) => {
  const typeMap = {
    'like': '点赞了你的文章',
    'comment': '评论了你的文章',
    'reply': '回复了你的评论',
    'follow': '关注了你',
    'collect': '收藏了你的文章'
  }
  return typeMap[type] || '通知'
}

const formatTime = (time) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 604800000) { // 1周内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// 生命周期
onMounted(() => {
  fetchNotifications(true)
  fetchUnreadCount()
})
</script>

<style scoped>
.notification-center {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-tabs {
  margin-bottom: 20px;
}

.notification-list {
  min-height: 400px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s;
  background: #fff;
}

.notification-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 4px solid #409eff;
  background: #f0f9ff;
}

.notification-checkbox {
  margin-right: 12px;
  margin-top: 4px;
}

.notification-content {
  flex: 1;
  cursor: pointer;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.notification-type {
  color: #606266;
  font-size: 14px;
}

.notification-time {
  color: #909399;
  font-size: 12px;
}

.notification-body {
  margin-left: 40px;
}

.notification-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.article-title {
  color: #409eff;
  font-weight: 500;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.notification-desc {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
}

.resource-info {
  margin-top: 8px;
}

.resource-title {
  display: inline-block;
  padding: 2px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 12px;
}

.delete-btn {
  color: #f56c6c !important;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.load-more {
  text-align: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .notification-item {
    padding: 12px;
  }

  .notification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .notification-actions {
    flex-direction: row;
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
