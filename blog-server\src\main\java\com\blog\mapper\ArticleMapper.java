package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.Article;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文章Mapper接口
 */
@Mapper
public interface ArticleMapper extends BaseMapper<Article> {
    
    /**
     * 根据标签ID查询文章ID列表
     * @param tagId 标签ID
     * @return 文章ID列表
     */
    @Select("SELECT article_id FROM article_tag WHERE tag_id = #{tagId}")
    List<Long> selectArticleIdsByTagId(@Param("tagId") Long tagId);
    
    /**
     * 根据分类ID统计文章数量
     * @param categoryId 分类ID
     * @return 文章数量
     */
    @Select("SELECT COUNT(*) FROM article WHERE category_id = #{categoryId} AND del_flag = 0")
    int countByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据用户名查询文章列表
     * @param username 用户名
     * @return 文章列表
     */
    List<Article> selectByUsername(@Param("username") String username);
} 