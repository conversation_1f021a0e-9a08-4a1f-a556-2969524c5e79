package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.entity.Article;
import com.blog.entity.Comment;
import com.blog.entity.User;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.CommentMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.ArticleService;
import com.blog.service.CommentService;
import com.blog.service.DashboardService;
import com.blog.vo.ArticleVO;
import com.blog.vo.CommentVO;
import com.blog.vo.DashboardStatsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仪表盘服务实现类
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private CommentService commentService;

    /**
     * 获取仪表盘统计数据
     * @return 统计数据
     */
    @Override
    public DashboardStatsVO getDashboardStats() {
        DashboardStatsVO stats = new DashboardStatsVO();

        // 获取今日开始和结束时间
        LocalDateTime todayStart = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MAX);

        log.info("当前时间: {}, 今日开始: {}, 今日结束: {}",
                LocalDateTime.now(), todayStart, todayEnd);

        // 统计文章数据
        long totalArticles = articleMapper.selectCount(
                new LambdaQueryWrapper<Article>()
                        .eq(Article::getDelFlag, 0) // 未删除的文章
        );
        stats.setArticleCount(totalArticles);

        // 统计今日新增文章
        long todayArticles = articleMapper.selectCount(
                new LambdaQueryWrapper<Article>()
                        .eq(Article::getDelFlag, 0)
                        .between(Article::getCreateTime, todayStart, todayEnd)
        );
        stats.setTodayArticleCount(todayArticles);

        // 统计用户数据
        // 先查询所有用户数量
        long allUsers = userMapper.selectCount(new LambdaQueryWrapper<User>());
        log.info("数据库中总用户数: {}", allUsers);

        // 查询各种状态的用户数量
        long statusZeroUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .eq(User::getStatus, 0) // 禁用状态的用户
        );
        long statusOneUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .eq(User::getStatus, 1) // 启用状态的用户（正常用户）
        );
        long statusNullUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .isNull(User::getStatus) // status为null的用户
        );

        log.info("status=0的用户数(禁用): {}, status=1的用户数(启用): {}, status=null的用户数: {}",
                statusZeroUsers, statusOneUsers, statusNullUsers);

        // 统计启用状态的用户（status=1）
        stats.setUserCount(statusOneUsers);

        // 统计今日新增用户
        log.info("今日时间范围: {} 到 {}", todayStart, todayEnd);

        // 查询今日所有新增用户（不限制status）
        long todayAllUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .between(User::getCreateTime, todayStart, todayEnd)
        );

        // 查询今日新增的status=0用户（禁用状态）
        long todayStatusZeroUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .eq(User::getStatus, 0)
                        .between(User::getCreateTime, todayStart, todayEnd)
        );

        // 查询今日新增的status=1用户（启用状态）
        long todayStatusOneUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                        .eq(User::getStatus, 1)
                        .between(User::getCreateTime, todayStart, todayEnd)
        );

        log.info("今日新增用户统计: 总数={}, status=0的(禁用)={}, status=1的(启用)={}",
                todayAllUsers, todayStatusZeroUsers, todayStatusOneUsers);

        // 统计今日新增的启用状态用户（status=1）
        stats.setTodayUserCount(todayStatusOneUsers);

        // 统计评论数据
        long totalComments = commentMapper.selectCount(
                new LambdaQueryWrapper<Comment>()
                        .eq(Comment::getStatus, 1) // 已发布的评论
        );
        stats.setCommentCount(totalComments);

        // 统计今日新增评论
        long todayComments = commentMapper.selectCount(
                new LambdaQueryWrapper<Comment>()
                        .eq(Comment::getStatus, 1)
                        .between(Comment::getCreateTime, todayStart, todayEnd)
        );
        stats.setTodayCommentCount(todayComments);

        // 统计总浏览量（所有文章的浏览量之和）
        List<Article> articles = articleMapper.selectList(
                new LambdaQueryWrapper<Article>()
                        .eq(Article::getDelFlag, 0)
                        .select(Article::getViewCount)
        );
        long totalViews = articles.stream()
                .mapToLong(article -> article.getViewCount() != null ? article.getViewCount() : 0)
                .sum();
        stats.setViewCount(totalViews);

        // 统计今日浏览量（这里简化处理，实际应该有专门的浏览记录表）
        // 暂时设置为今日新增文章的浏览量之和
        List<Article> todayArticleList = articleMapper.selectList(
                new LambdaQueryWrapper<Article>()
                        .eq(Article::getDelFlag, 0)
                        .between(Article::getCreateTime, todayStart, todayEnd)
                        .select(Article::getViewCount)
        );
        long todayViews = todayArticleList.stream()
                .mapToLong(article -> article.getViewCount() != null ? article.getViewCount() : 0)
                .sum();
        stats.setTodayViewCount(todayViews);

        log.info("获取仪表盘统计数据成功: 文章数={}, 用户数={}, 评论数={}, 浏览量={}",
                totalArticles, statusOneUsers, totalComments, totalViews);

        return stats;
    }

    /**
     * 获取最近文章列表
     * @param limit 限制数量
     * @return 最近文章列表
     */
    @Override
    public List<ArticleVO> getRecentArticles(int limit) {
        // 分页查询最近的文章
        IPage<ArticleVO> articlePage = articleService.getArticleList(1, limit, null, null, null, null);

        return articlePage.getRecords();
    }

    /**
     * 获取最近评论列表
     * @param limit 限制数量
     * @return 最近评论列表
     */
    @Override
    public List<CommentVO> getRecentComments(int limit) {
        // 查询最近的评论
        List<Comment> comments = commentMapper.selectList(
                new LambdaQueryWrapper<Comment>()
                        .eq(Comment::getStatus, 1) // 已发布的评论
                        .orderByDesc(Comment::getCreateTime)
                        .last("LIMIT " + limit)
        );

        // 转换为VO并获取关联信息
        return comments.stream()
                .map(comment -> {
                    try {
                        return commentService.convertToVO(comment);
                    } catch (Exception e) {
                        log.warn("转换评论VO失败，评论ID: {}", comment.getId(), e);
                        return null;
                    }
                })
                .filter(vo -> vo != null)
                .collect(Collectors.toList());
    }
}
