package com.blog.service;

import java.util.Map;

/**
 * 邮件服务接口
 */
public interface EmailService {

    /**
     * 发送简单文本邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    boolean sendTextMail(String to, String subject, String content);

    /**
     * 发送HTML邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param htmlContent HTML邮件内容
     * @return 是否发送成功
     */
    boolean sendHtmlMail(String to, String subject, String htmlContent);

    /**
     * 测试邮件配置
     * @param emailConfig 邮件配置信息
     * @return 测试结果信息
     */
    String testEmailConfig(Map<String, String> emailConfig);

    /**
     * 发送通知邮件
     * @param to 收件人邮箱
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     * @return 是否发送成功
     */
    boolean sendNotificationMail(String to, String title, String content, String type);

    /**
     * 使用指定配置发送邮件
     * @param emailConfig 邮件配置
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param isHtml 是否为HTML格式
     * @return 是否发送成功
     */
    boolean sendMailWithConfig(Map<String, String> emailConfig, String to, String subject, String content, boolean isHtml);
}
