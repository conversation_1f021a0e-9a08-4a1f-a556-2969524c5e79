package com.blog.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通知数据传输对象
 */
@Data
public class NotificationDTO {

    /**
     * 接收通知的用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 发送通知的用户ID
     */
    @NotNull(message = "发送者用户ID不能为空")
    private Long fromUserId;

    /**
     * 通知类型
     */
    @NotBlank(message = "通知类型不能为空")
    private String type;

    /**
     * 通知标题
     */
    @NotBlank(message = "通知标题不能为空")
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 关联的资源ID
     */
    private Long resourceId;

    /**
     * 关联的资源类型
     */
    private String resourceType;
}
