# 系统配置说明

## 📋 端口配置

| 服务 | 端口 | 配置文件 |
|------|------|----------|
| 后端 | 8080 | `blog-server/src/main/resources/application.yml` |
| 前端 | 3000 | `blog-web/vite.config.js` |
| MySQL | 3306 | 系统默认 |

## 🗄️ 数据库配置

### 连接信息
- **数据库**：blog_system
- **用户名**：root
- **密码**：12345
- **主机**：localhost:3306

### 初始化
```sql
-- 创建数据库
CREATE DATABASE blog_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据
SOURCE blog_system.sql;
```

## 🔐 认证配置

### JWT设置
```yaml
jwt:
  secret: blogSystem123456
  expiration: 1800000  # 30分钟
```

### 默认账户
- **管理员**：admin / admin123
- **测试用户**：可自行注册

## 📁 文件上传

### 配置
```yaml
file:
  upload:
    path: uploads
  access:
    path: /upload/
```

### 限制
- **格式**：JPG, PNG, GIF
- **大小**：最大2MB
- **位置**：项目根目录/uploads/

## 🔧 开发环境

### 后端配置
**文件**：`blog-server/src/main/resources/application.yml`
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: ***************************************
    username: root
    password: 12345
```

### 前端配置
**文件**：`blog-web/vite.config.js`
```javascript
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 🚀 生产环境

### 环境变量
```bash
export DB_HOST=your-db-host
export DB_USERNAME=your-username
export DB_PASSWORD=your-password
export JWT_SECRET=your-jwt-secret
```

### 配置文件
**生产配置**：`application-prod.yml`
```yaml
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST}:3306/blog_system
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

jwt:
  secret: ${JWT_SECRET}
```

## 🐛 常见配置问题

### 端口冲突
- 修改 `vite.config.js` 中的端口
- 修改 `application.yml` 中的端口
- 同时更新代理配置

### 数据库连接失败
- 检查MySQL服务状态
- 验证连接参数
- 确认数据库存在

### 文件上传失败
- 检查uploads目录权限
- 验证文件大小和格式
- 确认路径配置正确

---

**详细配置**：参考 [环境配置指南](环境配置指南.md)
