describe('文章互动功能测试', () => {
  let articleId;
  
  // 在所有测试前执行一次：创建测试文章
  before(() => {
    // 登录为管理员并创建一篇测试文章
    cy.loginAsAdmin();
    cy.visit('/admin/article/add');
    
    const title = `测试文章 ${Date.now()}`;
    cy.get('[data-cy="article-title-input"]').type(title);
    cy.get('[data-cy="article-content-editor"]').type('这是一篇用于测试点赞和收藏功能的文章。');
    cy.get('[data-cy="article-submit"]').click();
    
    // 等待提交成功
    cy.contains('文章发布成功');
    
    // 获取文章ID
    cy.visit('/home');
    cy.contains(title).click();
    cy.url().then(url => {
      articleId = url.split('/').pop();
    });
  });
  
  // 每个测试前登出
  beforeEach(() => {
    cy.clearLocalStorage();
    cy.visit('/');
  });
  
  it('未登录用户尝试点赞文章时应提示登录', () => {
    // 访问文章详情页
    cy.visit(`/article/${articleId}`);
    
    // 点击点赞按钮
    cy.get('[data-cy="like-button"]').click();
    
    // 应显示登录提示
    cy.contains('登录后才能点赞');
    cy.contains('去登录').should('be.visible');
    
    // 取消登录提示
    cy.contains('取消').click();
  });
  
  it('未登录用户尝试收藏文章时应提示登录', () => {
    // 访问文章详情页
    cy.visit(`/article/${articleId}`);
    
    // 点击收藏按钮
    cy.get('[data-cy="collect-button"]').click();
    
    // 应显示登录提示
    cy.contains('登录后才能收藏');
    cy.contains('去登录').should('be.visible');
    
    // 取消登录提示
    cy.contains('取消').click();
  });
  
  it('登录用户可以点赞和取消点赞文章', () => {
    // 登录
    cy.loginAsUser();
    
    // 访问文章详情页
    cy.visit(`/article/${articleId}`);
    
    // 获取初始点赞数
    let initialLikeCount;
    cy.get('[data-cy="like-button"]').then($btn => {
      const text = $btn.text();
      initialLikeCount = parseInt(text.match(/\((\d+)\)/)[1]);
    });
    
    // 点击点赞按钮
    cy.get('[data-cy="like-button"]').click();
    
    // 确认点赞成功
    cy.contains('点赞成功');
    cy.get('[data-cy="like-button"]').should('contain', '已点赞');
    
    // 检查点赞数增加
    cy.get('[data-cy="like-button"]').should('contain', `(${initialLikeCount + 1})`);
    
    // 再次点击取消点赞
    cy.get('[data-cy="like-button"]').click();
    
    // 确认取消点赞成功
    cy.contains('已取消点赞');
    cy.get('[data-cy="like-button"]').should('contain', '点赞');
    
    // 检查点赞数恢复
    cy.get('[data-cy="like-button"]').should('contain', `(${initialLikeCount})`);
  });
  
  it('登录用户可以收藏和取消收藏文章', () => {
    // 登录
    cy.loginAsUser();
    
    // 访问文章详情页
    cy.visit(`/article/${articleId}`);
    
    // 获取初始收藏数
    let initialCollectCount;
    cy.get('[data-cy="collect-button"]').then($btn => {
      const text = $btn.text();
      initialCollectCount = parseInt(text.match(/\((\d+)\)/)[1]);
    });
    
    // 点击收藏按钮
    cy.get('[data-cy="collect-button"]').click();
    
    // 确认收藏成功
    cy.contains('收藏成功');
    cy.get('[data-cy="collect-button"]').should('contain', '已收藏');
    
    // 检查收藏数增加
    cy.get('[data-cy="collect-button"]').should('contain', `(${initialCollectCount + 1})`);
    
    // 访问我的收藏页面
    cy.visit('/user/collection');
    
    // 确认文章显示在收藏列表中
    cy.contains('测试文章').should('be.visible');
    
    // 点击取消收藏
    cy.contains('取消收藏').click();
    cy.contains('确定').click();
    
    // 确认取消收藏成功
    cy.contains('已取消收藏');
    
    // 确认文章从收藏列表中消失
    cy.contains('测试文章').should('not.exist');
  });
}); 