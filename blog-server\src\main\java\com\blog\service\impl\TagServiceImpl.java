package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.exception.ConflictException;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.TagDTO;
import com.blog.entity.Tag;
import com.blog.mapper.ArticleTagMapper;
import com.blog.mapper.TagMapper;
import com.blog.service.TagService;
import com.blog.vo.TagVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 */
@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    @Autowired
    private ArticleTagMapper articleTagMapper;
    
    /**
     * 添加标签
     * @param tagDTO 标签DTO
     * @return 新增标签的ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTag(TagDTO tagDTO) {
        // 检查标签名称是否已存在
        if (checkNameExist(tagDTO.getName(), null)) {
            throw new BusinessException("标签名称已存在");
        }
        
        // 转换DTO为实体
        Tag tag = new Tag();
        BeanUtils.copyProperties(tagDTO, tag);
        
        // 保存标签
        save(tag);
        return tag.getId();
    }

    /**
     * 更新标签
     * @param tagDTO 标签DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTag(TagDTO tagDTO) {
        // 检查标签是否存在
        if (getById(tagDTO.getId()) == null) {
            // 抛出资源未找到异常
            throw new NotFoundException("标签不存在");
        }
        
        // 检查标签名称是否已存在
        if (checkNameExist(tagDTO.getName(), tagDTO.getId())) {
            throw new BusinessException("标签名称已存在");
        }
        
        // 转换DTO为实体
        Tag tag = new Tag();
        BeanUtils.copyProperties(tagDTO, tag);
        
        // 更新标签
        return updateById(tag);
    }

    /**
     * 删除标签
     * @param id 标签ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTag(Long id) {
        // 检查标签是否存在
        if (getById(id) == null) {
            // 抛出资源未找到异常
            throw new NotFoundException("标签不存在");
        }
        
        // 检查是否有关联的文章
        int articleCount = baseMapper.countArticleByTagId(id);
        if (articleCount > 0) {
            // 如果有关联文章，则抛出冲突异常，禁止删除
            throw new ConflictException("无法删除，标签下存在关联文章");
        }
        
        // （安全删除）先删除文章与标签的关联关系
        articleTagMapper.deleteByTagId(id);

        // 最后删除标签
        return removeById(id);
    }

    /**
     * 获取标签详情
     * @param id 标签ID
     * @return 标签VO
     */
    @Override
    public TagVO getTagDetail(Long id) {
        // 查询标签
        Tag tag = getById(id);
        if (tag == null) {
            // 抛出资源未找到异常
            throw new NotFoundException("标签不存在");
        }
        
        // 转换为VO
        TagVO tagVO = new TagVO();
        BeanUtils.copyProperties(tag, tagVO);
        
        // 查询文章数量
        int articleCount = baseMapper.countArticleByTagId(id);
        tagVO.setArticleCount(articleCount);
        
        return tagVO;
    }

    /**
     * 获取标签列表
     * @return 标签VO列表
     */
    @Override
    public List<TagVO> getTagList() {
        // 直接调用Mapper中的高效查询方法
        return baseMapper.selectTagWithArticleCount();
    }

    /**
     * 获取文章的标签列表
     * @param articleId 文章ID
     * @return 标签列表
     */
    @Override
    public List<TagVO> getTagsByArticleId(Long articleId) {
        // 查询文章关联的标签
        List<Tag> tagList = baseMapper.selectByArticleId(articleId);
        
        // 转换为VO
        return tagList.stream().map(tag -> {
            TagVO tagVO = new TagVO();
            BeanUtils.copyProperties(tag, tagVO);
            return tagVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取热门标签（使用频率最高的标签）
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @Override
    public List<TagVO> getPopularTags(int limit) {
        // 查询热门标签
        List<Tag> tagList = baseMapper.selectPopularTags(limit);
        
        // 转换为VO
        return tagList.stream().map(tag -> {
            TagVO tagVO = new TagVO();
            BeanUtils.copyProperties(tag, tagVO);
            
            // 查询文章数量
            int articleCount = baseMapper.countArticleByTagId(tag.getId());
            tagVO.setArticleCount(articleCount);
            
            return tagVO;
        }).collect(Collectors.toList());
    }

    /**
     * 检查标签是否存在
     * @param id 标签ID
     * @return 是否存在
     */
    @Override
    public boolean exists(Long id) {
        if (id == null) {
            return false;
        }
        return getById(id) != null;
    }

    /**
     * 检查标签名称是否已存在
     * @param name 标签名称
     * @param excludeId 排除的ID（更新时用）
     * @return 是否存在
     */
    private boolean checkNameExist(String name, Long excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<Tag>()
                .eq(Tag::getName, name);
        
        if (excludeId != null) {
            queryWrapper.ne(Tag::getId, excludeId);
        }
        
        return count(queryWrapper) > 0;
    }
} 