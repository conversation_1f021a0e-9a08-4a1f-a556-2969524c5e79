<template>
  <div class="tag-management-container" data-cy="tag-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>标签管理</span>
        </div>
      </template>

      <div class="toolbar">
        <el-button type="primary" :icon="Plus" @click="handleOpenDialog()" data-cy="add-tag-btn">新增标签</el-button>
      </div>

      <el-table :data="tagList" style="width: 100%" v-loading="loading" data-cy="tag-table">
        <el-table-column prop="name" label="标签名称" />
        <el-table-column prop="createTime" label="创建时间">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" :icon="Edit" circle @click="handleOpenDialog(scope.row)" data-cy="edit-tag-btn" />
            <el-button type="danger" :icon="Delete" circle @click="handleDelete(scope.row)" data-cy="delete-tag-btn" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="30%"
      @close="resetForm"
      data-cy="tag-dialog"
    >
      <el-form
        ref="formRef"
        :model="tagForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" data-cy="tag-name-input" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" data-cy="cancel-btn">取消</el-button>
          <el-button type="primary" @click="handleSubmit" data-cy="confirm-btn">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getTags, addTag, updateTag, deleteTag } from '@/api/tag';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { formatDateTime } from '@/utils/format';

// 加载状态
const loading = ref(true);

// 表格数据
const tagList = ref([]);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');

// 表单相关
const formRef = ref(null);
const tagForm = reactive({
  id: null,
  name: '',
});
const formRules = reactive({
  name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
});

// 获取标签列表
const fetchTags = async () => {
  loading.value = true;
  try {
    const res = await getTags();
    tagList.value = res.data;
  } catch (error) {
    console.error('获取标签列表失败', error);
    ElMessage.error('获取标签列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  fetchTags();
});

// 重置表单
const resetForm = () => {
  tagForm.id = null;
  tagForm.name = '';
  formRef.value?.resetFields();
};

// 打开新增/编辑对话框
const handleOpenDialog = (tag) => {
  resetForm();
  if (tag && tag.id) {
    // 编辑
    dialogTitle.value = '编辑标签';
    Object.assign(tagForm, tag);
  } else {
    // 新增
    dialogTitle.value = '新增标签';
  }
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (tag) => {
  ElMessageBox.confirm(`确定要删除标签 "${tag.name}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteTag(tag.id);
      ElMessage.success('删除成功');
      fetchTags(); // 重新加载数据
    } catch (error) {
      console.error('删除失败', error);
      // 如果有具体错误信息，显示给用户
      if (error.response && error.response.status === 409) {
        ElMessage.error(`删除失败: ${error.response.data.message || '该标签下有关联文章，无法删除'}`);
      } else {
        ElMessage.error('删除失败，请稍后重试');
      }
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 处理表单提交
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const isEdit = !!tagForm.id;
      try {
        if (isEdit) {
          await updateTag(tagForm.id, tagForm);
        } else {
          await addTag(tagForm);
        }
        ElMessage.success(isEdit ? '更新成功' : '新增成功');
        dialogVisible.value = false;
        fetchTags();
      } catch (error) {
        console.error(isEdit ? '更新失败' : '新增失败', error);
        // 业务异常提示由request拦截器处理
      }
    }
  });
};
</script>

<style scoped>
.tag-management-container {
  padding: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.toolbar {
  margin-bottom: 20px;
}
</style> 