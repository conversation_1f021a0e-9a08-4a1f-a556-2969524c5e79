package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分类Mapper接口
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 根据父分类ID查询子分类列表
     * @param parentId 父分类ID，如果为null则查询顶级分类
     * @return 子分类列表
     */
    List<Category> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询分类的文章数量
     * @param categoryId 分类ID
     * @return 文章数量
     */
    @Select("SELECT COUNT(*) FROM article WHERE category_id = #{categoryId} AND del_flag = 0")
    int countArticleByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 查询是否存在子分类
     * @param categoryId 分类ID
     * @return 子分类数量
     */
    @Select("SELECT COUNT(*) FROM category WHERE parent_id = #{categoryId}")
    int countChildrenByCategoryId(@Param("categoryId") Long categoryId);
} 