# 文档导航

## 🚀 快速开始
- **[快速开始.md](快速开始.md)** - 5分钟快速启动和验证

## 📁 文档分类

### 📋 项目文档 (project/)
按顺序阅读以下文档：
1. **[项目说明文档.md](project/项目说明文档.md)** - 项目概述、技术栈、功能特性
2. **[项目开发文档.md](project/项目开发文档.md)** - 开发规范、架构设计、API文档
3. **[项目进度.md](project/项目进度.md)** - 开发进度、功能完成情况
4. **[环境配置指南.md](project/环境配置指南.md)** - 开发环境搭建
5. **[系统配置.md](project/系统配置.md)** - 系统配置说明

### 🗄️ 数据库文档 (database/)
- **[blog_system.sql](database/blog_system.sql)** - 完整数据库脚本（一键部署）

### 🚀 部署文档
- **[CentOS7.6完整部署指南.md](CentOS7.6完整部署指南.md)** - CentOS 7.6企业级部署指南（推荐）⭐
- **[部署检查清单.md](部署检查清单.md)** - 部署前后检查清单
- **[部署系统变更说明.md](部署系统变更说明.md)** - 从阿里云Linux3到CentOS 7.6的变更说明

### 🧪 测试文档 (testing/)
按模块选择对应的测试指南：
1. **[测试总览.md](testing/测试总览.md)** - 测试环境和总体说明
2. **[第一阶段测试指南.md](testing/第一阶段测试指南.md)** - 用户认证模块测试
3. **[第二阶段测试指南.md](testing/第二阶段测试指南.md)** - 文章管理模块测试
4. **[第三阶段测试指南.md](testing/第三阶段测试指南.md)** - 分类标签模块测试
5. **[第四阶段测试指南.md](testing/第四阶段测试指南.md)** - 评论系统模块测试
6. **[第五阶段测试指南.md](testing/第五阶段测试指南.md)** - 用户互动、系统管理、邮件模板测试

### 🐛 问题记录 (issues/)
- **[问题记录与解决方案.md](issues/问题记录与解决方案.md)** - 常见问题和解决方案
- **各阶段总结文档** - 开发过程中的问题记录和经验总结

## 📖 使用指南

### 🆕 新用户
1. [快速开始.md](快速开始.md) - 5分钟上手
2. [project/项目说明文档.md](project/项目说明文档.md) - 了解项目

### 🧪 测试人员
1. [快速开始.md](快速开始.md) - 环境准备
2. [testing/测试总览.md](testing/测试总览.md) - 测试指南总览
3. 按需选择具体阶段测试

### 👨‍💻 开发者
1. [project/](project/) - 查看项目文档和配置
2. [issues/](issues/) - 了解问题记录和解决方案

### 🔧 运维人员
1. [project/系统配置.md](project/系统配置.md) - 系统配置
2. [database/](database/) - 数据库部署
3. [CentOS7.6完整部署指南.md](CentOS7.6完整部署指南.md) - CentOS 7.6服务器完整部署
4. [部署检查清单.md](部署检查清单.md) - 部署前后检查清单
5. [快速开始.md](快速开始.md) - 验证部署

---

## 🎛️ 管理后台功能

### 仪表盘统计
- **总体统计**：文章总数、用户总数、评论总数、总浏览量
- **今日统计**：今日新增文章、今日新增用户、今日新增评论、今日浏览量
- **最近数据**：最近发布的文章列表、最近的评论列表
- **权限控制**：仅管理员可访问

### 数据统计说明
- **用户状态**：status=1表示正常用户，status=0表示禁用用户
- **文章状态**：status=1表示已发布，status=0表示草稿
- **评论状态**：status=1表示已审核，status=0表示待审核
- **时间统计**：基于当日00:00:00到23:59:59的时间范围

---

**文档结构**：按功能分类，主目录简洁清晰