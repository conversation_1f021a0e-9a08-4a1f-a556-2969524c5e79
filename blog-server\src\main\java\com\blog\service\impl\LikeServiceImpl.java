package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.blog.entity.Article;
import com.blog.entity.ArticleLike;
import com.blog.mapper.ArticleLikeMapper;
import com.blog.mapper.ArticleMapper;
import com.blog.service.LikeService;
import com.blog.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 点赞服务实现类
 */
@Service
public class LikeServiceImpl implements LikeService {

    @Autowired
    private ArticleLikeMapper articleLikeMapper;
    
    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private NotificationService notificationService;

    @Override
    @Transactional
    public boolean likeArticle(Long articleId, Long userId) {
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已点赞
        if (hasLikedArticle(articleId, userId)) {
            return true; // 已点赞，直接返回成功
        }
        
        // 创建点赞记录
        ArticleLike like = new ArticleLike();
        like.setArticleId(articleId);
        like.setUserId(userId);
        like.setCreateTime(new Date());
        
        // 插入点赞记录
        int result = articleLikeMapper.insert(like);
        
        if (result > 0) {
            // 更新文章点赞数
            Article updateArticle = new Article();
            updateArticle.setId(articleId);
            updateArticle.setLikeCount(getArticleLikeCount(articleId));
            articleMapper.updateById(updateArticle);

            // 发送点赞通知
            notificationService.sendLikeNotification(articleId, userId);

            return true;
        }
        
        return false;
    }

    @Override
    @Transactional
    public boolean unlikeArticle(Long articleId, Long userId) {
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已点赞
        if (!hasLikedArticle(articleId, userId)) {
            return true; // 未点赞，直接返回成功
        }
        
        // 构建删除条件
        LambdaQueryWrapper<ArticleLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ArticleLike::getArticleId, articleId)
                .eq(ArticleLike::getUserId, userId);
        
        // 删除点赞记录
        int result = articleLikeMapper.delete(queryWrapper);
        
        if (result > 0) {
            // 更新文章点赞数
            Article updateArticle = new Article();
            updateArticle.setId(articleId);
            updateArticle.setLikeCount(getArticleLikeCount(articleId));
            articleMapper.updateById(updateArticle);
            return true;
        }
        
        return false;
    }

    @Override
    public boolean hasLikedArticle(Long articleId, Long userId) {
        // 查询用户点赞状态
        int count = articleLikeMapper.selectUserLikeStatus(articleId, userId);
        return count > 0;
    }

    @Override
    public int getArticleLikeCount(Long articleId) {
        // 查询文章点赞数
        return articleLikeMapper.selectLikeCount(articleId);
    }
} 