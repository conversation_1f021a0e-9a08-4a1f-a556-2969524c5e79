import request from '@/utils/request'

/**
 * 互动相关API
 */

/**
 * 获取文章互动状态（点赞、收藏状态及数量）
 * @param {Number} articleId 文章ID
 * @returns {Promise}
 */
export function getInteractionStatus(articleId) {
  return request({
    url: `/interaction/status/${articleId}`,
    method: 'get'
  })
}

/**
 * 添加互动（点赞或收藏）
 * @param {Object} data 包含articleId和type(like或collect)
 * @returns {Promise}
 */
export function addInteraction(data) {
  return request({
    url: '/interaction/add',
    method: 'post',
    data
  })
}

/**
 * 取消互动（取消点赞或取消收藏）
 * @param {Object} data 包含articleId和type(like或collect)
 * @returns {Promise}
 */
export function cancelInteraction(data) {
  return request({
    url: '/interaction/cancel',
    method: 'post',
    data
  })
}

/**
 * 获取用户收藏的文章列表
 * @returns {Promise}
 */
export function getUserCollections() {
  console.log('调用收藏列表API')
  return request({
    url: '/interaction/collections',
    method: 'get'
  }).then(response => {
    console.log('收藏列表API响应:', JSON.stringify(response))
    return response
  })
} 