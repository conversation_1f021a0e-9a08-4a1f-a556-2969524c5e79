package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.common.utils.SecurityUtils;
import com.blog.service.NotificationService;
import com.blog.vo.NotificationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知控制器
 */
@RestController
@RequestMapping("/notifications")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SecurityUtils securityUtils;

    /**
     * 获取当前用户的通知列表
     * @param isRead 是否已读（0-未读，1-已读，null-全部）
     * @param limit 限制数量，默认20
     * @return 通知列表
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public Result<List<NotificationVO>> getNotifications(
            @RequestParam(required = false) Integer isRead,
            @RequestParam(defaultValue = "20") Integer limit) {
        Long userId = securityUtils.getCurrentUserId();
        List<NotificationVO> notifications = notificationService.getUserNotifications(userId, isRead, limit);
        return Result.success(notifications);
    }

    /**
     * 获取未读通知数量
     * @return 未读通知数量
     */
    @GetMapping("/unread-count")
    @PreAuthorize("isAuthenticated()")
    public Result<Map<String, Object>> getUnreadCount() {
        Long userId = securityUtils.getCurrentUserId();
        int unreadCount = notificationService.getUnreadCount(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("unreadCount", unreadCount);
        
        return Result.success(result);
    }

    /**
     * 标记通知为已读
     * @param notificationIds 通知ID列表
     * @return 操作结果
     */
    @PutMapping("/mark-read")
    @PreAuthorize("isAuthenticated()")
    public Result<String> markAsRead(@RequestBody List<Long> notificationIds) {
        Long userId = securityUtils.getCurrentUserId();
        boolean success = notificationService.markAsRead(userId, notificationIds);
        
        if (success) {
            return Result.success("标记成功");
        } else {
            return Result.failed("标记失败");
        }
    }

    /**
     * 标记所有通知为已读
     * @return 操作结果
     */
    @PutMapping("/mark-all-read")
    @PreAuthorize("isAuthenticated()")
    public Result<String> markAllAsRead() {
        Long userId = securityUtils.getCurrentUserId();
        boolean success = notificationService.markAllAsRead(userId);
        
        if (success) {
            return Result.success("全部标记成功");
        } else {
            return Result.failed("标记失败");
        }
    }

    /**
     * 删除通知
     * @param notificationIds 通知ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @PreAuthorize("isAuthenticated()")
    public Result<String> deleteNotifications(@RequestBody List<Long> notificationIds) {
        Long userId = securityUtils.getCurrentUserId();
        boolean success = notificationService.deleteNotifications(userId, notificationIds);
        
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.failed("删除失败");
        }
    }
}
