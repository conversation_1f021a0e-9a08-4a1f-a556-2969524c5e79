<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.CategoryMapper">

    <!-- 根据父分类ID查询子分类列表 -->
    <select id="selectByParentId" resultType="com.blog.entity.Category">
        SELECT 
            id, name, description, `order`, parent_id, create_time, update_time
        FROM 
            category
        <where>
            <if test="parentId != null">
                parent_id = #{parentId}
            </if>
            <if test="parentId == null">
                parent_id IS NULL
            </if>
        </where>
        ORDER BY `order` ASC
    </select>

</mapper> 