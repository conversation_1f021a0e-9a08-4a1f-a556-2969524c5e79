package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.Notification;
import com.blog.vo.NotificationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知Mapper接口
 */
@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 获取用户的通知列表（包含发送者信息和资源信息）
     * @param userId 用户ID
     * @param isRead 是否已读（null表示查询全部）
     * @param limit 限制数量
     * @return 通知VO列表
     */
    List<NotificationVO> selectUserNotifications(@Param("userId") Long userId, 
                                               @Param("isRead") Integer isRead, 
                                               @Param("limit") Integer limit);

    /**
     * 获取用户未读通知数量
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int countUnreadNotifications(@Param("userId") Long userId);

    /**
     * 批量标记通知为已读
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 更新的记录数
     */
    int markNotificationsAsRead(@Param("userId") Long userId, 
                               @Param("notificationIds") List<Long> notificationIds);

    /**
     * 标记用户所有通知为已读
     * @param userId 用户ID
     * @return 更新的记录数
     */
    int markAllNotificationsAsRead(@Param("userId") Long userId);

    /**
     * 删除用户的通知
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 删除的记录数
     */
    int deleteUserNotifications(@Param("userId") Long userId, 
                               @Param("notificationIds") List<Long> notificationIds);

    /**
     * 检查是否存在相同的通知（防止重复通知）
     * @param userId 接收者用户ID
     * @param fromUserId 发送者用户ID
     * @param type 通知类型
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @return 通知数量
     */
    int countSimilarNotifications(@Param("userId") Long userId,
                                 @Param("fromUserId") Long fromUserId,
                                 @Param("type") String type,
                                 @Param("resourceId") Long resourceId,
                                 @Param("resourceType") String resourceType);
}
