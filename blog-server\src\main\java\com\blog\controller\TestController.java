package com.blog.controller;

import com.blog.common.api.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 匿名访问接口
     */
    @GetMapping("/anonymous")
    public Result<?> anonymous() {
        return Result.success("匿名访问成功");
    }

    /**
     * 需要认证的接口
     */
    @GetMapping("/authenticated")
    public Result<?> authenticated() {
        return Result.success("认证访问成功");
    }
} 