package com.blog.service;

/**
 * 点赞服务接口
 */
public interface LikeService {
    
    /**
     * 点赞文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 点赞成功返回true，否则返回false
     */
    boolean likeArticle(Long articleId, Long userId);
    
    /**
     * 取消点赞文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 取消点赞成功返回true，否则返回false
     */
    boolean unlikeArticle(Long articleId, Long userId);
    
    /**
     * 检查用户是否已点赞文章
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 已点赞返回true，否则返回false
     */
    boolean hasLikedArticle(Long articleId, Long userId);
    
    /**
     * 获取文章点赞数量
     * @param articleId 文章ID
     * @return 点赞数量
     */
    int getArticleLikeCount(Long articleId);
} 