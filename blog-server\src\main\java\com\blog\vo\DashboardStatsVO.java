package com.blog.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 仪表盘统计数据视图对象
 */
@Data
public class DashboardStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文章总数
     */
    private Long articleCount;

    /**
     * 用户总数
     */
    private Long userCount;

    /**
     * 评论总数
     */
    private Long commentCount;

    /**
     * 总浏览量
     */
    private Long viewCount;

    /**
     * 今日新增文章数
     */
    private Long todayArticleCount;

    /**
     * 今日新增用户数
     */
    private Long todayUserCount;

    /**
     * 今日新增评论数
     */
    private Long todayCommentCount;

    /**
     * 今日浏览量
     */
    private Long todayViewCount;
}
