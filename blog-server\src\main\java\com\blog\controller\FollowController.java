package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.common.utils.SecurityUtils;
import com.blog.dto.FollowDTO;
import com.blog.enums.FollowResultType;
import com.blog.mapper.UserMapper;
import com.blog.service.FollowService;
import com.blog.vo.UserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关注控制器
 */
@RestController
@RequestMapping("/follow")
public class FollowController {
    
    @Autowired
    private FollowService followService;
    
    @Autowired
    private SecurityUtils securityUtils;

    @Autowired
    private UserMapper userMapper;
    
    /**
     * 关注用户
     */
    @PostMapping("")
    @PreAuthorize("isAuthenticated()")
    public Result<?> follow(@RequestBody FollowDTO followDTO) {
        Long currentUserId = securityUtils.getCurrentUserId();
        FollowResultType resultType = followService.follow(currentUserId, followDTO.getUserId());

        if (resultType == FollowResultType.SUCCESS) {
            return Result.success(resultType.getMessage());
        } else {
            return Result.failed(resultType);
        }
    }
    
    /**
     * 取消关注用户
     */
    @PostMapping("/unfollow")
    @PreAuthorize("isAuthenticated()")
    public Result<?> unfollow(@RequestBody FollowDTO followDTO) {
        Long currentUserId = securityUtils.getCurrentUserId();
        FollowResultType resultType = followService.unfollow(currentUserId, followDTO.getUserId());
        
        if (resultType == FollowResultType.SUCCESS) {
            return Result.success(resultType.getMessage());
        } else {
            return Result.failed(resultType);
        }
    }
    
    /**
     * 获取关注状态
     */
    @GetMapping("/status/{userId}")
    public Result<?> getRelationship(@PathVariable Long userId) {
        // 校验用户是否存在
        if (userMapper.selectById(userId) == null) {
            return Result.failed(FollowResultType.USER_NOT_FOUND);
        }

        Map<String, Object> result = new HashMap<>();
        
        // 默认未登录状态
        boolean isFollowing = false;
        
        // 获取关注数和粉丝数
        int followingCount = followService.getFollowingCount(userId);
        int followerCount = followService.getFollowerCount(userId);
        
        // 如果用户已登录，查询关注状态
        Long currentUserId = securityUtils.getCurrentUserId();
        if (currentUserId != null) {
            isFollowing = followService.isFollowing(currentUserId, userId);
        }
        
        result.put("isFollowing", isFollowing);
        result.put("followingCount", followingCount);
        result.put("followerCount", followerCount);
        
        return Result.success(result);
    }
    
    /**
     * 获取当前用户的关注列表
     */
    @GetMapping("/following")
    @PreAuthorize("isAuthenticated()")
    public Result<List<UserVO>> getFollowingList() {
        Long currentUserId = securityUtils.getCurrentUserId();
        List<UserVO> followingList = followService.getFollowingList(currentUserId);
        return Result.success(followingList);
    }
    
    /**
     * 获取当前用户的粉丝列表
     */
    @GetMapping("/followers")
    @PreAuthorize("isAuthenticated()")
    public Result<List<UserVO>> getFollowerList() {
        Long currentUserId = securityUtils.getCurrentUserId();
        List<UserVO> followerList = followService.getFollowerList(currentUserId);
        return Result.success(followerList);
    }
    
    /**
     * 获取指定用户的关注列表（公共接口）
     */
    @GetMapping("/user/{userId}/following")
    public Result<List<UserVO>> getUserFollowingList(@PathVariable Long userId) {
        // 校验用户是否存在
        if (userMapper.selectById(userId) == null) {
            return Result.failed(FollowResultType.USER_NOT_FOUND);
        }
        List<UserVO> followingList = followService.getFollowingList(userId);
        return Result.success(followingList);
    }
    
    /**
     * 获取指定用户的粉丝列表（公共接口）
     */
    @GetMapping("/user/{userId}/followers")
    public Result<List<UserVO>> getUserFollowerList(@PathVariable Long userId) {
        // 校验用户是否存在
        if (userMapper.selectById(userId) == null) {
            return Result.failed(FollowResultType.USER_NOT_FOUND);
        }
        List<UserVO> followerList = followService.getFollowerList(userId);
        return Result.success(followerList);
    }
} 