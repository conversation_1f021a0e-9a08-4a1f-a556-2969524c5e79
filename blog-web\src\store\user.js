import { defineStore } from 'pinia'
import { login, register, getUserInfo as getUserInfoApi } from '@/api/auth'
import { getToken, setToken, removeToken, setUserInfo, getUserInfo, removeUserInfo, clearAuth } from '@/utils/auth'
import router from '@/router'

export const useUserStore = defineStore('user', {
  state: () => {
    // 从cookie中获取初始用户信息
    const userInfo = getUserInfo()
    return {
      token: getToken() || '',
      userId: userInfo?.userId || '',
      username: userInfo?.username || '',
      nickname: userInfo?.nickname || '',
      avatar: userInfo?.avatar || '',
      role: userInfo?.role || '',
      isLogin: !!getToken()
    }
  },
  
  getters: {
    isAdmin: (state) => state.role === 'admin',
    userInfo: (state) => {
      return {
        userId: state.userId,
        username: state.username,
        nickname: state.nickname,
        avatar: state.avatar,
        role: state.role
      }
    }
  },
  
  actions: {
    // 登录
    async login(loginData) {
      try {
        const res = await login(loginData)
        const { token, tokenHead, userId, username, nickname, avatar, role } = res.data
        
        // 保存token，确保格式正确 - 不在这里添加前缀，而是在请求拦截器中添加
        setToken(token)
        this.token = token
        
        // 保存用户信息
        this.userId = userId
        this.username = username
        this.nickname = nickname
        this.avatar = avatar
        this.role = role
        this.isLogin = true
        
        // 将用户信息保存到cookie
        setUserInfo(res.data)
        
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 注册
    async register(registerData) {
      try {
        const res = await register(registerData)
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getInfo() {
      try {
        // 先检查是否有token，没有token不发起请求
        if (!this.token && !getToken()) {
          return Promise.reject(new Error('没有token，请先登录'))
        }
        
        const res = await getUserInfoApi()
        const { id, username, nickname, avatar, role } = res.data
        
        this.userId = id
        this.username = username
        this.nickname = nickname
        this.avatar = avatar
        this.role = role
        this.isLogin = true
        
        // 更新存储在cookie中的用户信息
        setUserInfo(res.data)
        
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 退出登录
    logout() {
      // 清除认证信息
      clearAuth()
      
      // 重置状态
      this.token = ''
      this.userId = ''
      this.username = ''
      this.nickname = ''
      this.avatar = ''
      this.role = ''
      this.isLogin = false
      
      // 跳转到登录页
      router.push('/login')
    },
    
    // 重置状态
    resetState() {
      removeToken()
      removeUserInfo()
      
      this.token = ''
      this.userId = ''
      this.username = ''
      this.nickname = ''
      this.avatar = ''
      this.role = ''
      this.isLogin = false
    }
  }
}) 