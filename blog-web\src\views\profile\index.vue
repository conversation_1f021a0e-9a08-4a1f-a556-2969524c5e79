<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <div class="profile-header">
        <div class="avatar-container">
          <el-avatar :size="100" :src="userInfo.avatar || defaultAvatar" @click="showAvatarUpload"></el-avatar>
          <div class="avatar-uploader" v-if="isEditMode">
            <el-upload
              class="avatar-upload"
              :show-file-list="false"
              action="/api/upload/avatar"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              :headers="uploadHeaders"
            >
              <el-button size="small" type="primary">
                <el-icon><Upload /></el-icon>更换头像
              </el-button>
            </el-upload>
          </div>
        </div>
        <div class="user-info">
          <h1>{{ userInfo.nickname || userInfo.username }}</h1>
          <p>{{ userInfo.email || '未设置邮箱' }}</p>
          <p>角色：{{ userInfo.role === 'admin' ? '管理员' : '普通用户' }}</p>
          <p>注册时间：{{ formatDate(userInfo.createTime) }}</p>
          <div class="action-buttons">
            <el-button type="primary" @click="toggleEditMode" v-if="!isEditMode">
              编辑资料
            </el-button>
            <template v-else>
              <el-button type="success" @click="saveUserInfo">保存</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </template>
            <el-button type="warning" @click="showPasswordDialog">修改密码</el-button>
          </div>
        </div>
      </div>
      
      <el-divider></el-divider>
      
      <div class="profile-form" v-if="isEditMode">
        <el-form :model="editForm" :rules="rules" label-width="80px" ref="editFormRef">
          <el-form-item label="用户名">
            <el-input v-model="editForm.username" disabled></el-input>
          </el-form-item>
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="editForm.nickname"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="editForm.email"></el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="profile-detail" v-else>
        <div class="detail-item">
          <span class="detail-label">用户名</span>
          <span class="detail-value">{{ userInfo.username }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">昵称</span>
          <span class="detail-value">{{ userInfo.nickname || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">邮箱</span>
          <span class="detail-value">{{ userInfo.email || '未设置' }}</span>
        </div>
      </div>
    </el-card>
    
    <!-- 密码修改对话框 -->
    <el-dialog title="修改密码" v-model="passwordDialogVisible" width="500px" center>
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateUserPassword">确认修改</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getUserInfo, updateUserInfo, updatePassword, updateAvatar } from '@/api/user'
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'
import { Upload } from '@element-plus/icons-vue'

export default {
  name: 'UserProfile',
  components: {
    Upload
  },
  setup() {
    const userStore = useUserStore()
    const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    const userInfo = ref({})
    const isEditMode = ref(false)
    const editFormRef = ref(null)
    const passwordFormRef = ref(null)
    const passwordDialogVisible = ref(false)
    
    // 编辑表单
    const editForm = reactive({
      username: '',
      nickname: '',
      email: ''
    })
    
    // 密码表单
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    // 表单校验规则
    const rules = {
      nickname: [
        { min: 2, max: 20, message: '昵称长度在2-20个字符之间', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    }
    
    // 密码表单校验规则
    const passwordRules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请再次输入新密码', trigger: 'blur' },
        { 
          validator: (rule, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          }, 
          trigger: 'blur' 
        }
      ]
    }
    
    // 上传头像的请求头
    const uploadHeaders = {
      Authorization: getToken()
    }
    
    // 获取用户信息
    const fetchUserInfo = async () => {
      try {
        const res = await getUserInfo()
        if (res.code === 200) {
          userInfo.value = res.data
          Object.assign(editForm, {
            username: userInfo.value.username,
            nickname: userInfo.value.nickname,
            email: userInfo.value.email
          })
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    }
    
    // 切换编辑模式
    const toggleEditMode = () => {
      isEditMode.value = true
      Object.assign(editForm, {
        username: userInfo.value.username,
        nickname: userInfo.value.nickname,
        email: userInfo.value.email
      })
    }
    
    // 取消编辑
    const cancelEdit = () => {
      isEditMode.value = false
      editFormRef.value.resetFields()
    }
    
    // 保存用户信息
    const saveUserInfo = async () => {
      editFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const res = await updateUserInfo(editForm)
            if (res.code === 200) {
              ElMessage.success('资料更新成功')
              isEditMode.value = false
              fetchUserInfo()
              // 更新用户信息
              userStore.getInfo()
            }
          } catch (error) {
            console.error('更新用户信息失败', error)
          }
        }
      })
    }
    
    // 显示修改密码对话框
    const showPasswordDialog = () => {
      passwordDialogVisible.value = true
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
    
    // 更新密码
    const updateUserPassword = async () => {
      passwordFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const res = await updatePassword(passwordForm)
            if (res.code === 200) {
              ElMessage.success('密码修改成功，请重新登录')
              passwordDialogVisible.value = false
              // 退出登录
              setTimeout(() => {
                userStore.logout()
              }, 1500)
            }
          } catch (error) {
            // 全局拦截器已处理错误提示，此处仅在控制台记录错误
            console.error('修改密码失败', error)
          }
        }
      })
    }
    
    // 上传头像前的校验
    const beforeAvatarUpload = (file) => {
      const isImage = /\.(jpeg|jpg|gif|png)$/.test(file.name.toLowerCase())
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('上传头像图片只能是 JPG/PNG/GIF 格式!')
      }
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 2MB!')
      }
      
      return isImage && isLt2M
    }
    
    // 头像上传成功的回调
    const handleAvatarSuccess = async (res, file) => {
      if (res.code === 200) {
        try {
          const avatarUrl = res.data
          const updateRes = await updateAvatar({ avatar: avatarUrl })
          if (updateRes.code === 200) {
            userInfo.value.avatar = avatarUrl
            ElMessage.success('头像更新成功')
            // 更新用户信息
            userStore.getInfo()
          }
        } catch (error) {
          console.error('更新头像失败', error)
        }
      }
    }
    
    // 显示头像上传
    const showAvatarUpload = () => {
      if (isEditMode.value) {
        // 在编辑模式下点击头像触发上传
        document.querySelector('.avatar-upload .el-upload input').click()
      }
    }
    
    // 日期格式化
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
    
    onMounted(() => {
      fetchUserInfo()
    })
    
    return {
      userInfo,
      isEditMode,
      editForm,
      rules,
      editFormRef,
      passwordForm,
      passwordRules,
      passwordFormRef,
      passwordDialogVisible,
      defaultAvatar,
      uploadHeaders,
      toggleEditMode,
      cancelEdit,
      saveUserInfo,
      showPasswordDialog,
      updateUserPassword,
      beforeAvatarUpload,
      handleAvatarSuccess,
      showAvatarUpload,
      formatDate
    }
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.profile-card {
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  margin-bottom: 20px;
}

.avatar-container {
  margin-right: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-uploader {
  margin-top: 10px;
}

.user-info {
  flex: 1;
}

.user-info h1 {
  margin-top: 0;
  margin-bottom: 10px;
}

.user-info p {
  margin: 5px 0;
  color: #666;
}

.action-buttons {
  margin-top: 15px;
}

.profile-form {
  max-width: 500px;
  margin: 20px auto;
}

.detail-item {
  display: flex;
  margin: 15px 0;
}

.detail-label {
  width: 80px;
  color: #888;
  text-align: right;
  padding-right: 20px;
}

.detail-value {
  flex: 1;
}

.el-avatar {
  cursor: pointer;
}
</style> 