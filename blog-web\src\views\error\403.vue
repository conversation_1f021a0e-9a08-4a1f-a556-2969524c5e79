<template>
  <div class="access-denied">
    <h1>403</h1>
    <p>无权访问</p>
    <p class="description">抱歉，您没有权限访问此页面。</p>
    <router-link to="/">返回首页</router-link>
  </div>
</template>

<script>
export default {
  name: 'AccessDenied'
}
</script>

<style scoped>
.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

h1 {
  font-size: 6rem;
  margin-bottom: 1rem;
  color: #f56c6c;
}

p {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.description {
  font-size: 1rem;
  color: #606266;
  margin-bottom: 2rem;
}

a {
  color: #409eff;
  text-decoration: none;
}
</style> 