import request from '@/utils/request'

/**
 * 获取文章评论列表
 * @param {number} articleId 文章ID
 * @returns {Promise} 评论列表
 */
export function getArticleComments(articleId) {
  return request({
    url: `/comments/article/${articleId}`,
    method: 'get'
  })
}

/**
 * 获取当前用户的评论列表
 * @returns {Promise} 评论列表
 */
export function getCurrentUserComments() {
  return request({
    url: '/comments/user',
    method: 'get'
  })
}

/**
 * 发表评论
 * @param {Object} data 评论数据
 * @returns {Promise} 评论ID
 */
export function createComment(data) {
  return request({
    url: '/comments',
    method: 'post',
    data
  })
}

/**
 * 删除评论
 * @param {number} id 评论ID
 * @returns {Promise} 删除结果
 */
export function deleteComment(id) {
  return request({
    url: `/comments/${id}`,
    method: 'delete'
  })
}

/**
 * 获取指定用户的评论列表 (管理员接口)
 * @param {number} userId 用户ID
 * @returns {Promise} 评论列表
 */
export function getUserComments(userId) {
  return request({
    url: `/comments/user/${userId}`,
    method: 'get'
  })
}

/**
 * 更新评论状态 (管理员接口)
 * @param {number} id 评论ID
 * @param {number} status 状态 (0-未审核，1-已发布)
 * @returns {Promise} 更新结果
 */
export function updateCommentStatus(id, status) {
  return request({
    url: `/comments/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}

/**
 * 获取所有评论列表 (管理员接口)
 * @returns {Promise} 所有评论列表
 */
export function getAllComments() {
  return request({
    url: '/comments/all',
    method: 'get'
  })
} 