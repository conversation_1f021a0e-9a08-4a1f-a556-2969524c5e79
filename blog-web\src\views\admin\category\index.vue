<template>
  <div class="category-management-container" data-cy="category-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>分类管理</span>
        </div>
      </template>

      <div class="toolbar">
        <el-button type="primary" :icon="Plus" @click="handleOpenDialog()" data-cy="add-category-btn">新增分类</el-button>
      </div>

      <el-table :data="categoryList" style="width: 100%" v-loading="loading" data-cy="category-table">
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="createTime" label="创建时间">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" :icon="Edit" circle @click="handleOpenDialog(scope.row)" data-cy="edit-category-btn" />
            <el-button type="danger" :icon="Delete" circle @click="handleDelete(scope.row)" data-cy="delete-category-btn" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="30%"
      @close="resetForm"
      data-cy="category-dialog"
    >
      <el-form
        ref="formRef"
        :model="categoryForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" data-cy="category-name-input" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" data-cy="cancel-btn">取消</el-button>
          <el-button type="primary" @click="handleSubmit" data-cy="confirm-btn">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getCategories, addCategory, updateCategory, deleteCategory } from '@/api/category';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { formatDateTime } from '@/utils/format';

// 加载状态
const loading = ref(true);

// 表格数据
const categoryList = ref([]);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');

// 表单相关
const formRef = ref(null);
const categoryForm = reactive({
  id: null,
  name: '',
  order: 0,
});
const formRules = reactive({
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
});

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true;
  try {
    const res = await getCategories();
    categoryList.value = res.data;
  } catch (error) {
    console.error('获取分类列表失败', error);
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  fetchCategories();
});

// 重置表单
const resetForm = () => {
  categoryForm.id = null;
  categoryForm.name = '';
  categoryForm.order = 0;
  formRef.value?.resetFields();
};

// 打开新增/编辑对话框
const handleOpenDialog = (category) => {
  resetForm();
  if (category && category.id) {
    // 编辑
    dialogTitle.value = '编辑分类';
    Object.assign(categoryForm, category);
  } else {
    // 新增
    dialogTitle.value = '新增分类';
  }
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (category) => {
  ElMessageBox.confirm(`确定要删除分类 "${category.name}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteCategory(category.id);
      ElMessage.success('删除成功');
      fetchCategories(); // 重新加载数据
    } catch (error) {
      console.error('删除失败', error);
      // 如果有具体错误信息，显示给用户
      if (error.response && error.response.status === 409) {
        ElMessage.error(`删除失败: ${error.response.data.message || '该分类下有关联数据，无法删除'}`);
      } else {
        ElMessage.error('删除失败，请稍后重试');
      }
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 处理表单提交
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const isEdit = !!categoryForm.id;
      try {
        const payload = {
          name: categoryForm.name,
          order: categoryForm.order,
        };
        if (isEdit) {
          payload.id = categoryForm.id;
          await updateCategory(categoryForm.id, payload);
        } else {
          await addCategory(payload);
        }
        ElMessage.success(isEdit ? '更新成功' : '新增成功');
        dialogVisible.value = false;
        fetchCategories();
      } catch (error) {
        console.error(isEdit ? '更新失败' : '新增失败', error);
        // 业务异常提示由request拦截器处理
      }
    }
  });
};
</script>

<style scoped>
.category-management-container {
  padding: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.toolbar {
  margin-bottom: 20px;
}
</style> 