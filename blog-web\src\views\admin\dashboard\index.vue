<template>
  <div class="dashboard" v-loading="loading">
    <div class="header">
      <h1>仪表盘</h1>
      <el-button type="primary" @click="fetchDashboardData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 总体统计 -->
    <div class="stat-cards">
      <h2>总体统计</h2>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-title">文章总数</div>
            <div class="stat-value">{{ stats.articleCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-title">用户总数</div>
            <div class="stat-value">{{ stats.userCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-icon">💬</div>
            <div class="stat-title">评论总数</div>
            <div class="stat-value">{{ stats.commentCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-icon">👁️</div>
            <div class="stat-title">总浏览量</div>
            <div class="stat-value">{{ stats.viewCount || 0 }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 今日统计 -->
    <div class="stat-cards">
      <h2>今日统计</h2>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card today-card">
            <div class="stat-icon">📝</div>
            <div class="stat-title">今日新增文章</div>
            <div class="stat-value">{{ stats.todayArticleCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card today-card">
            <div class="stat-icon">👥</div>
            <div class="stat-title">今日新增用户</div>
            <div class="stat-value">{{ stats.todayUserCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card today-card">
            <div class="stat-icon">💬</div>
            <div class="stat-title">今日新增评论</div>
            <div class="stat-value">{{ stats.todayCommentCount || 0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card today-card">
            <div class="stat-icon">👁️</div>
            <div class="stat-title">今日浏览量</div>
            <div class="stat-value">{{ stats.todayViewCount || 0 }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 最近文章 -->
    <div class="recent-section">
      <div class="section-header">
        <h2>最近文章</h2>
        <el-button type="text" @click="$router.push('/admin/article')">查看全部</el-button>
      </div>
      <el-table :data="recentArticles" border style="width: 100%" empty-text="暂无数据">
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="categoryName" label="分类" width="120"></el-table-column>
        <el-table-column prop="author" label="作者" width="120"></el-table-column>
        <el-table-column prop="viewCount" label="浏览量" width="100" align="center"></el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '已发布' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewArticle(scope.row)">查看</el-button>
            <el-button size="small" @click="editArticle(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 最近评论 -->
    <div class="recent-section">
      <div class="section-header">
        <h2>最近评论</h2>
        <el-button type="text" @click="$router.push('/admin/comment')">查看全部</el-button>
      </div>
      <el-table :data="recentComments" border style="width: 100%" empty-text="暂无数据">
        <el-table-column prop="content" label="评论内容" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="username" label="评论者" width="120"></el-table-column>
        <el-table-column prop="articleTitle" label="文章标题" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="评论时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewComment(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { getDashboardOverview } from '@/api/dashboard'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    const loading = ref(false)

    const stats = reactive({
      articleCount: 0,
      userCount: 0,
      commentCount: 0,
      viewCount: 0,
      todayArticleCount: 0,
      todayUserCount: 0,
      todayCommentCount: 0,
      todayViewCount: 0
    })

    const recentArticles = ref([])
    const recentComments = ref([])

    // 获取仪表盘数据
    const fetchDashboardData = async () => {
      loading.value = true
      try {
        const res = await getDashboardOverview()
        if (res.code === 200) {
          // 更新统计数据
          Object.assign(stats, res.data.stats)

          // 更新最近文章
          recentArticles.value = res.data.recentArticles || []

          // 更新最近评论
          recentComments.value = res.data.recentComments || []
        } else {
          ElMessage.error(res.message || '获取仪表盘数据失败')
        }
      } catch (error) {
        console.error('获取仪表盘数据失败', error)
        ElMessage.error('获取仪表盘数据失败')
      } finally {
        loading.value = false
      }
    }

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 查看文章
    const viewArticle = (article) => {
      window.open(`/article/${article.id}`, '_blank')
    }

    // 查看评论
    const viewComment = (comment) => {
      window.open(`/article/${comment.articleId}`, '_blank')
    }

    // 编辑文章
    const editArticle = (article) => {
      router.push(`/admin/article/edit/${article.id}`)
    }

    onMounted(() => {
      fetchDashboardData()
    })

    return {
      loading,
      stats,
      recentArticles,
      recentComments,
      formatDate,
      viewArticle,
      viewComment,
      editArticle,
      fetchDashboardData
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.stat-cards {
  margin-bottom: 30px;
}

.stat-cards h2 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.stat-card {
  text-align: center;
  padding: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
}

.today-card .stat-value {
  color: #67c23a;
}

.recent-section {
  margin-top: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.section-header .el-button {
  color: #409eff;
}

.section-header .el-button:hover {
  color: #66b1ff;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}
</style>