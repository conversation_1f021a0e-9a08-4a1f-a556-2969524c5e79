package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.SystemSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 系统设置Mapper接口
 */
@Mapper
public interface SystemSettingMapper extends BaseMapper<SystemSetting> {

    /**
     * 根据设置键名获取设置值
     * @param settingKey 设置键名
     * @return 设置值
     */
    @Select("SELECT setting_value FROM system_setting WHERE setting_key = #{settingKey}")
    String getValueByKey(@Param("settingKey") String settingKey);
}
