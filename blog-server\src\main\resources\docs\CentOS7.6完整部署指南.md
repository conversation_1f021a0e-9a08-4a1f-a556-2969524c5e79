# 个人动态博客系统 - CentOS 7.6完整部署指南（MySQL版本）

## 📋 服务器信息
- **服务器IP**: **************
- **SSH端口**: 22
- **用户名**: root
- **密码**: qwer123..
- **系统版本**: CentOS Linux 7.6 (Core)

## ⚠️ CentOS 7.6 特别说明
- CentOS 7.6 已于2024年6月30日停止维护，建议仅用于学习环境
- 生产环境推荐迁移到 Rocky Linux 8/9 或 AlmaLinux 8/9
- 本指南针对CentOS 7.6的特殊配置进行了优化

## 🚀 第一步：连接服务器并安装基础软件

```bash
# 连接服务器
ssh root@************** -p 22
# 输入密码: qwer123..

# 更新系统并安装EPEL仓库
sudo yum update -y
sudo yum install -y epel-release

# 安装JDK 8 (CentOS 7.6兼容版本)
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 设置JAVA_HOME环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 安装MySQL 8.0 (使用项目本身的MySQL数据库)
# 添加MySQL 8.0官方仓库
sudo yum install -y https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL 8.0服务器和客户端
# 如果遇到GPG密钥错误，先导入正确的密钥
sudo rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
sudo rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql

# 清理yum缓存
sudo yum clean all
sudo yum makecache

# 安装MySQL（如果GPG验证失败，可以使用--nogpgcheck参数）
sudo yum install -y mysql-community-server mysql-community-client
# 如果上述命令失败，尝试：
# sudo yum install -y mysql-community-server mysql-community-client --nogpgcheck

# 安装Node.js 16 (使用NodeSource仓库)
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# 安装Git和其他工具
sudo yum install -y git wget unzip vim

# 安装Maven (从Apache官网下载，因为yum版本较老)
cd /opt
sudo wget https://archive.apache.org/dist/maven/maven-3/3.8.8/binaries/apache-maven-3.8.8-bin.tar.gz
sudo tar -xzf apache-maven-3.8.8-bin.tar.gz
sudo mv apache-maven-3.8.8 maven
sudo chown -R root:root /opt/maven

# 配置Maven环境变量
echo 'export MAVEN_HOME=/opt/maven' >> ~/.bashrc
echo 'export PATH=$MAVEN_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 安装编译工具（用于编译Nginx）
sudo yum groupinstall -y "Development Tools"
sudo yum install -y pcre-devel zlib-devel openssl-devel

# 验证安装
java -version
mvn -version
node -v
npm -v
```

## 🛢️ 第二步：配置MySQL 8.0数据库

```bash
# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 如果遇到"Unit not found"错误，说明MySQL服务没有正确安装
# 请检查安装是否成功：
# rpm -qa | grep mysql
# systemctl list-unit-files | grep mysql

# 如果MySQL确实没有安装成功，可以尝试重新安装：
# sudo yum remove -y mysql*
# sudo yum install -y https://dev.mysql.com/get/mysql80-community-release-el7-5.noarch.rpm
# sudo rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
# sudo yum install -y mysql-community-server mysql-community-client --nogpgcheck

# 获取MySQL初始root密码
sudo grep 'temporary password' /var/log/mysqld.log

# 运行MySQL安全配置
sudo mysql_secure_installation
# 输入上面获取的临时密码
# 设置新的root密码为: Blog@123456 (必须使用强密码，包含大小写字母、数字和特殊字符)
# 移除匿名用户: Y
# 禁止root远程登录: n (开发环境允许)
# 移除test数据库: Y
# 重新加载权限表: Y

# 然后修改为简单密码（推荐，与配置文件一致）：
mysql -u root -p
# 输入密码: Blog@123456
SET GLOBAL validate_password.policy=LOW;
SET GLOBAL validate_password.length=4;
ALTER USER 'root'@'localhost' IDENTIFIED BY '12345';
EXIT;

# 验证MySQL安装和配置
mysql -u root -p
# 输入密码: 12345

# 查看MySQL版本
SELECT VERSION();

# 创建数据库 (使用MySQL 8.0标准字符集)
CREATE DATABASE blog_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建远程访问用户（用于DBeaver等工具连接）
CREATE USER 'blog_admin'@'%' IDENTIFIED WITH mysql_native_password BY '12345';
GRANT ALL PRIVILEGES ON *.* TO 'blog_admin'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

# 验证用户创建
SELECT user,host,plugin FROM mysql.user WHERE user IN ('root','blog_admin');

SHOW DATABASES;
EXIT;

# 配置MySQL允许远程连接
sudo vi /etc/my.cnf
# 在[mysqld]部分添加：
# bind-address = 0.0.0.0

# 重启MySQL服务
sudo systemctl restart mysqld

# 配置防火墙开放MySQL端口
sudo systemctl start firewalld
sudo systemctl enable firewalld
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload

# 如果防火墙启动失败（显示"FirewallD is not running"），可以忽略此步骤
# 某些云服务器可能使用其他防火墙管理方式

# 验证MySQL端口监听
netstat -tlnp | grep 3306
```

### 🔧 DBeaver连接配置

完成上述配置后，可以使用DBeaver连接数据库：

**连接信息**：
- 主机：**************
- 端口：3306
- 数据库：blog_system
- 用户名：blog_admin
- 密码：12345

**驱动属性**（解决"Public Key Retrieval is not allowed"错误）：
- `allowPublicKeyRetrieval` = `true`
- `useSSL` = `false`

**常见连接问题解决**：
1. **Host not allowed**: 确保已创建远程用户并配置了bind-address
2. **Public Key Retrieval**: 在驱动属性中添加allowPublicKeyRetrieval=true
3. **连接超时**: 检查防火墙和网络连接

```

## 🌐 第三步：编译安装Nginx

```bash
# 下载nginx源码
cd /tmp
wget http://nginx.org/download/nginx-1.20.2.tar.gz
tar -xzf nginx-1.20.2.tar.gz
cd nginx-1.20.2

# 配置编译选项 (针对CentOS 7.6优化)
./configure \
    --prefix=/etc/nginx \
    --sbin-path=/usr/sbin/nginx \
    --conf-path=/etc/nginx/nginx.conf \
    --error-log-path=/var/log/nginx/error.log \
    --http-log-path=/var/log/nginx/access.log \
    --pid-path=/var/run/nginx.pid \
    --lock-path=/var/run/nginx.lock \
    --http-client-body-temp-path=/var/cache/nginx/client_temp \
    --http-proxy-temp-path=/var/cache/nginx/proxy_temp \
    --http-fastcgi-temp-path=/var/cache/nginx/fastcgi_temp \
    --http-uwsgi-temp-path=/var/cache/nginx/uwsgi_temp \
    --http-scgi-temp-path=/var/cache/nginx/scgi_temp \
    --with-http_ssl_module \
    --with-http_realip_module \
    --with-http_addition_module \
    --with-http_sub_module \
    --with-http_dav_module \
    --with-http_flv_module \
    --with-http_mp4_module \
    --with-http_gunzip_module \
    --with-http_gzip_static_module \
    --with-http_random_index_module \
    --with-http_secure_link_module \
    --with-http_stub_status_module \
    --with-http_auth_request_module \
    --with-file-aio \
    --with-http_v2_module

# 编译和安装
make && sudo make install

# 创建nginx用户
sudo useradd -r -d /var/cache/nginx -s /sbin/nologin nginx

# 创建必要的目录
sudo mkdir -p /var/cache/nginx/client_temp
sudo mkdir -p /var/log/nginx
sudo mkdir -p /etc/nginx/conf.d
```

## 🔧 第四步：配置Nginx服务

```bash
# 创建systemd服务文件 (CentOS 7.6兼容)
sudo cat > /etc/systemd/system/nginx.service << 'EOF'
[Unit]
Description=The nginx HTTP and reverse proxy server
After=network.target remote-fs.target nss-lookup.target

[Service]
Type=forking
PIDFile=/var/run/nginx.pid
ExecStartPre=/usr/sbin/nginx -t
ExecStart=/usr/sbin/nginx
ExecReload=/bin/kill -s HUP $MAINPID
KillSignal=SIGQUIT
TimeoutStopSec=5
KillMode=process
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload
```

## 📂 第五步：创建部署目录

```bash
# 创建部署目录
mkdir -p /opt/blog-system
cd /opt/blog-system

# 创建子目录
mkdir -p backend frontend uploads logs backups

# 设置权限
sudo chmod -R 755 /opt/blog-system
```

## 📤 第六步：本地打包和上传（企业级部署方案）

### 🏗️ 本地打包（推荐方式）

在本地计算机上执行打包：

```bash
# 1. 后端打包
cd blog-server
mvn clean package -DskipTests

# 检查JAR包是否生成成功
ls -la target/blog-server-0.0.1-SNAPSHOT.jar

# 2. 前端打包
cd ../blog-web
npm run build

# Windows PowerShell执行策略问题解决：
# 如果遇到"无法加载文件，因为在此系统上禁止运行脚本"错误：
# 方案1：使用cmd命令 - cmd /c "npm run build"
# 方案2：修改执行策略 - Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
# 方案3：使用cmd窗口而不是PowerShell

# 检查前端构建结果
ls -la dist/

cd ..
```

### 📦 上传构建产物（企业级方案）

```bash
# 1. 创建服务器目录结构
ssh root@************** -p 22 "mkdir -p /opt/blog-system/{backend,frontend/dist,uploads,logs,backups}"

# 首次SSH连接问题解决：
# 如果遇到"The authenticity of host can't be established"提示：
# 1. 输入 yes 接受主机密钥
# 2. 输入服务器密码：qwer123..
# 3. 主机密钥会保存到 ~/.ssh/known_hosts，以后不会再提示

# 如果遇到"Host key verification failed"：
# 可以使用 -o StrictHostKeyChecking=no 参数跳过验证（不推荐）

# 2. 上传后端JAR包
scp -P 22 blog-server/target/blog-server-0.0.1-SNAPSHOT.jar root@**************:/opt/blog-system/backend/

# 文件路径问题解决：
# 如果遇到"No such file or directory"错误：
# 1. 确保在本地计算机上执行scp命令，不是在服务器上
# 2. 确保当前目录是项目根目录（包含blog-server和blog-web文件夹）
# 3. 确保JAR文件已经构建：mvn clean package -DskipTests
# 4. 检查文件是否存在：ls blog-server/target/blog-server-*.jar

# 3. 上传后端配置文件
scp -P 22 blog-server/src/main/resources/application.yml root@**************:/opt/blog-system/backend/

# 4. 上传前端构建结果
scp -r -P 22 blog-web/dist/* root@**************:/opt/blog-system/frontend/dist/

# 注意：数据库表和数据需要手动导入，不在自动部署范围内
```

### 🚀 快速部署脚本（可选）

创建本地部署脚本 `deploy.bat`（Windows）或 `deploy.sh`（Linux/Mac）：

**Windows版本 (deploy.bat)**：
```batch
@echo off
echo 开始部署博客系统...

echo 1. 后端打包...
cd blog-server
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo 后端打包失败！
    pause
    exit /b 1
)

echo 2. 前端打包...
cd ..\blog-web
call npm run build
if %errorlevel% neq 0 (
    echo 前端打包失败！
    pause
    exit /b 1
)

echo 3. 上传文件到服务器...
cd ..
scp -P 22 blog-server\target\blog-server-0.0.1-SNAPSHOT.jar root@**************:/opt/blog-system/backend/
scp -P 22 blog-server\src\main\resources\application.yml root@**************:/opt/blog-system/backend/
scp -r -P 22 blog-web\dist\* root@**************:/opt/blog-system/frontend/dist/

echo 注意：数据库表和数据需要手动导入，不在自动部署范围内

echo 4. 重启服务...
ssh root@************** -p 22 "cd /opt/blog-system/backend && ./restart.sh"

echo 部署完成！
pause
```

**Linux/Mac版本 (deploy.sh)**：
```bash
#!/bin/bash
echo "开始部署博客系统..."

# 1. 后端打包
echo "1. 后端打包..."
cd blog-server
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "后端打包失败！"
    exit 1
fi

# 2. 前端打包
echo "2. 前端打包..."
cd ../blog-web
npm run build
if [ $? -ne 0 ]; then
    echo "前端打包失败！"
    exit 1
fi

# 3. 上传文件
echo "3. 上传文件到服务器..."
cd ..
scp -P 22 blog-server/target/blog-server-0.0.1-SNAPSHOT.jar root@**************:/opt/blog-system/backend/
scp -P 22 blog-server/src/main/resources/application.yml root@**************:/opt/blog-system/backend/
scp -r -P 22 blog-web/dist/* root@**************:/opt/blog-system/frontend/dist/

echo "注意：数据库表和数据需要手动导入，不在自动部署范围内"

# 4. 重启服务
echo "4. 重启服务..."
ssh root@************** -p 22 "cd /opt/blog-system/backend && ./restart.sh"

echo "部署完成！"
```

### 💡 企业级部署优势

**效率提升**：
- 本地打包速度快（开发环境优化）
- 只传输必要文件（JAR包 + 静态资源）
- 服务器资源消耗最小

**稳定性保证**：
- 本地环境稳定，依赖完整
- 避免服务器环境问题
- 减少部署失败概率

**运维友好**：
- 部署过程标准化
- 可以集成到CI/CD流水线
- 便于版本管理和回滚

## 💾 第七步：数据库表和数据导入（手动操作）

### 📋 数据库导入说明

**重要提示**：数据库表结构和初始数据需要您手动导入，部署脚本不会自动执行此操作。

### 🔧 获取数据库脚本

**方法一：使用DBeaver导出完整脚本（推荐）**
1. 连接到现有数据库（如果有）
2. 右键 blog_system 数据库 → 工具 → 导出数据 → SQL
3. 配置导出：包含表结构+数据，输出为单个文件
4. 保存为 `blog_system_complete.sql`

**方法二：使用项目提供的脚本**
- 检查项目中是否有现成的SQL文件
- 通常位于 `docs/database/` 或 `sql/` 目录

**方法三：如果没有现成脚本，需要手动创建基础数据**
```bash
# 导入数据库脚本后，如果config表为空，需要插入基础配置
mysql -u root -p12345 blog_system << 'EOF'
INSERT INTO config (config_key, config_value, config_desc, create_time, update_time) VALUES
('site_name', '个人博客系统', '网站名称', NOW(), NOW()),
('site_description', '一个基于Spring Boot和Vue的个人博客系统', '网站描述', NOW(), NOW()),
('comment_audit', '0', '评论审核：0-不需要审核，1-需要审核', NOW(), NOW()),
('allow_anonymous_comment', '1', '允许匿名评论：0-不允许，1-允许', NOW(), NOW()),
('email_enable', '0', '邮件功能启用：0-禁用，1-启用', NOW(), NOW()),
('theme_color', '#1890ff', '主题颜色', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();
EOF
```

### 🔧 手动导入步骤

请按照以下步骤手动导入数据库：

```bash
# 1. 准备数据库脚本文件
# 确保您已经准备好完整的数据库脚本文件（如 blog_system.sql）

# 2. 连接到MySQL数据库
mysql -u root -p
# 输入密码: 12345

# 3. 选择数据库
USE blog_system;

# 4. 手动执行SQL脚本或使用source命令
# 方式一：使用source命令（推荐）
source /path/to/your/blog_system.sql;

# 方式二：在命令行直接导入
# mysql -u root -p blog_system < /path/to/your/blog_system.sql

# 5. 验证导入结果
SHOW TABLES;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='blog_system';
```

### ✅ 验证导入结果

导入完成后，请验证以下内容：

```bash
# 验证表数量（应该有15个表）
mysql -u root -p -e "SHOW TABLES;" blog_system

# 验证表结构
mysql -u root -p -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='blog_system';"

# 验证管理员用户
mysql -u root -p -e "SELECT id, username, role FROM user;" blog_system

# 验证MySQL版本和字符集
mysql -u root -p -e "SELECT VERSION();"
mysql -u root -p -e "SHOW VARIABLES LIKE 'character_set%';"
```
**输入密码**：12345



## 📊 数据库表结构说明

完整的数据库包含以下15个表：

**核心业务表**：
- `user` - 用户表（包含bio, last_login_ip, last_login_time等完整字段）
- `article` - 文章表（包含完整的文章管理字段）
- `category` - 分类表（包含order_num排序和parent_id层级字段）
- `tag` - 标签表（包含create_time和update_time）
- `comment` - 评论表（包含匿名评论支持等完整字段）

**关联关系表**：
- `article_tag` - 文章标签关联表（复合主键，无id字段）
- `article_like` - 文章点赞表
- `article_collection` - 文章收藏表
- `comment_like` - 评论点赞表
- `user_follow` - 用户关注表

**系统功能表**：
- `config` - 系统配置表
- `system_setting` - 系统设置表
- `email_template` - 邮件模板表
- `notification` - 通知表
- `operation_log` - 操作日志表

**🚀 MySQL 8.0数据库特性**：
- ✅ 使用项目本身的MySQL数据库
- ✅ 完整的MySQL 8.0功能支持
- ✅ 标准的MySQL语法和协议
- ✅ 优秀的性能和稳定性
- ✅ 丰富的企业级功能

## ⚙️ 第八步：配置后端服务

```bash
cd /opt/blog-system/backend

# ⚠️ 重要：创建正确的生产环境配置文件
# 注意：避免YAML中重复的spring键，使用MySQL 8.0标准驱动
cat > application-prod.yml << 'EOF'
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  # 允许循环引用
  main:
    allow-circular-references: true
  # 数据源配置 (MySQL 8.0标准配置)
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 12345
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 初始化大小，最小，最大
      initial-size: 5
      min-idle: 5
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # mvc配置
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher

  # 邮件配置 (注意：放在spring块内，避免重复spring键)
  mail:
    host: smtp.163.com
    port: 465  # 使用SSL端口，避免25端口被封禁
    username: <EMAIL>
    password: CHQNTsxXQQeEvJRa
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
            fallback: false

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.blog.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag  # 逻辑删除字段
      logic-delete-value: 1 # 删除值
      logic-not-delete-value: 0 # 未删除值

# JWT配置
jwt:
  # JWT密钥
  secret: blogSystem123456
  # JWT过期时间（毫秒）
  expiration: 1800000  # 半小时
  # JWT头部
  tokenHeader: Authorization
  # JWT负载中拿到开头
  tokenHead: Bearer

# 文件上传配置
file:
  upload:
    path: ../uploads/  # 使用相对路径，避免路径拼接错误
  access:
    path: /upload/

# 系统配置
system:
  uploadDir: ../uploads  # 使用相对路径，相对于backend目录
  resourceUrl: /upload

# 日志配置
logging:
  file:
    name: /opt/blog-system/logs/blog-server.log
  level:
    com.blog: info
EOF

# 验证JAR包是否已上传
ls -la blog-server-0.0.1-SNAPSHOT.jar

# 验证配置文件语法
python -c "import yaml; yaml.safe_load(open('application-prod.yml'))" 2>/dev/null && echo "✅ YAML语法正确" || echo "❌ YAML语法有误，请检查配置"

# 验证配置文件
ls -la application*.yml
```

## 🚨 常见配置问题及解决方案

### 问题1：YAML重复键错误
**错误信息**：`DuplicateKeyException: found duplicate key spring`
**解决方案**：确保所有spring相关配置都在同一个spring块下，不要出现多个spring:

### 问题2：数据库驱动类找不到
**错误信息**：`ClassNotFoundException: com.mysql.cj.jdbc.Driver`
**解决方案**：确保项目依赖中包含MySQL驱动，配置中使用`com.mysql.cj.jdbc.Driver`

### 问题3：数据库连接失败
**解决方案**：URL中添加`allowPublicKeyRetrieval=true`参数，MySQL 8.0完全支持此参数

## 🚀 第九步：创建后端启动脚本

```bash
cd /opt/blog-system/backend

# 创建启动脚本 (CentOS 7.6优化版本)
cat > start.sh << 'EOF'
#!/bin/bash
# 博客系统后端启动脚本 - CentOS 7.6版本

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请检查JAVA_HOME配置"
    exit 1
fi

# 检查JAR文件是否存在
JAR_FILE="blog-server-0.0.1-SNAPSHOT.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: 未找到JAR文件 $JAR_FILE"
    echo "请确保已从本地上传JAR包到服务器"
    exit 1
fi

# 创建日志目录
mkdir -p /opt/blog-system/logs

# 启动应用
echo "正在启动博客系统后端服务..."
nohup java -jar -Xms512m -Xmx1024m $JAR_FILE --spring.config.location=application-prod.yml > /opt/blog-system/logs/blog-server.log 2>&1 &

# 保存进程ID
echo $! > /opt/blog-system/backend/app.pid
echo "后端服务已启动，PID: $(cat /opt/blog-system/backend/app.pid)"
echo "日志文件: /opt/blog-system/logs/blog-server.log"
echo "可以使用 tail -f /opt/blog-system/logs/blog-server.log 查看启动日志"

# 等待服务启动
echo "等待服务启动中..."
sleep 10

# 检查服务是否启动成功
if pgrep -f "blog-server.*jar" > /dev/null; then
    echo "✅ 后端服务启动成功"
    # 测试健康检查接口
    echo "正在测试服务健康状态..."
    sleep 5
    curl -s http://localhost:8080/api/actuator/health > /dev/null && echo "✅ 服务健康检查通过" || echo "⚠️ 服务可能还在启动中，请稍后再试"
else
    echo "❌ 后端服务启动失败，请检查日志"
    echo "查看错误日志: tail -f /opt/blog-system/logs/blog-server.log"
fi
EOF

# 创建停止脚本
cat > stop.sh << 'EOF'
#!/bin/bash
# 博客系统后端停止脚本

if [ -f /opt/blog-system/backend/app.pid ]; then
    PID=$(cat /opt/blog-system/backend/app.pid)
    echo "正在停止后端服务，PID: $PID"

    # 优雅停止
    kill -15 $PID

    # 等待进程结束
    sleep 5

    # 检查进程是否还在运行
    if kill -0 $PID 2>/dev/null; then
        echo "进程仍在运行，强制终止..."
        kill -9 $PID
    fi

    rm -f /opt/blog-system/backend/app.pid
    echo "后端服务已停止"
else
    echo "找不到PID文件，服务可能未运行"
fi
EOF

# 创建重启脚本
cat > restart.sh << 'EOF'
#!/bin/bash
# 博客系统后端重启脚本

echo "正在重启后端服务..."
sh stop.sh
sleep 3
sh start.sh
echo "后端服务已重启"
EOF

# 创建状态检查脚本
cat > status.sh << 'EOF'
#!/bin/bash
# 博客系统后端状态检查脚本

if [ -f /opt/blog-system/backend/app.pid ]; then
    PID=$(cat /opt/blog-system/backend/app.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "后端服务正在运行，PID: $PID"
        echo "内存使用情况:"
        ps -p $PID -o pid,ppid,cmd,%mem,%cpu --no-headers
        echo ""
        echo "端口监听情况:"
        netstat -tlnp | grep :8080
    else
        echo "后端服务未运行 (PID文件存在但进程不存在)"
        rm -f /opt/blog-system/backend/app.pid
    fi
else
    echo "后端服务未运行 (未找到PID文件)"
fi
EOF

# 添加执行权限
chmod +x start.sh stop.sh restart.sh status.sh
```

## 🌐 第十步：验证前端部署

```bash
# 验证前端文件是否正确上传
cd /opt/blog-system/frontend
ls -la dist/

# 检查关键文件是否存在
ls -la dist/index.html
ls -la dist/assets/

# 验证文件数量（应该有约65个文件）
find dist/ -type f | wc -l

echo "前端文件验证完成"
```

**💡 企业级部署说明**：
- 前端已在本地构建并上传，无需在服务器上安装Node.js依赖
- 只需验证文件上传是否完整
- 大大节省服务器资源和部署时间

## 🔄 第十一步：配置Nginx网站

```bash
# 创建nginx主配置文件 (CentOS 7.6优化版本)
sudo cat > /etc/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# 针对CentOS 7.6优化的事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 包含conf.d目录下的所有配置文件
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 创建博客网站配置文件
sudo cat > /etc/nginx/conf.d/blog.conf << 'EOF'
server {
    listen 80;
    server_name **************;

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # 前端静态文件
    location / {
        root /opt/blog-system/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端API代理
    location /api {
        proxy_pass http://localhost:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 上传文件访问 - 重要：必须在 location /api 之前！
    # 这样 /api/upload 路径会被当作静态文件处理，而不是代理到后端
    location /api/upload {
        alias /opt/blog-system/uploads;
        autoindex off;

        # 安全配置：禁止执行脚本
        location ~* \.(php|jsp|asp|sh)$ {
            deny all;
        }
    }

    # 上传文件访问 - 备用路径
    location /upload {
        alias /opt/blog-system/uploads;
        autoindex off;

        # 安全配置：禁止执行脚本
        location ~* \.(php|jsp|asp|sh)$ {
            deny all;
        }
    }

    # 后端API代理 - 重要：必须在静态文件location之后！
    # 否则 /api/upload 会被这个规则匹配并代理到后端
    location /api {
        proxy_pass http://localhost:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 日志配置
    access_log /opt/blog-system/logs/nginx_access.log;
    error_log /opt/blog-system/logs/nginx_error.log;
}
EOF

# 测试nginx配置
sudo nginx -t

# 启动nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查nginx状态
sudo systemctl status nginx
```

## � 第十一步：验证文件上传配置

### 🔧 检查配置文件

```bash
# 检查后端配置中的文件上传路径
cd /opt/blog-system/backend
grep -A 5 -B 5 "upload" application-prod.yml

# 确保使用相对路径配置：
# file:
#   upload:
#     path: ../uploads/
# system:
#   uploadDir: ../uploads
```

### 🧪 测试文件上传功能

```bash
# 1. 创建测试文件
echo "test" > /tmp/test.txt

# 2. 使用curl测试上传接口
curl -X POST -F "file=@/tmp/test.txt" http://localhost:8080/api/upload

# 3. 检查文件是否保存在正确位置
ls -la /opt/blog-system/uploads/

# 4. 测试文件访问
curl -I http://localhost/upload/文件路径
curl -I http://localhost/api/upload/文件路径
```

### ⚠️ 常见配置错误

**错误配置**（会导致路径拼接错误）：
```yaml
file:
  upload:
    path: /opt/blog-system/uploads/  # 绝对路径可能导致拼接错误
```

**正确配置**：
```yaml
file:
  upload:
    path: ../uploads/  # 相对路径，相对于backend目录
```

## �🚀 第十二步：启动所有服务

```bash
# 1. 启动后端服务
cd /opt/blog-system/backend
./start.sh

# 2. 等待后端服务启动并验证
echo "等待后端服务启动..."
sleep 15

# 检查后端服务状态
./status.sh

# 3. 验证后端服务健康状态
echo "验证后端服务..."
curl -s http://localhost:8080/api/actuator/health | python -m json.tool 2>/dev/null || echo "后端服务可能还在启动中"

# 4. 检查启动日志（如果有问题）
echo "检查启动日志..."
tail -20 /opt/blog-system/logs/blog-server.log

# 5. 验证前端访问
echo "验证前端访问..."
curl -I http://localhost

# 6. 验证API代理
echo "验证API代理..."
curl -I http://localhost/api/actuator/health
```

## ✅ 启动成功标志

**后端服务启动成功的标志**：
```
Started BlogServerApplication in X.XXX seconds (JVM running for X.XXX)
```

**前端访问成功的标志**：
```
HTTP/1.1 200 OK
```

**如果启动失败**，请参考下面的故障排查章节。

## 🔒 第十三步：配置防火墙 (CentOS 7.6 firewalld)

```bash
# 检查防火墙状态
sudo systemctl status firewalld

# 如果防火墙未启动，先启动它
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 配置防火墙规则
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=8080/tcp

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 验证防火墙规则
sudo firewall-cmd --list-all
```

## 🔍 第十四步：设置文件权限和SELinux

```bash
# 设置上传目录权限
sudo chown -R nginx:nginx /opt/blog-system/uploads
sudo chmod -R 755 /opt/blog-system/uploads

# 设置日志目录权限
sudo chmod -R 755 /opt/blog-system/logs

# 检查SELinux状态
getenforce

# 如果SELinux是Enforcing模式，需要设置相应的上下文
if [ "$(getenforce)" = "Enforcing" ]; then
    echo "配置SELinux上下文..."

    # 设置上传目录的SELinux上下文
    sudo setsebool -P httpd_can_network_connect 1
    sudo setsebool -P httpd_read_user_content 1
    sudo semanage fcontext -a -t httpd_exec_t "/opt/blog-system/uploads(/.*)?"
    sudo restorecon -R /opt/blog-system/uploads

    # 允许nginx访问网络
    sudo setsebool -P httpd_can_network_relay 1

    echo "SELinux配置完成"
else
    echo "SELinux未启用或处于Permissive模式"
fi
```

## 📊 第十五步：设置自动备份

```bash
# 创建备份脚本 (CentOS 7.6优化版本)
cat > /opt/blog-system/backup.sh << 'EOF'
#!/bin/bash
# 博客系统自动备份脚本 - CentOS 7.6版本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/blog-system/backups"
LOG_FILE="/opt/blog-system/logs/backup.log"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 记录备份开始时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始备份" >> $LOG_FILE

# 备份数据库
echo "正在备份数据库..."
mysqldump -u root -p12345 --single-transaction --routines --triggers blog_system > $BACKUP_DIR/blog_system_$DATE.sql

if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 数据库备份成功" >> $LOG_FILE
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 数据库备份失败" >> $LOG_FILE
    exit 1
fi

# 备份上传文件
echo "正在备份上传文件..."
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C /opt/blog-system uploads/

if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件备份成功" >> $LOG_FILE
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件备份失败" >> $LOG_FILE
fi

# 创建完整备份包
echo "正在创建完整备份包..."
tar -czf $BACKUP_DIR/blog_system_full_backup_$DATE.tar.gz -C $BACKUP_DIR blog_system_$DATE.sql uploads_$DATE.tar.gz

# 清理临时文件
rm -f $BACKUP_DIR/blog_system_$DATE.sql $BACKUP_DIR/uploads_$DATE.tar.gz

# 删除7天前的备份
find $BACKUP_DIR -name "blog_system_full_backup_*.tar.gz" -mtime +7 -delete

echo "$(date '+%Y-%m-%d %H:%M:%S') - 备份完成: $BACKUP_DIR/blog_system_full_backup_$DATE.tar.gz" >> $LOG_FILE
echo "备份完成: $BACKUP_DIR/blog_system_full_backup_$DATE.tar.gz"
EOF

# 添加执行权限
chmod +x /opt/blog-system/backup.sh

# 添加到crontab，每天凌晨2点执行
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/blog-system/backup.sh") | crontab -
```

## 📝 第十六步：设置服务监控

```bash
# 创建监控脚本 (CentOS 7.6优化版本)
cat > /opt/blog-system/monitor.sh << 'EOF'
#!/bin/bash
# 博客系统服务监控脚本 - CentOS 7.6版本

LOG_FILE="/opt/blog-system/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查后端服务
if ! pgrep -f "blog-server.*jar" > /dev/null; then
    echo "$DATE - 警告: 后端服务未运行，正在尝试重启..." >> $LOG_FILE
    cd /opt/blog-system/backend && ./start.sh
    sleep 10
    if pgrep -f "blog-server.*jar" > /dev/null; then
        echo "$DATE - 后端服务重启成功" >> $LOG_FILE
    else
        echo "$DATE - 后端服务重启失败" >> $LOG_FILE
    fi
fi

# 检查Nginx服务
if ! pgrep -f "nginx: master process" > /dev/null; then
    echo "$DATE - 警告: Nginx服务未运行，正在尝试重启..." >> $LOG_FILE
    sudo systemctl start nginx
    if systemctl is-active --quiet nginx; then
        echo "$DATE - Nginx服务重启成功" >> $LOG_FILE
    else
        echo "$DATE - Nginx服务重启失败" >> $LOG_FILE
    fi
fi

# 检查MySQL服务
if ! systemctl is-active --quiet mysqld; then
    echo "$DATE - 警告: MySQL服务未运行，正在尝试重启..." >> $LOG_FILE
    sudo systemctl start mysqld
    if systemctl is-active --quiet mysqld; then
        echo "$DATE - MySQL服务重启成功" >> $LOG_FILE
    else
        echo "$DATE - MySQL服务重启失败" >> $LOG_FILE
    fi
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/blog-system | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$DATE - 警告: 磁盘使用率过高 ($DISK_USAGE%)" >> $LOG_FILE
fi

# 检查内存使用
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "$DATE - 警告: 内存使用率过高 ($MEM_USAGE%)" >> $LOG_FILE
fi
EOF

chmod +x /opt/blog-system/monitor.sh

# 添加到crontab，每5分钟执行一次
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/blog-system/monitor.sh") | crontab -

# 查看crontab设置
crontab -l
```

## ✅ 第十七步：部署验证

### 1. 系统服务检查
```bash
# 检查所有关键服务状态
echo "=== 系统服务状态检查 ==="
systemctl status mysqld nginx --no-pager
echo ""

# 检查后端服务
echo "=== 后端服务状态 ==="
cd /opt/blog-system/backend && ./status.sh
echo ""

# 检查端口监听
echo "=== 端口监听状态 ==="
netstat -tlnp | grep -E ':80|:8080|:3306'  # 80:Nginx, 8080:Spring Boot, 3306:MySQL
echo ""
```

### 2. 功能验证
```bash
# 测试后端API
echo "=== 后端API测试 ==="
curl -s http://localhost:8080/api/actuator/health | python -m json.tool 2>/dev/null || echo "后端服务未响应"

# 测试前端访问
echo "=== 前端访问测试 ==="
curl -I http://localhost 2>/dev/null | head -1

# 测试数据库连接
echo "=== 数据库连接测试 ==="
mysql -u root -p12345 -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='blog_system';" 2>/dev/null
```

### 3. 访问网站
在浏览器中访问：`http://**************`

### 4. 管理员登录
- 用户名：admin
- 密码：admin123

### 5. 测试功能
- 发布文章
- 上传图片
- 发表评论
- 测试邮件功能（系统设置 → 邮件设置 → 测试邮件配置）

## 🔍 常见问题排查 (CentOS 7.6专用)

### 📋 问题快速索引
- **MySQL问题**：[0. MySQL安装和连接问题](#0-mysql安装和连接问题)
- **后端启动问题**：[1. 后端服务启动问题](#1-后端服务启动问题)
- **前端访问问题**：[2. 前端访问问题](#2-前端访问问题)
- **数据库连接问题**：[3. 数据库连接问题](#3-数据库连接问题)
- **文件上传问题**：[4. 文件上传问题](#4-文件上传问题)
- **本地部署问题**：[6. 本地部署问题](#6-本地部署问题)
- **系统设置问题**：[7. 系统设置保存问题](#7-系统设置保存问题)
- **邮件功能问题**：[8. 邮件功能配置问题](#8-邮件功能配置问题)
- **头像显示问题**：[9. 头像显示问题](#9-头像显示问题)

### 0. MySQL安装和连接问题

#### 问题A：MySQL服务启动失败
**错误现象**：
```
Failed to start mysqld.service: Unit not found.
```

**解决方案**：
```bash
# 检查MySQL是否正确安装
rpm -qa | grep mysql
systemctl list-unit-files | grep mysql

# 如果没有安装，重新安装MySQL
sudo yum remove -y mysql*
sudo yum install -y https://dev.mysql.com/get/mysql80-community-release-el7-5.noarch.rpm
sudo rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
sudo yum install -y mysql-community-server mysql-community-client --nogpgcheck
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

#### 问题B：MySQL密码策略错误
**错误现象**：
```
Your password does not satisfy the current policy requirements
```

**解决方案**：
```bash
# 先使用符合策略的强密码：Blog@123456
# 包含大写字母、小写字母、数字和特殊字符

# 然后修改为简单密码（与配置文件一致）：
mysql -u root -p
# 输入密码: Blog@123456
SET GLOBAL validate_password.policy=LOW;
SET GLOBAL validate_password.length=4;
ALTER USER 'root'@'localhost' IDENTIFIED BY '12345';
ALTER USER 'blog_admin'@'%' IDENTIFIED BY '12345';
FLUSH PRIVILEGES;
EXIT;
```

#### 问题C：DBeaver远程连接失败
**错误现象**：
```
Host 'xxx.xxx.xxx.xxx' is not allowed to connect to this MySQL server
Public Key Retrieval is not allowed
```

**解决方案**：
```bash
# 1. 创建远程用户
mysql -u root -p
# 输入密码: 12345
CREATE USER 'blog_admin'@'%' IDENTIFIED WITH mysql_native_password BY '12345';
GRANT ALL PRIVILEGES ON *.* TO 'blog_admin'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
EXIT;

# 2. 配置MySQL允许远程连接
sudo vi /etc/my.cnf
# 添加：bind-address = 0.0.0.0
sudo systemctl restart mysqld

# 3. 开放防火墙端口
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload

# 4. DBeaver连接配置
# 驱动属性添加：allowPublicKeyRetrieval=true, useSSL=false
```

#### 问题D：防火墙服务未运行
**错误现象**：
```
FirewallD is not running
```

**解决方案**：
```bash
# 启动防火墙服务
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 如果启动失败，可能是云服务器使用其他防火墙管理
# 可以忽略此步骤，直接测试MySQL连接
netstat -tlnp | grep 3306
```

### 1. 后端服务启动问题

#### 问题A：YAML配置文件重复键错误
**错误现象**：
```
DuplicateKeyException: found duplicate key spring
```

**解决方案**：
```bash
cd /opt/blog-system/backend

# 检查配置文件中是否有重复的spring键
grep -n "^spring:" application-prod.yml

# 如果发现多个spring:，需要合并为一个
# 重新创建正确的配置文件（参考第八步）
```

#### 问题B：数据库驱动类找不到
**错误现象**：
```
ClassNotFoundException: com.mysql.cj.jdbc.Driver
```

**解决方案**：
```bash
# 检查JAR包中包含的数据库驱动
jar -tf blog-server-0.0.1-SNAPSHOT.jar | grep -i mysql

# 确保配置文件使用正确的驱动类
grep -n "driver-class-name" application-prod.yml
grep -n "jdbc:" application-prod.yml

# 如果配置错误，修改为正确的MySQL驱动
sed -i 's/driver-class-name:.*/driver-class-name: com.mysql.cj.jdbc.Driver/g' application-prod.yml

# 重启服务
./restart.sh
```

#### 问题C：SQL语法错误
**错误现象**：
```
ERROR 1064 (42000): You have an error in your SQL syntax
ERROR 1064 (42000) at line 220: You have an error in your SQL syntax
```

**解决方案**：
```bash
# MySQL 8.0使用标准MySQL语法，手动导入数据库脚本
mysql -u root -p blog_system < blog_system.sql

# 如果还有语法错误，检查字符编码
file blog_system.sql

# 确保文件是UTF-8编码，如果不是，转换编码
iconv -f GBK -t UTF-8 blog_system.sql > blog_system_utf8.sql
mysql -u root -p blog_system < blog_system_utf8.sql

# 检查MySQL版本兼容性
mysql -u root -p -e "SELECT VERSION();"
```

#### 问题D：数据库表结构不完整
**错误现象**：
```
Unknown column 'bio' in 'field list'
Unknown column 'order_num' in 'field list'
Unknown column 'anonymous_nickname' in 'field list'
```

**解决方案**：
```bash
# 检查当前表结构
mysql -u root -p -e "DESCRIBE user;" blog_system
mysql -u root -p -e "DESCRIBE category;" blog_system
mysql -u root -p -e "DESCRIBE comment;" blog_system

# 如果发现字段缺失，需要重新导入完整的数据库脚本
# 备份现有数据（如果有重要数据）
mysqldump -u root -p12345 blog_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 删除现有数据库并重新创建
mysql -u root -p -e "DROP DATABASE blog_system;"
mysql -u root -p -e "CREATE DATABASE blog_system;"

# 重新手动导入数据库脚本（请手动执行）
# mysql -u root -p blog_system < your_blog_system.sql
```

#### 问题D：数据库连接失败
**错误现象**：
```
Communications link failure
```

**解决方案**：
```bash
# 测试数据库连接
mysql -u root -p12345 -e "SELECT 1"

# 检查MySQL服务状态
systemctl status mysqld

# 如果数据库正常，检查URL配置是否正确
# 确保URL中包含allowPublicKeyRetrieval=true参数
```

### 2. 后端服务运行问题
```bash
# 查看后端日志
tail -f /opt/blog-system/logs/blog-server.log

# 检查Java进程
ps -ef | grep java

# 检查JVM内存使用
jps -v

# 重启后端服务
cd /opt/blog-system/backend && ./restart.sh

# 检查端口占用
netstat -tlnp | grep :8080

# 测试服务健康状态
curl http://localhost:8080/api/actuator/health
```

### 2. 前端访问问题
```bash
# 查看Nginx日志
tail -f /opt/blog-system/logs/nginx_error.log
tail -f /opt/blog-system/logs/nginx_access.log

# 检查Nginx进程
ps -ef | grep nginx

# 测试Nginx配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx

# 检查Nginx状态
sudo systemctl status nginx
```

### 3. 数据库连接问题
```bash
# 检查MySQL状态
sudo systemctl status mysqld

# 测试数据库连接
mysql -u root -p12345 -e "SHOW DATABASES;"

# 查看MySQL日志
sudo tail -f /var/log/mysqld.log
# 或者查看系统日志
sudo journalctl -u mysqld -f

# 重启MySQL
sudo systemctl restart mysqld

# 检查数据库进程
ps -ef | grep mysqld
ps -ef | grep mysql
```

### 4. 文件上传问题

#### 问题A：文件上传成功但无法访问（404错误）
**错误现象**：
```
前端显示：上传成功响应: {code: 200, message: '操作成功', data: '/upload/xxx.png'}
但访问时：GET http://**************/api/upload/xxx.png 404 (Not Found)
```

**排查步骤**：
```bash
# 1. 检查文件是否真的保存成功
ls -la /opt/blog-system/uploads/

# 2. 检查后端日志中的上传路径
grep -i "upload\|file" /opt/blog-system/logs/blog-server.log

# 3. 查看路径拼接是否正确
tail -20 /opt/blog-system/logs/blog-server.log | grep "目标文件路径\|完整的物理存储路径"
```

**常见原因和解决方案**：

**原因1：路径配置错误导致文件保存在错误位置**
```bash
# 检查是否保存在错误路径（如：/opt/blog-system/backend/opt/blog-system/uploads/）
ls -la /opt/blog-system/backend/opt/blog-system/uploads/

# 修改配置文件使用相对路径
vi /opt/blog-system/backend/application-prod.yml
# 修改为：
# file:
#   upload:
#     path: ../uploads/
# system:
#   uploadDir: ../uploads

# 移动已上传的文件到正确位置
mkdir -p /opt/blog-system/uploads/
cp -r /opt/blog-system/backend/opt/blog-system/uploads/* /opt/blog-system/uploads/ 2>/dev/null || true
rm -rf /opt/blog-system/backend/opt/blog-system/uploads

# 重启服务
cd /opt/blog-system/backend && ./restart.sh
```

**原因2：Nginx配置缺少API路径支持**
```bash
# 检查Nginx配置
grep -A 10 "location.*upload" /etc/nginx/conf.d/blog.conf

# 添加API路径支持
vi /etc/nginx/conf.d/blog.conf
# 在现有location /upload配置后添加：
# location /api/upload {
#     alias /opt/blog-system/uploads;
#     autoindex off;
# }

# 重启Nginx
nginx -t && systemctl reload nginx
```

**原因3：目录权限问题**
```bash
# 检查上传目录权限
ls -la /opt/blog-system/uploads

# 修复权限
sudo chown -R nginx:nginx /opt/blog-system/uploads
sudo chmod -R 755 /opt/blog-system/uploads

# 检查SELinux上下文 (如果启用)
ls -Z /opt/blog-system/uploads

# 重新设置SELinux上下文
sudo restorecon -R /opt/blog-system/uploads
```

### 5. 防火墙问题
```bash
# 检查防火墙状态
sudo firewall-cmd --list-all

# 临时开放端口 (测试用)
sudo firewall-cmd --add-port=8080/tcp
sudo firewall-cmd --add-port=80/tcp

# 永久开放端口
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --reload
```

### 6. 本地部署问题

#### 问题A：PowerShell执行策略限制
**错误现象**：
```
无法加载文件，因为在此系统上禁止运行脚本
```

**解决方案**：
```bash
# 方案1：使用cmd命令
cmd /c "npm run build"

# 方案2：修改执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 方案3：使用cmd窗口而不是PowerShell
```

#### 问题B：SSH首次连接问题
**错误现象**：
```
The authenticity of host can't be established
Host key verification failed
```

**解决方案**：
```bash
# 当提示时输入 yes 接受主机密钥
# 然后输入服务器密码：qwer123..

# 或者跳过主机密钥验证（不推荐）：
ssh -o StrictHostKeyChecking=no root@************** -p 22
```

#### 问题C：文件路径错误
**错误现象**：
```
No such file or directory
```

**解决方案**：
```bash
# 确保在本地计算机上执行scp命令，不是在服务器上
# 确保当前目录是项目根目录
cd D:\java

# 确保文件已构建
cd blog-server && mvn clean package -DskipTests && cd ..
cd blog-web && npm run build && cd ..

# 检查文件是否存在
ls blog-server/target/blog-server-*.jar
ls blog-web/dist/
```

### 7. 系统设置保存问题

#### 问题：管理后台系统设置显示保存成功但刷新后没有改变
**错误现象**：
```
前端显示：保存成功
后端日志：==> Parameters: config_key(String) <== Total: 0
```

**原因分析**：config表或system_setting表缺少基础配置数据

**解决方案**：
```bash
# 1. 检查配置表数据
mysql -u root -p12345 -e "SELECT COUNT(*) as total FROM config;" blog_system
mysql -u root -p12345 -e "SELECT COUNT(*) as total FROM system_setting;" blog_system

# 2. 插入基础配置数据
mysql -u root -p12345 blog_system << 'EOF'
INSERT INTO config (config_key, config_value, config_desc, create_time, update_time) VALUES
('site_name', '个人博客系统', '网站名称', NOW(), NOW()),
('comment_audit', '0', '评论审核：0-不需要审核，1-需要审核', NOW(), NOW()),
('allow_anonymous_comment', '1', '允许匿名评论：0-不允许，1-允许', NOW(), NOW()),
('email_enable', '0', '邮件功能启用：0-禁用，1-启用', NOW(), NOW()),
('theme_color', '#1890ff', '主题颜色', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();
EOF

# 3. 重启后端服务
cd /opt/blog-system/backend && ./restart.sh

# 4. 验证配置
mysql -u root -p12345 -e "SELECT config_key, config_value FROM config;" blog_system
```

### 8. 邮件功能配置问题

#### 问题A：邮件测试连接504超时或连接失败
**错误现象**：
```
前端：504 Gateway Time-out
后端日志：MailConnectException: Couldn't connect to host, port: smtp.163.com, 25; timeout -1
```

**原因分析**：
1. 云服务商通常封禁25端口（防止垃圾邮件）
2. Nginx代理超时时间不够
3. 需要使用SSL/TLS加密端口

**解决方案**：

**步骤1：测试邮件服务器端口连通性**
```bash
# 测试不同端口
telnet smtp.163.com 25   # 通常被封禁
telnet smtp.163.com 465  # SSL端口
telnet smtp.163.com 587  # TLS端口
```

**步骤2：修改邮件配置使用SSL端口465**
```bash
vi /opt/blog-system/backend/application-prod.yml

# 修改邮件配置为：
spring:
  mail:
    host: smtp.163.com
    port: 465  # 使用SSL端口
    username: <EMAIL>
    password: CHQNTsxXQQeEvJRa
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
            fallback: false
```

**步骤3：增加Nginx超时配置**
```bash
vi /etc/nginx/conf.d/blog.conf

# 在location /api部分添加：
proxy_connect_timeout 30s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;
```

**步骤4：重启服务**
```bash
nginx -t && systemctl reload nginx
cd /opt/blog-system/backend && ./restart.sh
```

#### 问题B：如果465端口不可用，使用587端口（TLS）
```yaml
spring:
  mail:
    host: smtp.163.com
    port: 587
    username: <EMAIL>
    password: CHQNTsxXQQeEvJRa
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            enable: false
```

### 9. 头像显示问题

#### 问题：个人资料头像上传成功但不显示
**错误现象**：
```
前端：头像上传显示成功，但页面不显示头像
浏览器：GET http://**************/api/upload/avatar/xxx.png 401/404
```

**原因分析**：
1. Nginx配置问题：`/api/upload` 路径被代理到后端API而不是静态文件
2. 文件保存路径问题：头像文件保存在日期目录而不是avatar目录
3. 文件权限问题

**解决方案**：

**步骤1：修复Nginx配置（location顺序很重要）**
```bash
vi /etc/nginx/conf.d/blog.conf

# 确保配置顺序正确：
server {
    # ... 其他配置

    # 上传文件访问 - 必须在 location /api 之前！
    location /api/upload {
        alias /opt/blog-system/uploads;
        autoindex off;
    }

    location /upload {
        alias /opt/blog-system/uploads;
        autoindex off;
    }

    # 后端API代理 - 必须在静态文件location之后！
    location /api {
        proxy_pass http://localhost:8080/api;
        # ... 其他代理配置
    }
}

# 重启Nginx
nginx -t && systemctl reload nginx
```

**步骤2：检查头像文件位置**
```bash
# 检查头像文件是否存在
ls -la /opt/blog-system/uploads/avatar/

# 查找实际的图片文件位置
find /opt/blog-system/uploads -name "*.png" -type f

# 查看最近上传的文件
ls -lat /opt/blog-system/uploads/2025/07/27/ | head -5
```

**步骤3：移动头像文件到正确位置**
```bash
# 创建avatar目录
mkdir -p /opt/blog-system/uploads/avatar

# 找到最新的头像文件并复制到avatar目录
LATEST_FILE=$(ls -t /opt/blog-system/uploads/2025/07/27/*.png | head -1)
cp "$LATEST_FILE" /opt/blog-system/uploads/avatar/目标文件名.png

# 设置正确权限
chown nginx:nginx /opt/blog-system/uploads/avatar/*.png
chmod 644 /opt/blog-system/uploads/avatar/*.png
```

**步骤4：验证修复**
```bash
# 测试文件访问
curl -I http://localhost/api/upload/avatar/文件名.png
# 应该返回 200 OK

# 刷新浏览器页面验证头像显示
```

#### 快速修复脚本
```bash
# 一键修复头像显示问题
LATEST_FILE=$(ls -t /opt/blog-system/uploads/2025/07/27/*.png | head -1)
echo "使用文件: $LATEST_FILE"
mkdir -p /opt/blog-system/uploads/avatar
cp "$LATEST_FILE" /opt/blog-system/uploads/avatar/$(basename "$LATEST_FILE")
chown nginx:nginx /opt/blog-system/uploads/avatar/*.png
chmod 644 /opt/blog-system/uploads/avatar/*.png
curl -I http://localhost/api/upload/avatar/$(basename "$LATEST_FILE")
```

**注意事项**：
- Nginx的location匹配是按配置文件顺序进行的
- 更具体的路径（如`/api/upload`）必须放在更通用的路径（如`/api`）之前
- 头像文件可能保存在日期目录中，需要手动移动到avatar目录

### 10. Node.js和npm问题
```bash
# 检查Node.js版本
node -v
npm -v

# 清理npm缓存
npm cache clean --force

# 重新安装依赖
cd /opt/blog-system/frontend
rm -rf node_modules package-lock.json
npm install

# 使用国内镜像
npm config set registry https://registry.npmmirror.com
```

## 📋 日常维护命令

### 服务管理
```bash
# 查看所有服务状态
systemctl status nginx mysqld --no-pager
cd /opt/blog-system/backend && ./status.sh

# 重启所有服务
sudo systemctl restart mysqld nginx
cd /opt/blog-system/backend && ./restart.sh

# 查看系统资源使用
top
df -h
free -h
iostat 1 5
```

### 日志查看
```bash
# 查看实时日志
tail -f /opt/blog-system/logs/blog-server.log
tail -f /opt/blog-system/logs/nginx_access.log
tail -f /opt/blog-system/logs/nginx_error.log
tail -f /opt/blog-system/logs/monitor.log

# 查看系统日志
sudo journalctl -u nginx -f
sudo journalctl -u mysqld -f
```

### 备份恢复
```bash
# 手动执行备份
/opt/blog-system/backup.sh

# 查看备份文件
ls -la /opt/blog-system/backups/

# 恢复数据库
mysql -u root -p12345 blog_system < /opt/blog-system/backups/blog_system_20250715_120000.sql

# 恢复上传文件
cd /opt/blog-system
tar -xzf backups/uploads_20250715_120000.tar.gz
```

### 性能优化 (CentOS 7.6)

#### 系统级优化
```bash
# 调整系统参数
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
echo 'fs.file-max=65535' >> /etc/sysctl.conf
sysctl -p

# 调整文件描述符限制
echo '* soft nofile 65535' >> /etc/security/limits.conf
echo '* hard nofile 65535' >> /etc/security/limits.conf
```

#### MySQL 8.0性能优化
```bash
# 备份原配置
sudo cp /etc/my.cnf /etc/my.cnf.backup

# 根据服务器内存调整配置
sudo cat >> /etc/my.cnf << 'EOF'

[mysqld]
# 基础性能配置
innodb_buffer_pool_size=512M  # 设置为可用内存的70-80%
innodb_log_file_size=128M
max_connections=200
query_cache_size=0  # MySQL 8.0已弃用
query_cache_type=0

# MySQL 8.0特有优化
innodb_adaptive_hash_index=ON
innodb_flush_log_at_trx_commit=2
default_authentication_plugin=mysql_native_password

# 连接优化
max_connect_errors=100000
wait_timeout=28800
interactive_timeout=28800

# 缓存优化
table_open_cache=2000
thread_cache_size=50
EOF

sudo systemctl restart mysqld
```

#### Nginx性能优化
```bash
# 编辑Nginx主配置
vi /etc/nginx/nginx.conf

# 在http块中添加：
# worker_processes auto;
# worker_connections 1024;
# keepalive_timeout 65;
# gzip on;
# gzip_types text/plain text/css application/json application/javascript;

systemctl reload nginx
```

#### Java应用优化
```bash
# 编辑启动脚本，调整JVM参数
vi /opt/blog-system/backend/start.sh

# 根据服务器内存调整：
# -Xms512m -Xmx1024m  # 2GB内存服务器
# -Xms1024m -Xmx2048m # 4GB内存服务器
```

## 🎉 部署完成

恭喜您！博客系统已成功部署到CentOS 7.6服务器。

## 🌐 访问验证

### 1. 后端服务验证
```bash
# 检查后端服务状态
curl -s http://localhost:8080/api/actuator/health

# 应该返回类似以下内容：
# {"status":"UP"}
```

### 2. 前端页面验证
在浏览器中访问：**http://****************

### 3. 完整功能验证
- **管理员登录**：admin / admin123
- **前后端通信**：检查登录是否成功
- **数据库连接**：检查是否能正常显示数据

## 📋 系统信息

**访问地址**：http://**************
**管理员账户**：admin / admin123
**邮箱配置**：<EMAIL>
**数据库**：MySQL 8.0，用户名root，密码12345

## 📝 部署完成验证清单

### 🔍 基础服务验证
```bash
# 1. 检查MySQL服务
systemctl status mysqld
mysql -u root -p12345 -e "SELECT VERSION();"

# 2. 检查后端服务
cd /opt/blog-system/backend && ./status.sh
curl -s http://localhost:8080/api/actuator/health

# 3. 检查Nginx服务
systemctl status nginx
curl -I http://localhost/

# 4. 检查文件上传目录权限
ls -la /opt/blog-system/uploads/
```

### 🌐 功能验证清单
- [ ] ✅ 前端页面正常访问：http://**************
- [ ] ✅ 管理员登录成功：admin / admin123
- [ ] ✅ 系统设置保存正常
- [ ] ✅ 文件上传功能正常
- [ ] ✅ 头像显示功能正常
- [ ] ✅ 邮件测试连接成功
- [ ] ✅ 数据库连接正常（DBeaver可连接）

### 🚨 常见问题快速检查
```bash
# 如果前端无法访问
systemctl status nginx
curl -I http://localhost/

# 如果后端API报错
tail -50 /opt/blog-system/logs/blog-server.log

# 如果文件上传失败
ls -la /opt/blog-system/uploads/
curl -I http://localhost/api/upload/test.png

# 如果头像不显示
curl -I http://localhost/api/upload/avatar/test.png

# 如果邮件发送失败
telnet smtp.163.com 465
```

## 📝 CentOS 7.6特别注意事项

1. **安全更新**：CentOS 7.6已停止维护，请定期检查安全漏洞
2. **软件版本**：部分软件包版本较老，建议关注兼容性问题
3. **迁移建议**：生产环境建议迁移到Rocky Linux 8/9或AlmaLinux 8/9
4. **备份重要性**：由于系统不再更新，备份变得更加重要
5. **MySQL优势**：MySQL 8.0提供稳定的企业级数据库功能

## � 安全建议

### 基础安全配置
```bash
# 1. 修改SSH默认端口（可选）
vi /etc/ssh/sshd_config
# Port 22 改为其他端口

# 2. 禁用root远程登录（生产环境推荐）
# PermitRootLogin no

# 3. 配置防火墙只开放必要端口
firewall-cmd --permanent --remove-service=ssh  # 如果修改了SSH端口
firewall-cmd --permanent --add-port=新SSH端口/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=3306/tcp  # 仅在需要远程数据库访问时
firewall-cmd --reload

# 4. 定期更新系统（在CentOS 7.6生命周期内）
yum update -y
```

### 应用安全配置
```bash
# 1. 修改默认管理员密码
# 登录后台 → 个人资料 → 修改密码

# 2. 修改数据库密码（生产环境）
mysql -u root -p12345
ALTER USER 'root'@'localhost' IDENTIFIED BY '新的强密码';
ALTER USER 'blog_admin'@'%' IDENTIFIED BY '新的强密码';
FLUSH PRIVILEGES;

# 3. 配置SSL证书（生产环境推荐）
# 使用Let's Encrypt或其他SSL证书服务

# 4. 定期备份数据
# 设置定时任务执行 /opt/blog-system/backup.sh
```

## �🚀 MySQL 8.0数据库特性

1. **企业级功能**：提供完整的企业级数据库功能
2. **性能优化**：针对现代应用场景进行了优化
3. **标准兼容**：完全符合SQL标准
4. **存储引擎**：InnoDB存储引擎提供优秀的事务支持
5. **社区支持**：庞大的用户社区和丰富的文档
6. **稳定性**：经过大量生产环境验证的稳定性

## 🔗 相关资源

- **CentOS 7.6官方文档**：https://docs.centos.org/
- **MySQL 8.0官方文档**：https://dev.mysql.com/doc/refman/8.0/en/
- **MySQL安装指南**：https://dev.mysql.com/doc/mysql-installation-excerpt/8.0/en/
- **Rocky Linux迁移指南**：https://rockylinux.org/
- **AlmaLinux迁移指南**：https://almalinux.org/
- **Spring Boot官方文档**：https://spring.io/projects/spring-boot
- **Vue 3官方文档**：https://vuejs.org/

如有问题，请参考上述排查步骤或查看日志文件。
