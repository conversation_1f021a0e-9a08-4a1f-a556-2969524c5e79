package com.blog.service;

import com.blog.entity.User;
import com.blog.entity.UserFollow;
import com.blog.enums.FollowResultType;
import com.blog.mapper.UserFollowMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.impl.FollowServiceImpl;
import com.blog.vo.UserVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 关注服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class FollowServiceTest {

    @Mock
    private UserFollowMapper userFollowMapper;
    
    @Mock
    private UserMapper userMapper;
    
    @InjectMocks
    private FollowServiceImpl followService;
    
    // 测试用户数据
    private User testUser1;
    private User testUser2;
    private User testUser3;
    
    @BeforeEach
    public void setup() {
        // 初始化测试用户
        testUser1 = new User();
        testUser1.setId(1L);
        testUser1.setUsername("user1");
        testUser1.setNickname("用户1");
        testUser1.setAvatar("avatar1.jpg");
        
        testUser2 = new User();
        testUser2.setId(2L);
        testUser2.setUsername("user2");
        testUser2.setNickname("用户2");
        testUser2.setAvatar("avatar2.jpg");
        
        testUser3 = new User();
        testUser3.setId(3L);
        testUser3.setUsername("user3");
        testUser3.setNickname("用户3");
        testUser3.setAvatar("avatar3.jpg");
    }
    
    @Test
    @DisplayName("用户关注成功测试")
    public void testFollowSuccess() {
        // 准备测试数据
        Long followerId = 1L;
        Long followedId = 2L;
        
        // 模拟用户存在
        when(userMapper.selectById(followerId)).thenReturn(testUser1);
        when(userMapper.selectById(followedId)).thenReturn(testUser2);
        
        // 模拟未关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(0);
        
        // 模拟插入成功
        when(userFollowMapper.insert(any(UserFollow.class))).thenReturn(1);
        
        // 执行测试
        FollowResultType result = followService.follow(followerId, followedId);

        // 验证结果
        assertEquals(FollowResultType.SUCCESS, result);
        verify(userFollowMapper).insert(any(UserFollow.class));
    }
    
    @Test
    @DisplayName("关注自己失败测试")
    public void testFollowSelfFail() {
        // 准备测试数据 - 关注自己
        Long userId = 1L;
        
        // 执行测试
        FollowResultType result = followService.follow(userId, userId);

        // 验证结果
        assertEquals(FollowResultType.CANNOT_FOLLOW_SELF, result);
        verify(userFollowMapper, never()).insert(any(UserFollow.class));
    }
    
    @Test
    @DisplayName("关注不存在的用户失败测试")
    public void testFollowNonExistentUserFail() {
        // 准备测试数据
        Long followerId = 1L;
        Long nonExistentUserId = 999L;
        
        // 模拟关注者存在
        when(userMapper.selectById(followerId)).thenReturn(testUser1);
        
        // 模拟被关注者不存在
        when(userMapper.selectById(nonExistentUserId)).thenReturn(null);
        
        // 执行测试
        FollowResultType result = followService.follow(followerId, nonExistentUserId);

        // 验证结果
        assertEquals(FollowResultType.USER_NOT_FOUND, result);
        verify(userFollowMapper, never()).insert(any(UserFollow.class));
    }
    
    @Test
    @DisplayName("重复关注测试")
    public void testFollowAlreadyFollowing() {
        // 准备测试数据
        Long followerId = 1L;
        Long followedId = 2L;
        
        // 模拟用户存在
        when(userMapper.selectById(followerId)).thenReturn(testUser1);
        when(userMapper.selectById(followedId)).thenReturn(testUser2);
        
        // 模拟已关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(1);
        
        // 执行测试
        FollowResultType result = followService.follow(followerId, followedId);

        // 验证结果
        assertEquals(FollowResultType.ALREADY_FOLLOWING, result);
        verify(userFollowMapper, never()).insert(any(UserFollow.class)); // 但不会再次插入记录
    }
    
    @Test
    @DisplayName("取消关注成功测试")
    public void testUnfollowSuccess() {
        // 准备测试数据
        Long followerId = 1L;
        Long followedId = 2L;
        
        // 模拟已关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(1);
        
        // 模拟删除成功
        when(userFollowMapper.delete(any())).thenReturn(1);
        
        // 执行测试
        FollowResultType result = followService.unfollow(followerId, followedId);

        // 验证结果
        assertEquals(FollowResultType.SUCCESS, result);
        verify(userFollowMapper).delete(any());
    }
    
    @Test
    @DisplayName("取消未关注的用户测试")
    public void testUnfollowNotFollowing() {
        // 准备测试数据
        Long followerId = 1L;
        Long followedId = 2L;
        
        // 模拟未关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(0);
        
        // 执行测试
        FollowResultType result = followService.unfollow(followerId, followedId);

        // 验证结果
        assertEquals(FollowResultType.NOT_FOLLOWING, result);
        verify(userFollowMapper, never()).delete(any()); // 但不会执行删除操作
    }
    
    @Test
    @DisplayName("查询关注状态测试")
    public void testIsFollowing() {
        // 准备测试数据
        Long followerId = 1L;
        Long followedId = 2L;
        
        // 模拟已关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(1);
        
        // 执行测试
        boolean result = followService.isFollowing(followerId, followedId);
        
        // 验证结果
        assertTrue(result);
        
        // 模拟未关注状态
        when(userFollowMapper.selectIsFollowing(followerId, followedId)).thenReturn(0);
        
        // 再次执行测试
        result = followService.isFollowing(followerId, followedId);
        
        // 验证结果
        assertFalse(result);
    }
    
    @Test
    @DisplayName("获取关注列表测试")
    public void testGetFollowingList() {
        // 准备测试数据
        Long userId = 1L;
        List<Long> followingIds = Arrays.asList(2L, 3L);
        
        // 模拟查询关注ID列表
        when(userFollowMapper.selectFollowingIds(userId)).thenReturn(followingIds);
        
        // 模拟查询用户信息
        when(userMapper.selectById(2L)).thenReturn(testUser2);
        when(userMapper.selectById(3L)).thenReturn(testUser3);
        
        // 使用 lenient() 设置宽松模式，允许未使用的 stub
        lenient().when(userFollowMapper.selectFollowingCount(2L)).thenReturn(5);
        lenient().when(userFollowMapper.selectFollowerCount(2L)).thenReturn(10);
        lenient().when(userFollowMapper.selectFollowingCount(3L)).thenReturn(3);
        lenient().when(userFollowMapper.selectFollowerCount(3L)).thenReturn(7);
        
        // 模拟关注状态
        lenient().when(userFollowMapper.selectIsFollowing(userId, 2L)).thenReturn(1);
        lenient().when(userFollowMapper.selectIsFollowing(userId, 3L)).thenReturn(1);
        
        // 执行测试
        List<UserVO> result = followService.getFollowingList(userId);
        
        // 验证结果
        assertThat(result).hasSize(2);
        assertEquals(testUser2.getId(), result.get(0).getId());
        assertEquals(testUser3.getId(), result.get(1).getId());
    }
    
    @Test
    @DisplayName("获取空关注列表测试")
    public void testGetEmptyFollowingList() {
        // 准备测试数据
        Long userId = 1L;
        
        // 模拟查询空关注ID列表
        when(userFollowMapper.selectFollowingIds(userId)).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<UserVO> result = followService.getFollowingList(userId);
        
        // 验证结果
        assertThat(result).isEmpty();
    }
    
    @Test
    @DisplayName("获取粉丝列表测试")
    public void testGetFollowerList() {
        // 准备测试数据
        Long userId = 1L;
        List<Long> followerIds = Arrays.asList(2L, 3L);
        
        // 模拟查询粉丝ID列表
        when(userFollowMapper.selectFollowerIds(userId)).thenReturn(followerIds);
        
        // 模拟查询用户信息
        when(userMapper.selectById(2L)).thenReturn(testUser2);
        when(userMapper.selectById(3L)).thenReturn(testUser3);
        
        // 使用 lenient() 设置宽松模式，允许未使用的 stub
        lenient().when(userFollowMapper.selectFollowingCount(2L)).thenReturn(5);
        lenient().when(userFollowMapper.selectFollowerCount(2L)).thenReturn(10);
        lenient().when(userFollowMapper.selectFollowingCount(3L)).thenReturn(3);
        lenient().when(userFollowMapper.selectFollowerCount(3L)).thenReturn(7);
        
        // 模拟关注状态 - 假设当前用户关注了用户2但没关注用户3
        lenient().when(userFollowMapper.selectIsFollowing(userId, 2L)).thenReturn(1);
        lenient().when(userFollowMapper.selectIsFollowing(userId, 3L)).thenReturn(0);
        
        // 执行测试
        List<UserVO> result = followService.getFollowerList(userId);
        
        // 验证结果
        assertThat(result).hasSize(2);
        assertEquals(testUser2.getId(), result.get(0).getId());
        assertEquals(testUser3.getId(), result.get(1).getId());
    }
    
    @Test
    @DisplayName("获取空粉丝列表测试")
    public void testGetEmptyFollowerList() {
        // 准备测试数据
        Long userId = 1L;
        
        // 模拟查询空粉丝ID列表
        when(userFollowMapper.selectFollowerIds(userId)).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<UserVO> result = followService.getFollowerList(userId);
        
        // 验证结果
        assertThat(result).isEmpty();
    }
    
    @Test
    @DisplayName("获取关注数量测试")
    public void testGetFollowingCount() {
        // 准备测试数据
        Long userId = 1L;
        int expectedCount = 5;
        
        // 模拟查询关注数量
        when(userFollowMapper.selectFollowingCount(userId)).thenReturn(expectedCount);
        
        // 执行测试
        int result = followService.getFollowingCount(userId);
        
        // 验证结果
        assertEquals(expectedCount, result);
    }
    
    @Test
    @DisplayName("获取粉丝数量测试")
    public void testGetFollowerCount() {
        // 准备测试数据
        Long userId = 1L;
        int expectedCount = 10;
        
        // 模拟查询粉丝数量
        when(userFollowMapper.selectFollowerCount(userId)).thenReturn(expectedCount);
        
        // 执行测试
        int result = followService.getFollowerCount(userId);
        
        // 验证结果
        assertEquals(expectedCount, result);
    }
} 