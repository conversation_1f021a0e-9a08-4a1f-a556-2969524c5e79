package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.entity.Config;
import com.blog.mapper.ConfigMapper;
import com.blog.service.ConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统配置服务实现类
 */
@Service
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, Config> implements ConfigService {

    @Override
    public String getValueByKey(String configKey) {
        return baseMapper.getValueByKey(configKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateValueByKey(String configKey, String configValue) {
        int result = baseMapper.updateValueByKey(configKey, configValue);
        return result > 0;
    }

    @Override
    public Map<String, String> getAllConfigs() {
        List<Config> configs = list();
        Map<String, String> configMap = new HashMap<>();
        for (Config config : configs) {
            configMap.put(config.getConfigKey(), config.getConfigValue());
        }
        return configMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateConfigs(Map<String, String> configs) {
        try {
            for (Map.Entry<String, String> entry : configs.entrySet()) {
                updateValueByKey(entry.getKey(), entry.getValue());
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新配置失败", e);
            return false;
        }
    }

    @Override
    public Map<String, String> getSiteInfo() {
        Map<String, String> siteInfo = new HashMap<>();
        siteInfo.put("site_title", getValueByKey("site_title"));
        siteInfo.put("site_description", getValueByKey("site_description"));
        siteInfo.put("site_keywords", getValueByKey("site_keywords"));
        siteInfo.put("site_logo", getValueByKey("site_logo"));
        siteInfo.put("icp", getValueByKey("icp"));
        return siteInfo;
    }

    @Override
    public Map<String, String> getCommentConfig() {
        Map<String, String> commentConfig = new HashMap<>();
        commentConfig.put("comment_audit", getValueByKey("comment_audit"));
        commentConfig.put("allow_anonymous_comment", getValueByKey("allow_anonymous_comment"));
        return commentConfig;
    }

    @Override
    public Map<String, String> getEmailConfig() {
        Map<String, String> emailConfig = new HashMap<>();
        emailConfig.put("smtp_server", getValueByKey("smtp_server"));
        emailConfig.put("smtp_port", getValueByKey("smtp_port"));
        emailConfig.put("from_email", getValueByKey("from_email"));
        emailConfig.put("email_username", getValueByKey("email_username"));
        emailConfig.put("email_password", getValueByKey("email_password"));
        return emailConfig;
    }
}
