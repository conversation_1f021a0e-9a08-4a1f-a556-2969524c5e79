import request from '@/utils/request'

/**
 * 关注相关API
 */

/**
 * 关注用户
 * @param {Number} userId 要关注的用户ID
 * @returns {Promise}
 */
export function followUser(userId) {
  return request({
    url: '/follow',
    method: 'post',
    data: { userId }
  })
}

/**
 * 取消关注用户
 * @param {Number} userId 要取消关注的用户ID
 * @returns {Promise}
 */
export function unfollowUser(userId) {
  return request({
    url: '/follow/unfollow',
    method: 'post',
    data: { userId }
  })
}

/**
 * 获取关注状态和统计数据
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getFollowStatus(userId) {
  return request({
    url: `/follow/status/${userId}`,
    method: 'get'
  })
}

/**
 * 获取当前用户的关注列表
 * @returns {Promise}
 */
export function getMyFollowingList() {
  return request({
    url: '/follow/following',
    method: 'get'
  })
}

/**
 * 获取当前用户的粉丝列表
 * @returns {Promise}
 */
export function getMyFollowerList() {
  return request({
    url: '/follow/followers',
    method: 'get'
  })
}

/**
 * 获取指定用户的关注列表
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getUserFollowingList(userId) {
  return request({
    url: `/follow/user/${userId}/following`,
    method: 'get'
  })
}

/**
 * 获取指定用户的粉丝列表
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getUserFollowerList(userId) {
  return request({
    url: `/follow/user/${userId}/followers`,
    method: 'get'
  })
} 