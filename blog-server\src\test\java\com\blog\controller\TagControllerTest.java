package com.blog.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.blog.dto.TagDTO;
import com.blog.entity.Article;
import com.blog.entity.ArticleTag;
import com.blog.entity.Tag;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.ArticleTagMapper;
import com.blog.mapper.TagMapper;

/**
 * 集成测试：TagController
 * 描述：测试标签管理相关的API端点
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
public class TagControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TagMapper tagMapper;

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private ArticleTagMapper articleTagMapper;

    /**
     * 测试用例：获取所有标签列表
     * 场景：当用户（需要认证）请求标签列表时
     * 预期：应返回200 OK状态，且响应体中包含一个JSON数组格式的标签数据
     */
    @Test
    @WithMockUser // 模拟一个已认证的用户
    public void shouldGetAllTags() throws Exception {
        // 执行一个GET请求到/tags
        mockMvc.perform(get("/tags"))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：响应的Content-Type应为application/json
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                // 断言3：响应体JSON中，'code'字段的值应为200
                .andExpect(jsonPath("$.code").value(200))
                // 断言4：响应体JSON中，'data'字段应为一个数组 (表示标签列表)
                .andExpect(jsonPath("$.data").isArray());
    }

    /**
     * 测试用例：管理员应能成功添加一个新标签
     * 场景：当管理员发送一个包含新标签信息的POST请求时
     * 预期：应返回200 OK状态，响应体中包含新创建标签的ID
     */
    @Test
    @WithMockUser(roles = "ADMIN") // 模拟一个角色为'ADMIN'的用户
    public void shouldAddTagAsAdmin() throws Exception {
        // 准备一个待添加的标签DTO对象
        TagDTO newTag = new TagDTO();
        newTag.setName("TDD测试标签");

        // 执行POST请求，并将DTO对象序列化为JSON字符串作为请求体
        mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newTag)))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：响应体JSON中，'code'字段的值应为200
                .andExpect(jsonPath("$.code").value(200))
                // 断言3：响应体JSON中，'data'字段应为一个数字（新ID）
                .andExpect(jsonPath("$.data").isNumber());
    }

    /**
     * 测试用例：非管理员用户应被禁止添加标签
     * 场景：当一个没有管理员角色的普通用户尝试发送POST请求添加标签时
     * 预期：应返回403 Forbidden状态，拒绝该次操作
     */
    @Test
    @WithMockUser(roles = "USER") // 模拟一个角色为'USER'的普通用户
    public void shouldForbidAddTagForNonAdmin() throws Exception {
        // 准备一个待添加的标签DTO对象
        TagDTO newTag = new TagDTO();
        newTag.setName("违规测试标签");

        // 执行POST请求
        mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newTag)))
                // 断言：HTTP状态码应为403 (Forbidden)，因为用户权限不足
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：管理员应能成功更新一个标签
     * 场景：管理员提供一个有效的标签ID和更新信息
     * 预期：应返回200 OK状态，并且标签信息得到更新
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldUpdateTagAsAdmin() throws Exception {
        // 步骤 1: 创建一个标签用于更新
        TagDTO originalTag = new TagDTO();
        originalTag.setName("原始标签名");
        String createResponse = mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(originalTag)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long tagId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data")).longValue();

        // 步骤 2: 准备更新数据并执行PUT请求
        TagDTO updatedInfo = new TagDTO();
        updatedInfo.setName("更新后的标签名");
        mockMvc.perform(put("/tags/" + tagId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 步骤 3: (验证) 确认标签信息已被修改
        mockMvc.perform(get("/tags/" + tagId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.name").value("更新后的标签名"));
    }

    /**
     * 测试用例：非管理员用户应被禁止更新标签
     * 场景：普通用户尝试更新标签
     * 预期：应返回403 Forbidden
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldForbidUpdateTagForNonAdmin() throws Exception {
        // 步骤 1: (作为admin) 先创建一个标签
        TagDTO originalTag = new TagDTO();
        originalTag.setName("标签禁止更新测试");
        String createResponse = mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(originalTag)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long tagId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data")).longValue();

        // 步骤 2: 准备更新数据
        TagDTO updatedInfo = new TagDTO();
        updatedInfo.setName("不应成功的更新");

        // 步骤 3: (切换为普通用户) 尝试执行PUT请求
        mockMvc.perform(put("/tags/" + tagId)
                        .with(user("normalUser").roles("USER"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：尝试更新一个不存在的标签时应返回404
     * 场景：管理员向一个不存在的标签ID发送更新请求
     * 预期：应返回404 Not Found
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldReturnNotFoundWhenUpdatingNonExistentTag() throws Exception {
        long nonExistentTagId = 99999L;
        TagDTO updatedInfo = new TagDTO();
        updatedInfo.setName("不存在的标签");

        mockMvc.perform(put("/tags/" + nonExistentTagId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：管理员应能成功删除一个标签
     * 场景：管理员提供一个有效的标签ID进行删除
     * 预期：应返回200 OK状态，并且该标签确实被删除
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldDeleteTagAsAdmin() throws Exception {
        // 步骤 1: 创建一个标签用于删除
        TagDTO tagToDelete = new TagDTO();
        tagToDelete.setName("待删除标签");
        String createResponse = mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(tagToDelete)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long tagId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data")).longValue();

        // 步骤 2: 执行删除请求
        mockMvc.perform(delete("/tags/" + tagId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 步骤 3: (验证) 确认标签已被删除 (再次获取应返回404)
        mockMvc.perform(get("/tags/" + tagId))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：非管理员用户应被禁止删除标签
     * 场景：普通用户尝试删除标签
     * 预期：应返回403 Forbidden
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldForbidDeleteTagForNonAdmin() throws Exception {
        // 步骤 1: (作为admin) 先创建一个标签
        TagDTO tagToDelete = new TagDTO();
        tagToDelete.setName("标签禁止删除测试");
        String createResponse = mockMvc.perform(post("/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(tagToDelete)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long tagId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data")).longValue();

        // 步骤 2: (切换为普通用户) 尝试执行删除请求
        mockMvc.perform(delete("/tags/" + tagId)
                        .with(user("normalUser").roles("USER")))
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：尝试删除一个不存在的标签时应返回404
     * 场景：管理员向一个不存在的标签ID发送删除请求
     * 预期：应返回404 Not Found
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldReturnNotFoundWhenDeletingNonExistentTag() throws Exception {
        long nonExistentTagId = 99999L;

        mockMvc.perform(delete("/tags/" + nonExistentTagId))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：应禁止删除有关联文章的标签
     * 场景：一个标签已经与至少一篇文章关联，此时尝试删除该标签
     * 预期：应返回409 Conflict，并提示无法删除
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldForbidDeletionOfTagWithArticles() throws Exception {
        // 步骤 1: 准备数据 - 创建一个标签
        Tag tag = new Tag();
        tag.setName("有关联文章的标签");
        tagMapper.insert(tag);

        // 步骤 2: 准备数据 - 创建一篇文章
        Article article = new Article();
        article.setTitle("测试文章");
        article.setContent("这是一篇用于测试的文章内容");
        article.setAuthorId(1L); // 假设存在ID为1的用户
        article.setCategoryId(1L); // 假设存在ID为1的分类
        articleMapper.insert(article);

        // 步骤 3: 准备数据 - 将文章和标签关联起来
        articleTagMapper.insert(new ArticleTag(article.getId(), tag.getId()));

        // 步骤 4: 执行删除请求
        mockMvc.perform(delete("/tags/" + tag.getId()))
                .andExpect(status().isConflict());
    }
} 