package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.blog.entity.Article;
import com.blog.entity.ArticleCollection;
import com.blog.entity.Category;
import com.blog.entity.User;
import com.blog.mapper.ArticleCollectionMapper;
import com.blog.mapper.ArticleMapper;
import com.blog.service.ArticleService;
import com.blog.service.CategoryService;
import com.blog.service.CollectionService;
import com.blog.service.NotificationService;
import com.blog.service.UserService;
import com.blog.vo.ArticleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收藏服务实现类
 */
@Service
public class CollectionServiceImpl implements CollectionService {

    @Autowired
    private ArticleCollectionMapper articleCollectionMapper;
    
    @Autowired
    private ArticleMapper articleMapper;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private ArticleService articleService;

    @Autowired
    private NotificationService notificationService;

    @Override
    @Transactional
    public boolean collectArticle(Long articleId, Long userId) {
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已收藏
        if (hasCollectedArticle(articleId, userId)) {
            return true; // 已收藏，直接返回成功
        }
        
        // 创建收藏记录
        ArticleCollection collection = new ArticleCollection();
        collection.setArticleId(articleId);
        collection.setUserId(userId);
        collection.setCreateTime(new Date());
        
        // 插入收藏记录
        boolean success = articleCollectionMapper.insert(collection) > 0;

        if (success) {
            // 发送收藏通知
            notificationService.sendCollectNotification(articleId, userId);
        }

        return success;
    }

    @Override
    @Transactional
    public boolean uncollectArticle(Long articleId, Long userId) {
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已收藏
        if (!hasCollectedArticle(articleId, userId)) {
            return true; // 未收藏，直接返回成功
        }
        
        // 构建删除条件
        LambdaQueryWrapper<ArticleCollection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ArticleCollection::getArticleId, articleId)
                .eq(ArticleCollection::getUserId, userId);
        
        // 删除收藏记录
        return articleCollectionMapper.delete(queryWrapper) > 0;
    }

    @Override
    public boolean hasCollectedArticle(Long articleId, Long userId) {
        // 查询用户收藏状态
        int count = articleCollectionMapper.selectUserCollectionStatus(articleId, userId);
        return count > 0;
    }

    @Override
    public List<ArticleVO> getUserCollections(Long userId) {
        // 查询用户收藏的文章ID列表
        List<Long> articleIds = articleCollectionMapper.selectUserCollectionIds(userId);
        if (articleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取所有收藏的文章
        List<ArticleVO> result = new ArrayList<>();
        for (Long articleId : articleIds) {
            try {
                // 使用ArticleService获取完整的文章信息
                ArticleVO article = articleService.getArticleDetail(articleId);
                if (article != null) {
                    result.add(article);
                }
            } catch (Exception e) {
                // 如果文章不存在或获取失败，忽略这篇文章
                continue;
            }
        }
        
        return result;
    }

    @Override
    public int getArticleCollectionCount(Long articleId) {
        // 查询文章收藏数
        return articleCollectionMapper.selectCollectionCount(articleId);
    }
} 