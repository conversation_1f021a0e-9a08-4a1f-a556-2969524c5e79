import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { siteConfig } from '@/utils/siteConfig'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'


// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/register/index.vue'),
    meta: { title: '注册' }
  },
  {
    path: '/',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '首页' }
      },
      {
        path: 'article/:id',
        name: 'Article',
        component: () => import('@/views/article/index.vue'),
        meta: { title: '文章详情' }
      },
      {
        path: 'category/:id',
        name: 'Category',
        component: () => import('@/views/category/index.vue'),
        meta: { title: '分类' }
      },
      {
        path: 'tag/:id',
        name: 'Tag',
        component: () => import('@/views/tag/index.vue'),
        meta: { title: '标签' }
      },
      {
        path: 'archive',
        name: 'Archive',
        component: () => import('@/views/archive/index.vue'),
        meta: { title: '归档' }
      },
      {
        path: 'search',
        name: 'Search',
        component: () => import('@/views/search/index.vue'),
        meta: { title: '搜索' }
      }
    ]
  },
  {
    path: '/admin',
    component: () => import('@/views/admin/layout/index.vue'),
    redirect: '/admin/dashboard',
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/admin/dashboard/index.vue'),
        meta: { title: '仪表盘' }
      },
      {
        path: 'article',
        name: 'ArticleManage',
        component: () => import('@/views/admin/article/index.vue'),
        meta: { title: '文章管理' }
      },
      {
        path: 'article/edit/:id',
        name: 'ArticleEdit',
        component: () => import('@/views/admin/article/edit.vue'),
        meta: { title: '编辑文章' }
      },
      {
        path: 'article/add',
        name: 'ArticleAdd',
        component: () => import('@/views/admin/article/add.vue'),
        meta: { title: '添加文章' }
      },
      {
        path: 'category',
        name: 'CategoryManage',
        component: () => import('@/views/admin/category/index.vue'),
        meta: { title: '分类管理' }
      },
      {
        path: 'tag',
        name: 'TagManage',
        component: () => import('@/views/admin/tag/index.vue'),
        meta: { title: '标签管理' }
      },
      {
        path: 'comment',
        name: 'CommentManage',
        component: () => import('@/views/admin/CommentManagement.vue'),
        meta: { title: '评论管理' }
      },
      {
        path: 'user',
        name: 'UserManage',
        component: () => import('@/views/admin/user/index.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'setting',
        name: 'Setting',
        component: () => import('@/views/admin/setting/index.vue'),
        meta: { title: '系统设置' }
      },
      {
        path: 'email-template',
        name: 'EmailTemplate',
        component: () => import('@/views/admin/email-template/index.vue'),
        meta: { title: '邮件模板管理' }
      }
    ]
  },
  {
    path: '/user',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/user/profile',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/profile/index.vue'),
        meta: { title: '个人资料' }
      },
      {
        path: 'collection',
        name: 'UserCollection',
        component: () => import('@/views/user/collection/index.vue'),
        meta: { title: '我的收藏' }
      },
      {
        path: 'comment',
        name: 'UserComment',
        component: () => import('@/views/user/comment/index.vue'),
        meta: { title: '我的评论' }
      },
      {
        path: 'notifications',
        name: 'NotificationCenter',
        component: () => import('@/views/user/NotificationCenter.vue'),
        meta: { title: '消息中心' }
      }
    ]
  },
  {
    path: '/403',
    name: 'AccessDenied',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '无权访问' }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]



const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题 - 使用动态获取的网站标题
  const siteTitle = siteConfig.value.title || '个人动态博客'
  document.title = to.meta.title ? `${to.meta.title} - ${siteTitle}` : siteTitle
  
  // 获取当前token
  const token = getToken()
  const userStore = useUserStore()
  
  // 如果有token但用户状态未加载，尝试加载用户信息
  if (token && !userStore.isLogin) {
    try {
      await userStore.getInfo()
    } catch (error) {
      console.error('获取用户信息失败', error)
      // 如果token无效，清除状态并重定向到登录页
      userStore.resetState()
      next('/login')
      return
    }
  }
  
  // 判断该路由是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 检查路由是否需要特定角色
      const requiredRoles = to.matched.flatMap(record => record.meta.roles).filter(Boolean);

      if (requiredRoles.length > 0 && !requiredRoles.includes(userStore.role)) {
        // 如果用户角色不匹配，重定向到403页面
        next('/403');
      } else {
        // 角色匹配或路由不需要特定角色，放行
        next();
      }
    }
  } else {
    // 管理员登录后自动跳转管理后台的逻辑只在初次登录时执行
    // 登录后通过导航访问前台页面是允许的
    if ((to.path === '/home' || to.path === '/') && userStore.isAdmin && from.path === '/login') {
      // 只有从登录页来时才重定向管理员到后台
      next('/admin/dashboard')
    } else {
      next()
    }
  }
})

// 路由后置守卫
router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

export default router 