# 个人动态博客系统 - 第二阶段测试指南

本文档提供了测试文章管理模块的指南，现在完全基于自动化测试流程。

## 🛠️ 环境准备

请确保以下环境已准备就绪：

1. **数据库**：MySQL数据库已启动并已执行最新的`blog_system.sql`脚本。
2. **后端服务**：Spring Boot应用可正常编译。
3. **前端应用**：Vue应用可正常启动。

## 🧪 后端API自动化测试

文章管理模块的后端API功能由 `src/test/java/com/blog/` 下的自动化测试用例保证 (例如 `ArticleControllerTest.java`，待创建)。

### 计划覆盖的API功能点
- **创建文章**: 验证 `/api/articles` (POST) 接口能否成功创建文章（任何登录用户都可以）。
- **获取文章列表**: 验证 `/api/articles` (GET) 接口能否返回分页的文章列表。
- **获取文章详情**: 验证 `/api/articles/{id}` (GET) 接口能否返回单个文章的正确信息。
- **更新文章**: 验证 `/api/articles/{id}` (PUT) 接口能否成功更新文章（作者本人或管理员）。
- **删除文章**: 验证 `/api/articles/{id}` (DELETE) 接口能否成功删除文章（作者本人或管理员）。
- **权限测试**: 验证普通用户无法更新或删除他人文章，但管理员可以操作任何文章。

### 如何运行
与第一阶段相同，进入后端项目根目录，执行以下命令即可运行所有相关测试：

```bash
mvn -f blog-server/pom.xml test
```

## 🖥️ 前端端到端(E2E)测试

前端的关键业务流程将通过Cypress自动化测试来保障。这能模拟真实用户在浏览器中的操作，确保前后端系统协同工作顺畅。

### 如何运行

1.  确保您的前端开发服务器正在运行 (`npm run dev`)。
2.  打开Cypress测试运行器 (`npx cypress open`)。
3.  在测试列表（Specs）中，点击您想要运行的测试文件。

*如果您是首次运行，请参照第一阶段指南中的 "3.1 首次运行与配置" 完成初始化。*

### 计划中的测试场景 (文章管理)
- **文章发布流程** (`article-publish.cy.js`): 模拟用户登录 -> 进入文章管理 -> 创建新文章 -> 填写表单并发布 -> 在文章列表中验证新文章是否存在。
- **文章编辑流程** (`article-edit.cy.js`): 在文章列表中找到一篇文章 -> 点击编辑 -> 修改内容并保存 -> 验证修改是否生效。
- **文章删除流程** (`article-delete.cy.js`): 在文章列表中找到一篇文章 -> 点击删除 -> 在弹窗中确认 -> 验证文章已从列表中消失。

---

## 附录：分类管理模块的TDD最佳实践

在第三阶段的分类管理模块开发中，我们全面采用了测试驱动开发（TDD）的方法论，为`CategoryController`构建了一套极其健壮的集成测试。这套测试不仅保证了功能的正确性，还驱动了后端API设计的优化。

### 1. TDD开发流程

我们严格遵循"**红-绿-重构**"的循环：
1.  **红**：先编写一个失败的测试用例，明确定义出我们期望实现的功能或行为。
2.  **绿**：编写最简单、最直接的产品代码，让测试用例通过。
3.  **重构**：在测试用例的保护下，对产品代码进行优化和重构，提升代码质量和设计。

### 2. `CategoryControllerTest` 测试用例全景

通过TDD，我们为`CategoryController`的 **Create, Read, Update, Delete (CRUD)** 操作编写了全面的集成测试，覆盖了所有关键路径和边界情况：

-   **Create (POST /categories)**
    -   `shouldAddCategoryAsAdmin`: 管理员应能成功添加新分类。
    -   `shouldForbidAddCategoryForNonAdmin`: 普通用户尝试添加时，应返回 **403 Forbidden**。

-   **Read (GET /categories)**
    -   `shouldGetAllCategories`: 任何已认证用户都能获取分类列表。
    -   （单个获取的测试隐含在Update和Delete的验证步骤中）。

-   **Update (PUT /categories/{id})**
    -   `shouldUpdateCategoryAsAdmin`: 管理员应能成功更新一个分类。
    -   `shouldForbidUpdateCategoryForNonAdmin`: 普通用户尝试更新时，应返回 **403 Forbidden**。
    -   `shouldReturnNotFoundWhenUpdatingNonExistentCategory`: 尝试更新不存在的分类时，应返回 **404 Not Found**。

-   **Delete (DELETE /categories/{id})**
    -   `shouldDeleteCategoryAsAdmin`: 管理员应能成功删除一个"干净"的分类。
    -   `shouldForbidDeleteCategoryForNonAdmin`: 普通用户尝试删除时，应返回 **403 Forbidden**。
    -   `shouldReturnNotFoundWhenDeletingNonExistentCategory`: 尝试删除不存在的分类时，应返回 **404 Not Found**。
    -   `shouldForbidDeletionOfCategoryWithChildren`: 尝试删除带子分类的分类时，应返回 **409 Conflict**。
    -   `shouldForbidDeletionOfCategoryWithArticles`: 尝试删除含关联文章的分类时，应返回 **409 Conflict**。

### 3. TDD如何驱动更优设计

在TDD过程中，测试失败的信息驱动了我们对异常处理机制的重大重构：

-   **背景**: 最初，所有业务错误（如"资源不存在"、"存在冲突"）都抛出通用的`BusinessException`，导致API层返回模糊的`200 OK`或`500`错误码。
-   **TDD驱动的重构**:
    1.  `shouldReturnNotFound...` 测试的失败，促使我们创建了专用的`NotFoundException`。
    2.  `shouldForbidDeletion...` 测试的失败，促使我们创建了专用的`ConflictException`。
    3.  为了让这些新异常能映射到正确的HTTP状态码（404, 409）并返回统一的JSON体，我们进一步重构了`GlobalExceptionHandler`，为其添加了针对这些特定异常的处理方法。

这个过程完美展示了TDD如何帮助我们编写出不仅功能正确，而且API设计更优雅、更符合RESTful规范的后端服务。

### 4. 最终成果

`blog-server/src/test/java/com/blog/controller/CategoryControllerTest.java` 文件是本次TDD实践的最终成果。它既是分类管理模块功能质量的"活文档"，也是后续模块进行自动化测试的最佳实践参考。

---

### 5. 案例2: 标签模块的业务约束测试

在标签模块的开发中，我们同样运用TDD来处理更复杂的业务约束，特别是删除操作。

-   **驱动测试 (The Driving Test)**: `shouldForbidDeletionOfTagWithArticles`
    -   **目标**: 验证当一个标签已与文章关联时，系统应禁止删除该标签。
    -   **场景**: 在测试方法中，我们通过 `ArticleMapper` 和 `ArticleTagMapper` 直接操作数据库，手动创建一个标签，一篇文章，并将它们关联起来。
    -   **预期**: 客户端尝试通过 `DELETE /tags/{id}` 删除该标签时，服务器应返回 **409 Conflict** 状态码。

-   **TDD驱动的实现**:
    1.  **红灯 (Red)**: 初次运行此测试时，测试失败。因为当时的 `deleteTag` 方法在删除标签前，仅仅删除了 `article_tag` 的关联记录，然后删除了标签本身，最终返回了 `200 OK`，与预期的 `409 Conflict` 不符。
    2.  **绿灯 (Green)**: 为了让测试通过，我们修改了 `TagServiceImpl` 中的 `deleteTag` 方法。在删除逻辑的最开始，我们增加了前置检查：`baseMapper.countArticleByTagId(id)`。如果发现关联的文章数量大于0，则不再继续执行删除，而是直接抛出 `ConflictException`。
    3.  **重构 (Refactor)**: 我们的 `GlobalExceptionHandler` 已经能够正确处理 `ConflictException` 并将其映射为 `409` 状态码，因此这一步无需重构。

-   **成果**: `blog-server/src/test/java/com/blog/controller/TagControllerTest.java` 中新增的测试用例再次验证了TDD的价值，它确保了我们的API不仅能处理常规的CRUD，还能正确执行关键的业务保护逻辑。 