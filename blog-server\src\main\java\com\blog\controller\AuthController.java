package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.LoginDTO;
import com.blog.dto.RegisterDTO;
import com.blog.entity.User;
import com.blog.service.UserService;
import com.blog.vo.LoginVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Resource
    private UserService userService;
    
    @Value("${jwt.tokenHead}")
    private String tokenHead;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<?> register(@Validated @RequestBody RegisterDTO registerDTO) {
        User user = new User();
        BeanUtils.copyProperties(registerDTO, user);
        
        if (userService.register(user)) {
            return Result.success();
        }
        return Result.failed("注册失败");
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<?> login(@Validated @RequestBody LoginDTO loginDTO) {
        String token = userService.login(loginDTO.getUsername(), loginDTO.getPassword());
        
        // 获取用户信息
        User user = userService.getUserByUsername(loginDTO.getUsername());
        
        // 构建返回数据
        LoginVO loginVO = LoginVO.builder()
                .token(token)
                .tokenHead(tokenHead)
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .role(user.getRole())
                .build();
        
        return Result.success(loginVO);
    }
}