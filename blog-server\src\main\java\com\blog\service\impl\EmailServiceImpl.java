package com.blog.service.impl;

import com.blog.service.ConfigService;
import com.blog.service.EmailService;
import com.blog.service.EmailTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 邮件服务实现类
 */
@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private ConfigService configService;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Override
    public boolean sendTextMail(String to, String subject, String content) {
        try {
            // 获取邮件配置
            Map<String, String> emailConfig = configService.getEmailConfig();
            
            // 创建邮件发送器
            JavaMailSender mailSender = createMailSender(emailConfig);
            if (mailSender == null) {
                log.error("邮件配置不完整，无法发送邮件");
                return false;
            }

            // 创建简单邮件消息
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailConfig.get("from_email"));
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            // 发送邮件
            mailSender.send(message);
            log.info("文本邮件发送成功: to={}, subject={}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("发送文本邮件失败: to={}, subject={}, error={}", to, subject, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean sendHtmlMail(String to, String subject, String htmlContent) {
        try {
            // 获取邮件配置
            Map<String, String> emailConfig = configService.getEmailConfig();
            
            // 创建邮件发送器
            JavaMailSender mailSender = createMailSender(emailConfig);
            if (mailSender == null) {
                log.error("邮件配置不完整，无法发送邮件");
                return false;
            }

            // 创建MIME邮件消息
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            
            helper.setFrom(emailConfig.get("from_email"));
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // true表示HTML格式

            // 发送邮件
            mailSender.send(mimeMessage);
            log.info("HTML邮件发送成功: to={}, subject={}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("发送HTML邮件失败: to={}, subject={}, error={}", to, subject, e.getMessage());
            return false;
        }
    }

    @Override
    public String testEmailConfig(Map<String, String> emailConfig) {
        try {
            // 验证配置参数
            String validationResult = validateEmailConfig(emailConfig);
            if (validationResult != null) {
                return validationResult;
            }

            // 创建邮件发送器进行测试
            JavaMailSender mailSender = createMailSender(emailConfig);
            if (mailSender == null) {
                return "邮件配置创建失败";
            }

            // 发送测试邮件到发件人自己
            String testEmail = emailConfig.get("from_email");

            // 尝试使用模板，如果模板不存在则使用默认内容
            String subject;
            String content;

            try {
                // 准备模板变量
                Map<String, String> variables = new HashMap<>();
                variables.put("SMTP_SERVER", emailConfig.get("smtp_server"));
                variables.put("SMTP_PORT", emailConfig.get("smtp_port"));
                variables.put("TEST_TIME", java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 使用模板渲染
                Map<String, String> preview = emailTemplateService.previewTemplate("TEST_EMAIL", variables);
                subject = preview.get("subject");
                content = preview.get("content");
            } catch (Exception e) {
                // 如果模板不存在或出错，使用默认内容
                log.warn("使用邮件模板失败，使用默认内容: {}", e.getMessage());
                subject = "博客系统邮件配置测试";
                content = "这是一封测试邮件，如果您收到此邮件，说明邮件配置正确！\n\n发送时间: " +
                         java.time.LocalDateTime.now().toString();
            }

            // 判断是否为HTML内容，如果是则发送HTML邮件
            if (content.contains("<") && content.contains(">")) {
                // 发送HTML邮件
                MimeMessage mimeMessage = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

                helper.setFrom(testEmail);
                helper.setTo(testEmail);
                helper.setSubject(subject);
                helper.setText(content, true); // true表示HTML格式

                mailSender.send(mimeMessage);
            } else {
                // 发送文本邮件
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(testEmail);
                message.setTo(testEmail);
                message.setSubject(subject);
                message.setText(content);

                mailSender.send(message);
            }
            
            return "邮件配置测试成功！测试邮件已发送到 " + testEmail;
        } catch (Exception e) {
            log.error("邮件配置测试失败: {}", e.getMessage());
            return "邮件配置测试失败: " + e.getMessage();
        }
    }

    @Override
    public boolean sendNotificationMail(String to, String title, String content, String type) {
        try {
            // 构建通知邮件的HTML内容
            String htmlContent = buildNotificationHtml(title, content, type);
            
            // 发送HTML邮件
            return sendHtmlMail(to, title, htmlContent);
        } catch (Exception e) {
            log.error("发送通知邮件失败: to={}, title={}, error={}", to, title, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean sendMailWithConfig(Map<String, String> emailConfig, String to, String subject, String content, boolean isHtml) {
        try {
            // 验证配置
            String validationResult = validateEmailConfig(emailConfig);
            if (validationResult != null) {
                log.error("邮件配置验证失败: {}", validationResult);
                return false;
            }

            // 创建邮件发送器
            JavaMailSender mailSender = createMailSender(emailConfig);
            if (mailSender == null) {
                return false;
            }

            if (isHtml) {
                // 发送HTML邮件
                MimeMessage mimeMessage = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
                
                helper.setFrom(emailConfig.get("from_email"));
                helper.setTo(to);
                helper.setSubject(subject);
                helper.setText(content, true);

                mailSender.send(mimeMessage);
            } else {
                // 发送文本邮件
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(emailConfig.get("from_email"));
                message.setTo(to);
                message.setSubject(subject);
                message.setText(content);

                mailSender.send(message);
            }

            log.info("邮件发送成功: to={}, subject={}, isHtml={}", to, subject, isHtml);
            return true;
        } catch (Exception e) {
            log.error("发送邮件失败: to={}, subject={}, error={}", to, subject, e.getMessage());
            return false;
        }
    }

    /**
     * 创建邮件发送器
     * @param emailConfig 邮件配置
     * @return JavaMailSender实例
     */
    private JavaMailSender createMailSender(Map<String, String> emailConfig) {
        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            
            // 设置SMTP服务器配置
            mailSender.setHost(emailConfig.get("smtp_server"));
            mailSender.setPort(Integer.parseInt(emailConfig.get("smtp_port")));
            mailSender.setUsername(emailConfig.get("email_username"));
            mailSender.setPassword(emailConfig.get("email_password"));

            // 设置邮件属性
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.debug", "false");
            
            // 根据端口设置SSL
            int port = Integer.parseInt(emailConfig.get("smtp_port"));
            if (port == 465) {
                props.put("mail.smtp.ssl.enable", "true");
            } else if (port == 587) {
                props.put("mail.smtp.starttls.enable", "true");
            }

            return mailSender;
        } catch (Exception e) {
            log.error("创建邮件发送器失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证邮件配置
     * @param emailConfig 邮件配置
     * @return 验证结果，null表示验证通过
     */
    private String validateEmailConfig(Map<String, String> emailConfig) {
        if (emailConfig == null || emailConfig.isEmpty()) {
            return "邮件配置为空";
        }

        String smtpServer = emailConfig.get("smtp_server");
        String smtpPort = emailConfig.get("smtp_port");
        String fromEmail = emailConfig.get("from_email");
        String username = emailConfig.get("email_username");
        String password = emailConfig.get("email_password");

        if (!StringUtils.hasText(smtpServer)) {
            return "SMTP服务器不能为空";
        }
        if (!StringUtils.hasText(smtpPort)) {
            return "SMTP端口不能为空";
        }
        if (!StringUtils.hasText(fromEmail)) {
            return "发件人邮箱不能为空";
        }
        if (!StringUtils.hasText(username)) {
            return "邮箱用户名不能为空";
        }
        if (!StringUtils.hasText(password)) {
            return "邮箱密码不能为空";
        }

        // 验证端口号格式
        try {
            int port = Integer.parseInt(smtpPort);
            if (port <= 0 || port > 65535) {
                return "SMTP端口号无效";
            }
        } catch (NumberFormatException e) {
            return "SMTP端口号格式错误";
        }

        return null; // 验证通过
    }

    /**
     * 构建通知邮件的HTML内容
     * @param title 标题
     * @param content 内容
     * @param type 类型
     * @return HTML内容
     */
    private String buildNotificationHtml(String title, String content, String type) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>" + title + "</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>" + title + "</h2>" +
                "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>" +
                "<p style='margin: 0; font-size: 16px;'>" + content + "</p>" +
                "</div>" +
                "<p style='color: #7f8c8d; font-size: 14px; margin-top: 30px;'>" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }
}
