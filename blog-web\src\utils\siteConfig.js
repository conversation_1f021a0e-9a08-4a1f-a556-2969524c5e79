import { ref } from 'vue'
import { getToken } from './auth'

// 全局网站配置状态
export const siteConfig = ref({
  title: '个人动态博客',
  description: '',  // 默认不显示描述，等待从服务器获取
  keywords: '博客,技术,生活',
  logo: '',
  icp: '',
  allowAnonymousComment: 'false'  // 是否允许匿名评论
})

/**
 * 获取网站配置信息
 */
export async function fetchSiteConfig() {
  try {
    // 网站基本信息应该是公开的，但为了兼容性，如果有token就带上
    const headers = {}
    const token = getToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch('/api/config/site', { headers })
    const result = await response.json()

    if (result.code === 200 && result.data) {
      const data = result.data

      // 处理Logo URL，确保是完整的访问路径
      let logoUrl = data.site_logo || ''
      if (logoUrl && !logoUrl.startsWith('http') && !logoUrl.startsWith('/api')) {
        logoUrl = '/api' + logoUrl
      }

      siteConfig.value = {
        title: data.site_title || '个人动态博客',
        description: data.site_description || '',  // 如果没有配置描述，就不显示
        keywords: data.site_keywords || '博客,技术,生活',
        logo: logoUrl,
        icp: data.icp || ''
      }

      // 更新页面标题和SEO信息
      document.title = siteConfig.value.title
      updateSEOTags()
    }
  } catch (error) {
    console.error('获取网站配置失败', error)
  }
}

/**
 * 获取网站标题（兼容旧接口）
 */
export async function fetchSiteTitle() {
  try {
    // 网站标题应该是公开的，但为了兼容性，如果有token就带上
    const headers = {}
    const token = getToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch('/api/config/site_title', { headers })
    const result = await response.json()

    if (result.code === 200 && result.data) {
      siteConfig.value.title = result.data
      document.title = result.data
      return result.data
    }
  } catch (error) {
    console.error('获取网站标题失败', error)
  }

  return siteConfig.value.title
}

/**
 * 更新页面SEO标签
 */
function updateSEOTags() {
  // 更新description meta标签
  let descMeta = document.querySelector('meta[name="description"]')
  if (descMeta) {
    descMeta.setAttribute('content', siteConfig.value.description)
  } else {
    descMeta = document.createElement('meta')
    descMeta.setAttribute('name', 'description')
    descMeta.setAttribute('content', siteConfig.value.description)
    document.head.appendChild(descMeta)
  }

  // 更新keywords meta标签
  let keywordsMeta = document.querySelector('meta[name="keywords"]')
  if (keywordsMeta) {
    keywordsMeta.setAttribute('content', siteConfig.value.keywords)
  } else {
    keywordsMeta = document.createElement('meta')
    keywordsMeta.setAttribute('name', 'keywords')
    keywordsMeta.setAttribute('content', siteConfig.value.keywords)
    document.head.appendChild(keywordsMeta)
  }
}

/**
 * 获取评论设置
 */
export async function fetchCommentConfig() {
  try {
    const headers = {}
    const token = getToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch('/api/config/comment', { headers })
    const result = await response.json()

    if (result.code === 200 && result.data) {
      siteConfig.value.allowAnonymousComment = result.data.allow_anonymous_comment || 'false'
    }
  } catch (error) {
    console.error('获取评论配置失败', error)
  }
}

/**
 * 刷新网站配置（在保存设置后调用）
 */
export function refreshSiteConfig() {
  return Promise.all([fetchSiteConfig(), fetchCommentConfig()])
}
