<template>
  <div class="archive-page">
    <h1>归档</h1>
    
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="Object.keys(archives).length === 0" class="empty">
      <el-empty description="暂无文章"></el-empty>
    </div>
    <div v-else class="archive-list">
      <div v-for="(year, yearIndex) in Object.keys(archives).sort((a, b) => b - a)" :key="yearIndex" class="archive-year">
        <h2>{{ year }}年</h2>
        <div v-for="(month, monthIndex) in Object.keys(archives[year]).sort((a, b) => b - a)" :key="monthIndex" class="archive-month">
          <h3>{{ month }}月</h3>
          <div v-for="article in archives[year][month]" :key="article.id" class="archive-item">
            <div class="archive-date">{{ formatDate(article.createTime) }}</div>
            <div class="archive-title">
              <router-link :to="`/article/${article.id}`">{{ article.title }}</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'ArchivePage',
  setup() {
    const loading = ref(true)
    const archives = ref({})
    
    const fetchArchives = async () => {
      loading.value = true
      try {
        // 调用归档API
        const response = await fetch('/api/articles/archive')
        const result = await response.json()

        if (result.code === 200) {
          archives.value = result.data || {}
        } else {
          console.error('获取归档数据失败', result.message)
          archives.value = {}
        }
      } catch (error) {
        console.error('获取归档数据失败', error)
        archives.value = {}
      } finally {
        loading.value = false
      }
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getDate()}日`
    }
    
    onMounted(() => {
      fetchArchives()
    })
    
    return {
      loading,
      archives,
      formatDate
    }
  }
}
</script>

<style scoped>
.archive-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.loading, .empty {
  margin-top: 30px;
}

.archive-list {
  margin-top: 30px;
}

.archive-year {
  margin-bottom: 30px;
}

.archive-year h2 {
  font-size: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.archive-month {
  margin-bottom: 20px;
  margin-left: 20px;
}

.archive-month h3 {
  font-size: 18px;
  margin-bottom: 15px;
}

.archive-item {
  display: flex;
  margin-bottom: 10px;
  margin-left: 20px;
}

.archive-date {
  width: 60px;
  color: #999;
}

.archive-title a {
  color: #333;
  text-decoration: none;
}

.archive-title a:hover {
  color: #409eff;
}
</style> 