// 🎨 现代化全局样式系统 - 最时髦的设计
// ✨ 基于最新设计趋势和最佳实践

// 📦 导入 Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

// ✨ CSS 自定义属性 - 现代化设计系统
:root {
  // 🎨 主色彩系统 - 现代渐变色
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --primary-color: var(--primary-600);
  --primary-light: var(--primary-400);
  --primary-dark: var(--primary-800);
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-gradient-hover: linear-gradient(135deg, #764ba2 0%, #667eea 100%);

  // 🌈 语义化色彩
  --success-color: #10b981;
  --success-light: #34d399;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;
  --error-color: #ef4444;
  --error-light: #f87171;
  --info-color: #3b82f6;
  --info-light: #60a5fa;

  // 🎭 中性色彩系统
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-400);
  --text-inverse: #ffffff;
  --text-muted: var(--gray-500);

  // 🏗️ 背景色彩
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-dark: rgba(0, 0, 0, 0.8);

  // 🎯 边框色彩
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);
  --border-focus: var(--primary-color);

  // 📏 间距系统 - 8px 基准
  --spacing-0: 0;
  --spacing-1: 0.25rem;   // 4px
  --spacing-2: 0.5rem;    // 8px
  --spacing-3: 0.75rem;   // 12px
  --spacing-4: 1rem;      // 16px
  --spacing-5: 1.25rem;   // 20px
  --spacing-6: 1.5rem;    // 24px
  --spacing-8: 2rem;      // 32px
  --spacing-10: 2.5rem;   // 40px
  --spacing-12: 3rem;     // 48px
  --spacing-16: 4rem;     // 64px
  --spacing-20: 5rem;     // 80px
  --spacing-24: 6rem;     // 96px

  // 📐 圆角系统
  --radius-none: 0;
  --radius-sm: 0.25rem;   // 4px
  --radius-md: 0.375rem;  // 6px
  --radius-lg: 0.5rem;    // 8px
  --radius-xl: 0.75rem;   // 12px
  --radius-2xl: 1rem;     // 16px
  --radius-3xl: 1.5rem;   // 24px
  --radius-full: 9999px;

  // 🌊 阴影系统 - 现代化层次
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.4);

  // 🎬 动画系统
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  // 📱 断点系统
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  // 🔤 字体系统
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

  --font-size-xs: 0.75rem;     // 12px
  --font-size-sm: 0.875rem;    // 14px
  --font-size-base: 1rem;      // 16px
  --font-size-lg: 1.125rem;    // 18px
  --font-size-xl: 1.25rem;     // 20px
  --font-size-2xl: 1.5rem;     // 24px
  --font-size-3xl: 1.875rem;   // 30px
  --font-size-4xl: 2.25rem;    // 36px
  --font-size-5xl: 3rem;       // 48px
  --font-size-6xl: 3.75rem;    // 60px

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  // 🎯 Z-index 系统
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 🌙 深色模式支持
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-500);
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --border-light: var(--gray-700);
    --border-medium: var(--gray-600);
    --border-dark: var(--gray-500);
    --bg-glass: rgba(0, 0, 0, 0.8);
  }
}

// 🔄 现代化重置样式
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
  overflow-x: hidden;
  min-height: 100vh;
}

// 🔗 现代化链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;

  &:hover {
    color: var(--primary-light);
    transform: translateY(-1px);
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }

  &:active {
    transform: translateY(0);
  }
}

// 🎨 现代化滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
  transition: background var(--transition-fast);

  &:hover {
    background: var(--gray-500);
  }
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

// 📦 现代化容器系统
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding-left: var(--spacing-6);
  padding-right: var(--spacing-6);

  @media (max-width: 768px) {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

// 🎴 现代化卡片系统
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-medium);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover::before {
    opacity: 1;
  }
}

.card-glass {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-elevated {
  box-shadow: var(--shadow-2xl);
}

.card-flat {
  box-shadow: none;
  border: 1px solid var(--border-light);
}

// 🔘 现代化按钮系统
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  position: relative;
  overflow: hidden;

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  &:active {
    transform: scale(0.98);
  }
}
// 🎨 按钮变体
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);

  &:hover {
    background: var(--primary-gradient-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
  }
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);

  &:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
    transform: translateY(-2px);
  }
}

.btn-ghost {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);

  &:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
  }
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

// 🎯 现代化布局工具类
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

// 📏 现代化间距系统
.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mt-0 { margin-top: var(--spacing-0); }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-0 { margin-bottom: var(--spacing-0); }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.ml-0 { margin-left: var(--spacing-0); }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }

.mr-0 { margin-right: var(--spacing-0); }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }

.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.pt-0 { padding-top: var(--spacing-0); }
.pt-1 { padding-top: var(--spacing-1); }
.pt-2 { padding-top: var(--spacing-2); }
.pt-3 { padding-top: var(--spacing-3); }
.pt-4 { padding-top: var(--spacing-4); }
.pt-6 { padding-top: var(--spacing-6); }
.pt-8 { padding-top: var(--spacing-8); }

.pb-0 { padding-bottom: var(--spacing-0); }
.pb-1 { padding-bottom: var(--spacing-1); }
.pb-2 { padding-bottom: var(--spacing-2); }
.pb-3 { padding-bottom: var(--spacing-3); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pb-6 { padding-bottom: var(--spacing-6); }
.pb-8 { padding-bottom: var(--spacing-8); }

.pl-0 { padding-left: var(--spacing-0); }
.pl-1 { padding-left: var(--spacing-1); }
.pl-2 { padding-left: var(--spacing-2); }
.pl-3 { padding-left: var(--spacing-3); }
.pl-4 { padding-left: var(--spacing-4); }

.pr-0 { padding-right: var(--spacing-0); }
.pr-1 { padding-right: var(--spacing-1); }
.pr-2 { padding-right: var(--spacing-2); }
.pr-3 { padding-right: var(--spacing-3); }
.pr-4 { padding-right: var(--spacing-4); }
// 🔤 现代化文字系统
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

// 📐 现代化圆角系统
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

// 🌊 现代化阴影系统
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }
.shadow-glow { box-shadow: var(--shadow-glow); }

// 🎨 现代化背景系统
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.bg-gradient {
  background: var(--primary-gradient);
}

.bg-gradient-hover {
  background: var(--primary-gradient);
  transition: all var(--transition-normal);

  &:hover {
    background: var(--primary-gradient-hover);
  }
}

// 🔲 边框系统
.border { border: 1px solid var(--border-light); }
.border-0 { border: none; }
.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }

// 📱 现代化响应式系统
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }

// 🔧 响应式断点工具类
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:text-xs { font-size: var(--font-size-xs); }
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:text-base { font-size: var(--font-size-base); }
  .sm\:text-lg { font-size: var(--font-size-lg); }
  .sm\:text-xl { font-size: var(--font-size-xl); }
  .sm\:text-2xl { font-size: var(--font-size-2xl); }
  .sm\:p-2 { padding: var(--spacing-2); }
  .sm\:p-4 { padding: var(--spacing-4); }
  .sm\:p-6 { padding: var(--spacing-6); }
  .sm\:px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
  .sm\:px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
  .sm\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  .sm\:py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
  .sm\:py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
  .sm\:py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
  .sm\:m-2 { margin: var(--spacing-2); }
  .sm\:m-4 { margin: var(--spacing-4); }
  .sm\:mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
  .sm\:mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
  .sm\:my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
  .sm\:my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
  .sm\:gap-2 { gap: var(--spacing-2); }
  .sm\:gap-4 { gap: var(--spacing-4); }
  .sm\:gap-6 { gap: var(--spacing-6); }
  .sm\:rounded-lg { border-radius: var(--radius-lg); }
  .sm\:rounded-xl { border-radius: var(--radius-xl); }
  .sm\:shadow-md { box-shadow: var(--shadow-md); }
  .sm\:shadow-lg { box-shadow: var(--shadow-lg); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:flex-col { flex-direction: column; }
  .md\:flex-row { flex-direction: row; }
  .md\:items-center { align-items: center; }
  .md\:justify-center { justify-content: center; }
  .md\:text-center { text-align: center; }
  .md\:text-left { text-align: left; }
  .md\:text-xs { font-size: var(--font-size-xs); }
  .md\:text-sm { font-size: var(--font-size-sm); }
  .md\:text-base { font-size: var(--font-size-base); }
  .md\:text-lg { font-size: var(--font-size-lg); }
  .md\:text-xl { font-size: var(--font-size-xl); }
  .md\:text-2xl { font-size: var(--font-size-2xl); }
  .md\:text-3xl { font-size: var(--font-size-3xl); }
  .md\:p-2 { padding: var(--spacing-2); }
  .md\:p-4 { padding: var(--spacing-4); }
  .md\:p-6 { padding: var(--spacing-6); }
  .md\:px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
  .md\:px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
  .md\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  .md\:py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
  .md\:py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
  .md\:py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
  .md\:py-8 { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }
  .md\:m-2 { margin: var(--spacing-2); }
  .md\:m-4 { margin: var(--spacing-4); }
  .md\:mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
  .md\:mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
  .md\:my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
  .md\:my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
  .md\:my-6 { margin-top: var(--spacing-6); margin-bottom: var(--spacing-6); }
  .md\:gap-2 { gap: var(--spacing-2); }
  .md\:gap-4 { gap: var(--spacing-4); }
  .md\:gap-6 { gap: var(--spacing-6); }
  .md\:rounded-lg { border-radius: var(--radius-lg); }
  .md\:rounded-xl { border-radius: var(--radius-xl); }
  .md\:rounded-2xl { border-radius: var(--radius-2xl); }
  .md\:shadow-md { box-shadow: var(--shadow-md); }
  .md\:shadow-lg { box-shadow: var(--shadow-lg); }
  .md\:shadow-xl { box-shadow: var(--shadow-xl); }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:flex-col { flex-direction: column; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:text-sm { font-size: var(--font-size-sm); }
  .lg\:text-base { font-size: var(--font-size-base); }
  .lg\:text-lg { font-size: var(--font-size-lg); }
  .lg\:text-xl { font-size: var(--font-size-xl); }
  .lg\:text-2xl { font-size: var(--font-size-2xl); }
  .lg\:text-3xl { font-size: var(--font-size-3xl); }
  .lg\:text-4xl { font-size: var(--font-size-4xl); }
  .lg\:p-4 { padding: var(--spacing-4); }
  .lg\:p-6 { padding: var(--spacing-6); }
  .lg\:p-8 { padding: var(--spacing-8); }
  .lg\:px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
  .lg\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  .lg\:px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }
  .lg\:py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
  .lg\:py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
  .lg\:py-8 { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }
  .lg\:gap-4 { gap: var(--spacing-4); }
  .lg\:gap-6 { gap: var(--spacing-6); }
  .lg\:gap-8 { gap: var(--spacing-8); }
}

// 🎯 现代化网格系统
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }

@media (max-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (max-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (max-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

// 📐 现代化宽度系统
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-screen { height: 100vh; }
.h-1\/2 { height: 50%; }

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-screen { max-width: 100vw; }

// 🎨 现代化定位系统
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

// 🌊 现代化溢出处理
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

// 🎭 文本溢出处理
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

// 🎬 现代化动画类
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// 🎯 现代化表单样式
.form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-sans);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &::placeholder {
    color: var(--text-tertiary);
  }
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-group {
  margin-bottom: var(--spacing-6);
}
// 🎭 现代化交互效果和微动画
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary-color);
  }
  50% {
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-4px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(4px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 🎨 加载状态和骨架屏
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;

  &::before,
  &::after {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: pulse 1.4s ease-in-out infinite both;
  }

  &::before {
    animation-delay: -0.16s;
  }

  &::after {
    animation-delay: 0.16s;
  }
}

// 🎯 现代化悬停效果
.hover-lift {
  transition: all var(--transition-normal);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }
}

.hover-scale {
  transition: transform var(--transition-fast);

  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: all var(--transition-normal);

  &:hover {
    animation: glow 1s ease-in-out infinite alternate;
  }
}

.hover-float {
  transition: transform var(--transition-normal);

  &:hover {
    animation: float 2s ease-in-out infinite;
  }
}

.hover-rotate {
  transition: transform var(--transition-fast);

  &:hover {
    transform: rotate(5deg);
  }
}

// 🎬 点击效果
.click-bounce {
  &:active {
    animation: bounce 0.6s ease;
  }
}

.click-scale {
  &:active {
    transform: scale(0.95);
  }
}

.click-ripple {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }
}

// 🌟 现代化按钮增强效果
.btn {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

.btn-primary {
  &:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
  }
}

.btn-ghost {
  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}

// 🎭 微交互效果
.micro-interaction {
  &:hover {
    .icon {
      animation: bounce 0.6s ease;
    }

    .text {
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

// 🎨 现代化表单交互
.form-input {
  &:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  }

  &:invalid {
    border-color: var(--error-color);
    animation: shake 0.5s ease-in-out;
  }
}

// 🎯 现代化卡片交互
.card {
  &:hover {
    .card-image {
      transform: scale(1.05);
    }

    .card-title {
      color: var(--primary-color);
    }
  }
}

.card-image {
  transition: transform var(--transition-normal);
  overflow: hidden;
  border-radius: var(--radius-lg);
}

// 🌊 现代化导航交互
.nav-link {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width var(--transition-normal);
  }

  &:hover::after,
  &.active::after {
    width: 100%;
  }
}

// 📱 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-glow:hover,
  .hover-float:hover,
  .hover-rotate:hover {
    transform: none;
    animation: none;
    box-shadow: none;
  }

  .btn:hover::before {
    left: -100%;
  }

  .nav-link::after {
    display: none;
  }
}

// 🎬 页面进入动画
.page-enter {
  animation: pageEnter 0.5s ease-out;
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 🎯 滚动触发动画
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;

  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

// 🌟 特殊效果
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-glow {
  box-shadow:
    0 0 5px var(--primary-color),
    0 0 10px var(--primary-color),
    0 0 15px var(--primary-color);
}

// 🎨 现代化进度条
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;

  &::before {
    content: '';
    display: block;
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    animation: shimmer 2s infinite;
  }
}

// 🎭 现代化工具提示
.tooltip {
  position: relative;

  &::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--bg-dark);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-md);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all var(--transition-fast);
    z-index: var(--z-tooltip);
  }

  &:hover::before {
    opacity: 1;
    transform: translateX(-50%) translateY(-4px);
  }
}