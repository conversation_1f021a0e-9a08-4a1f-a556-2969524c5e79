package com.blog.dto;

import lombok.Data;

/**
 * 文章查询DTO - 用于文章列表查询
 */
@Data
public class ArticleQueryDTO {
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 文章标题关键字
     */
    private String title;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 文章状态(0草稿,1已发布,2已删除)
     */
    private Integer status;
    
    /**
     * 是否只查询置顶文章(true是,false否)
     */
    private Boolean isTop;
} 