package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.common.exception.BusinessException;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.ArticleDTO;
import com.blog.entity.Article;
import com.blog.entity.ArticleTag;
import com.blog.entity.Category;
import com.blog.entity.User;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.ArticleTagMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.ArticleService;
import com.blog.service.CategoryService;
import com.blog.service.TagService;
import com.blog.vo.ArticleVO;
import com.blog.vo.TagVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文章服务实现类
 */
@Service
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ArticleTagMapper articleTagMapper;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private TagService tagService;

    /**
     * 创建文章
     * @param articleDTO 文章DTO
     * @return 文章ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createArticle(ArticleDTO articleDTO) {
        // 获取当前登录用户
        UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String username = userDetails.getUsername();
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查分类是否存在
        if (articleDTO.getCategoryId() != null) {
            if (!categoryService.exists(articleDTO.getCategoryId())) {
                throw new BusinessException("分类不存在");
            }
        }

        // 构建文章实体
        Article article = new Article();
        BeanUtils.copyProperties(articleDTO, article);
        article.setAuthorId(user.getId());
        article.setViewCount(0);
        article.setLikeCount(0);
        article.setCommentCount(0);
        
        // 保存文章
        save(article);
        
        // 处理标签关联
        handleArticleTags(article.getId(), articleDTO.getTagIds());

        return article.getId();
    }

    /**
     * 更新文章
     * @param articleDTO 文章DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateArticle(ArticleDTO articleDTO) {
        // 检查文章是否存在
        Article article = getById(articleDTO.getId());
        if (article == null) {
            throw new BusinessException("文章不存在");
        }

        // 获取当前登录用户
        UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String username = userDetails.getUsername();
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查权限
        boolean isAdmin = user.getRole().equals("admin");
        if (!isAdmin && !Objects.equals(article.getAuthorId(), user.getId())) {
            throw new BusinessException("无权修改他人文章");
        }

        // 检查分类是否存在
        if (articleDTO.getCategoryId() != null) {
            if (!categoryService.exists(articleDTO.getCategoryId())) {
                throw new BusinessException("分类不存在");
            }
        }

        // 更新文章基本信息
        BeanUtils.copyProperties(articleDTO, article);
        boolean result = updateById(article);

        // 处理标签关联
        handleArticleTags(article.getId(), articleDTO.getTagIds());

        return result;
    }

    /**
     * 删除文章
     * @param id 文章ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteArticle(Long id) {
        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new NotFoundException("文章不存在");
        }

        // 获取当前登录用户
        UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String username = userDetails.getUsername();
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查权限
        boolean isAdmin = user.getRole().equals("admin");
        if (!isAdmin && !Objects.equals(article.getAuthorId(), user.getId())) {
            throw new BusinessException("无权删除他人文章");
        }

        // 删除标签关联
        articleTagMapper.deleteByArticleId(id);

        // 逻辑删除文章
        return removeById(id);
    }

    /**
     * 获取文章详情
     * @param id 文章ID
     * @return 文章VO
     */
    @Override
    public ArticleVO getArticleDetail(Long id) {
        // 获取文章
        Article article = getById(id);
        if (article == null) {
            throw new NotFoundException("文章不存在");
        }

        // 增加浏览量
        article.setViewCount(article.getViewCount() + 1);
        updateById(article);

        // 转换为VO
        ArticleVO articleVO = convertArticleToVO(article);

        return articleVO;
    }

    /**
     * 分页获取文章列表
     * @param current 当前页
     * @param size 每页大小
     * @param keyword 搜索关键字
     * @param categoryId 分类ID
     * @param tagId 标签ID
     * @param status 文章状态
     * @return 文章VO分页对象
     */
    @Override
    public IPage<ArticleVO> getArticleList(Integer current, Integer size, String keyword, Long categoryId, Long tagId, Integer status) {
        Page<Article> page = new Page<>(current, size);
        
        // 构建查询条件
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态条件
        if (status != null) {
            queryWrapper.eq(Article::getStatus, status);
        } else {
            // 默认查询已发布文章
            queryWrapper.eq(Article::getStatus, 1);
        }
        
        // 关键字搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper ->
                wrapper.like(Article::getTitle, keyword)
                    .or()
                    .like(Article::getSummary, keyword)
                    .or()
                    .like(Article::getContent, keyword)
            );
        }
        
        // 分类筛选
        if (categoryId != null) {
            queryWrapper.eq(Article::getCategoryId, categoryId);
        }
        
        // 标签筛选
        List<Long> articleIds = null;
        if (tagId != null) {
            // 获取包含该标签的文章ID列表
            articleIds = baseMapper.selectArticleIdsByTagId(tagId);
            if (articleIds.isEmpty()) {
                // 没有找到符合条件的文章，返回空结果
                return new Page<>(current, size);
            }
            queryWrapper.in(Article::getId, articleIds);
        }

        // 排序
        queryWrapper.orderByDesc(Article::getIsTop)
                .orderByDesc(Article::getCreateTime);

        // 分页查询
        IPage<Article> articlePage = page(page, queryWrapper);

        // 转换为VO
        return convertArticlePageToVO(articlePage);
    }

    /**
     * 根据用户名获取文章列表
     * @param current 当前页
     * @param size 每页大小
     * @param username 用户名
     * @param status 文章状态
     * @return 文章VO分页对象
     */
    @Override
    public IPage<ArticleVO> getArticleListByUsername(Integer current, Integer size, String username, Integer status) {
        // 获取用户信息
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        Page<Article> page = new Page<>(current, size);

        // 构建查询条件
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getAuthorId, user.getId());
        
        // 状态条件
        if (status != null) {
            queryWrapper.eq(Article::getStatus, status);
        }
        
        // 排序
        queryWrapper.orderByDesc(Article::getIsTop)
                .orderByDesc(Article::getCreateTime);

        // 分页查询
        IPage<Article> articlePage = page(page, queryWrapper);

        // 转换为VO
        return convertArticlePageToVO(articlePage);
    }

    /**
     * 获取推荐文章列表
     * @param limit 限制数量
     * @return 文章VO列表
     */
    @Override
    public List<ArticleVO> getRecommendArticleList(Integer limit) {
        // 构建查询条件
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)  // 已发布的文章
                .eq(Article::getIsTop, 1)       // 置顶的文章
                .orderByDesc(Article::getCreateTime)
                .last("LIMIT " + limit);        // 限制数量

        List<Article> articleList = list(queryWrapper);

        // 转换为VO
        return articleList.stream()
                .map(this::convertArticleToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取热门文章列表
     * @param limit 限制数量
     * @return 文章VO列表
     */
    @Override
    public List<ArticleVO> getPopularArticleList(Integer limit) {
        // 构建查询条件
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)  // 已发布的文章
                .orderByDesc(Article::getViewCount)
                .orderByDesc(Article::getLikeCount)
                .orderByDesc(Article::getCreateTime)
                .last("LIMIT " + limit);        // 限制数量

        List<Article> articleList = list(queryWrapper);

        // 转换为VO
        return articleList.stream()
                .map(this::convertArticleToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取文章归档数据（按年月分组）
     * @return 归档数据Map，格式：{年份: {月份: [文章列表]}}
     */
    @Override
    public Map<String, Map<String, List<ArticleVO>>> getArchiveData() {
        // 查询所有已发布的文章，按创建时间倒序
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)  // 只查询已发布的文章
                .orderByDesc(Article::getCreateTime);

        List<Article> articleList = list(queryWrapper);

        // 按年月分组
        Map<String, Map<String, List<ArticleVO>>> archiveMap = new LinkedHashMap<>();

        for (Article article : articleList) {
            // 转换为VO
            ArticleVO articleVO = convertArticleToVO(article);

            // 获取年份和月份
            LocalDateTime createTime = article.getCreateTime();
            String year = String.valueOf(createTime.getYear());
            String month = String.valueOf(createTime.getMonthValue());

            // 初始化年份Map
            archiveMap.computeIfAbsent(year, k -> new LinkedHashMap<>());

            // 初始化月份List
            archiveMap.get(year).computeIfAbsent(month, k -> new ArrayList<>());

            // 添加文章到对应的年月
            archiveMap.get(year).get(month).add(articleVO);
        }

        return archiveMap;
    }

    /**
     * 处理文章标签关联
     * @param articleId 文章ID
     * @param tagIds 标签ID列表
     */
    private void handleArticleTags(Long articleId, List<Long> tagIds) {
        // 先删除原有关联
        articleTagMapper.deleteByArticleId(articleId);
        
        // 添加新的关联
        if (tagIds != null && !tagIds.isEmpty()) {
            // 验证所有标签都存在
            for (Long tagId : tagIds) {
                if (!tagService.exists(tagId)) {
                    throw new BusinessException("标签不存在：" + tagId);
                }
            }
            
            // 批量插入关联
            articleTagMapper.insertBatch(articleId, tagIds);
        }
    }

    /**
     * 将文章实体转换为VO
     * @param article 文章实体
     * @return 文章VO
     */
    private ArticleVO convertArticleToVO(Article article) {
        ArticleVO articleVO = new ArticleVO();
        BeanUtils.copyProperties(article, articleVO);

        // 设置作者信息
        User author = userMapper.selectById(article.getAuthorId());
        if (author != null) {
            articleVO.setAuthor(author.getNickname());
            articleVO.setAuthorAvatar(author.getAvatar());
        }

        // 设置分类信息
        if (article.getCategoryId() != null) {
            Category category = categoryService.getById(article.getCategoryId());
            if (category != null) {
                articleVO.setCategoryName(category.getName());
            }
        }

        // 设置标签信息
        List<TagVO> tagVOList = tagService.getTagsByArticleId(article.getId());
        articleVO.setTags(tagVOList);

        return articleVO;
    }

    /**
     * 将文章分页对象转换为VO分页对象
     * @param articlePage 文章分页对象
     * @return 文章VO分页对象
     */
    private IPage<ArticleVO> convertArticlePageToVO(IPage<Article> articlePage) {
        Page<ArticleVO> voPage = new Page<>(
                articlePage.getCurrent(),
                articlePage.getSize(),
                articlePage.getTotal());

        List<ArticleVO> voList = articlePage.getRecords().stream()
                .map(this::convertArticleToVO)
                .collect(Collectors.toList());

        voPage.setRecords(voList);
        return voPage;
    }
} 