<template>
  <div class="article-manage">
    <div class="header">
      <h1>文章管理</h1>
      <el-button type="primary" @click="addArticle">添加文章</el-button>
    </div>
    
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="标题">
          <el-input v-model="searchForm.title" placeholder="请输入文章标题"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="已发布" value="1"></el-option>
            <el-option label="草稿" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchArticles">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table :data="articles" v-loading="loading" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="标题" min-width="200"></el-table-column>
      <el-table-column prop="categoryName" label="分类" width="120"></el-table-column>
      <el-table-column prop="viewCount" label="浏览量" width="100"></el-table-column>
      <el-table-column prop="commentCount" label="评论数" width="100"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="editArticle(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="confirmDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getArticles, deleteArticle } from '@/api/article'
import ArticlePreview from '@/components/ArticlePreview.vue'

export default {
  name: 'ArticleManage',
  components: {
    ArticlePreview
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    
    // 搜索表单
    const searchForm = reactive({
      title: '',
      status: ''  // 使用空字符串，与字符串值匹配
    })
    
    // 分页信息
    const pagination = reactive({
      current: 1,
      size: 10,
      total: 0
    })
    
    // 文章列表
    const articles = ref([])
    
    // 获取文章列表
    const fetchArticles = async () => {
      loading.value = true
      try {
        // 确保分页参数是有效数字
        const currentPage = isNaN(pagination.current) ? 1 : pagination.current
        const pageSize = isNaN(pagination.size) ? 10 : pagination.size

        const params = {
          current: currentPage,
          size: pageSize,
          keyword: searchForm.title || ''    // 确保keyword不为undefined
        }

        // 只在status有值时才添加到参数中
        if (searchForm.status !== '' && searchForm.status !== null && searchForm.status !== undefined) {
          const statusValue = parseInt(searchForm.status)
          if (!isNaN(statusValue)) {
            params.status = statusValue
          }
        }

        const res = await getArticles(params)
        if (res.code === 200) {
          articles.value = res.data.records
          pagination.total = res.data.total
        } else {
          ElMessage.error(res.message || '获取文章列表失败')
        }
      } catch (error) {
        console.error('获取文章列表失败', error)
        ElMessage.error('获取文章列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索文章
    const searchArticles = () => {
      pagination.current = 1
      fetchArticles()
    }
    
    // 重置搜索
    const resetSearch = () => {
      searchForm.title = ''
      searchForm.status = ''  // 重置为空字符串
      searchArticles()
    }
    
    // 处理每页显示数量变化
    const handleSizeChange = (size) => {
      // 确保size是有效数字
      pagination.size = isNaN(size) ? 10 : size
      fetchArticles()
    }

    // 处理页码变化
    const handleCurrentChange = (page) => {
      // 确保page是有效数字
      pagination.current = isNaN(page) ? 1 : page
      fetchArticles()
    }
    
    // 添加文章
    const addArticle = () => {
      router.push('/admin/article/add')
    }
    
    // 编辑文章
    const editArticle = (row) => {
      router.push(`/admin/article/edit/${row.id}`)
    }
    
    // 确认删除文章
    const confirmDelete = (row) => {
      ElMessageBox.confirm(`确定要删除文章"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleDelete(row.id)
      }).catch(() => {})
    }
    
    // 删除文章
    const handleDelete = async (id) => {
      try {
        const res = await deleteArticle(id)
        if (res.code === 200) {
          ElMessage.success('删除成功')
          fetchArticles()
        } else {
          ElMessage.error(res.message || '删除失败')
        }
      } catch (error) {
        console.error('删除文章失败', error)
        ElMessage.error('删除失败')
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }
    
    // 获取状态类型
    const getStatusType = (status) => {
      switch (status) {
        case 1:
          return 'success'
        case 0:
          return 'info'
        case 2:
          return 'danger'
        default:
          return 'info'
      }
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 1:
          return '已发布'
        case 0:
          return '草稿'
        case 2:
          return '已删除'
        default:
          return '未知'
      }
    }
    
    onMounted(() => {
      fetchArticles()
    })
    
    return {
      loading,
      searchForm,
      pagination,
      articles,
      searchArticles,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      addArticle,
      editArticle,
      confirmDelete,
      formatDate,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.article-manage {
  padding: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 