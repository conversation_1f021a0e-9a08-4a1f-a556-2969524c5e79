package com.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.dto.PasswordUpdateDTO;
import com.blog.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 注册用户
     * @param user 用户信息
     * @return 注册结果
     */
    boolean register(User user);

    /**
     * 登录
     * @param username 用户名
     * @param password 密码
     * @return 生成的JWT token
     */
    String login(String username, String password);
    
    /**
     * 根据用户名获取用户
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);
    
    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param passwordUpdateDTO 密码更新DTO
     * @return 更新结果
     */
    boolean updatePassword(Long userId, PasswordUpdateDTO passwordUpdateDTO);
} 