/**
 * 测试套件：标签管理功能
 * 描述：覆盖管理员对标签进行增删改查的核心操作
 */
describe('标签管理功能', () => {
  let authToken;
  
  // 在所有测试开始前获取认证Token
  before(() => {
    // 直接使用API登录获取token
    cy.request({
      method: 'POST',
      url: '/api/auth/login',
      body: {
        username: 'admin',
        password: 'admin123'
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      cy.log('Login Response:', JSON.stringify(response.body));
      
      // 从登录响应中正确提取Token信息
      const { token, tokenHead } = response.body.data;
      expect(token).to.exist;

      // 组装成标准的Authorization请求头，并存储以备后用
      authToken = `${tokenHead} ${token}`;
      
      // 将token存储到localStorage中
      window.localStorage.setItem('blog_token', token);
      
      // 存储用户信息
      const userInfo = {
        userId: response.body.data.userId,
        username: response.body.data.username,
        nickname: response.body.data.nickname,
        role: response.body.data.role
      };
      window.localStorage.setItem('blog_user_info', JSON.stringify(userInfo));
    });
  });

  // 测试标签列表API是否正常工作
  it('应能通过API获取标签列表', () => {
    cy.request({
      method: 'GET',
      url: '/api/tags',
      headers: {
        'Authorization': authToken
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      expect(response.body.code).to.eq(200);
      expect(response.body.data).to.be.an('array');
    });
  });

  // 测试创建标签API是否正常工作
  it('应能通过API创建标签', () => {
    const tagName = `测试标签-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/tags',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: tagName
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      cy.log('Tag Response:', JSON.stringify(response.body));
      
      // 验证返回的数据
      expect(response.body.code).to.eq(200);
      
      // 检查API返回格式
      if (typeof response.body.data === 'number') {
        // 如果返回的是ID，验证ID是否为数字
        const tagId = response.body.data;
        expect(tagId).to.be.a('number');
        
        // 获取标签详情验证名称
        cy.request({
          method: 'GET',
          url: `/api/tags/${tagId}`,
          headers: {
            'Authorization': authToken
          }
        }).then(detailResponse => {
          expect(detailResponse.status).to.eq(200);
          expect(detailResponse.body.data.name).to.eq(tagName);
        });
      } else {
        // 如果返回的是完整对象，直接验证
        const tag = response.body.data;
        expect(tag).to.exist;
        expect(tag).to.have.property('id');
        expect(tag.id).to.be.a('number');
        expect(tag.name).to.eq(tagName);
      }
    });
  });

  // 测试更新标签API是否正常工作
  it('应能通过API更新标签', () => {
    // 先创建一个标签
    const originalName = `原标签-${Date.now()}`;
    const updatedName = `更新标签-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/tags',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: originalName
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      
      // 获取标签ID
      let tagId;
      if (typeof response.body.data === 'number') {
        tagId = response.body.data;
      } else {
        tagId = response.body.data.id;
      }
      
      expect(tagId).to.be.a('number');
      
      // 然后更新这个标签
      cy.request({
        method: 'PUT',
        url: `/api/tags/${tagId}`,
        headers: {
          'Authorization': authToken
        },
        body: {
          name: updatedName
        }
      }).then(updateResponse => {
        expect(updateResponse.status).to.eq(200);
        expect(updateResponse.body.code).to.eq(200);
        
        // 验证更新是否成功
        cy.request({
          method: 'GET',
          url: `/api/tags/${tagId}`,
          headers: {
            'Authorization': authToken
          }
        }).then(getResponse => {
          expect(getResponse.status).to.eq(200);
          expect(getResponse.body.data.name).to.eq(updatedName);
        });
      });
    });
  });

  // 测试删除标签API是否正常工作
  it('应能通过API删除标签', () => {
    // 先创建一个标签
    const tagName = `待删除标签-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/tags',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: tagName
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      
      // 获取标签ID
      let tagId;
      if (typeof response.body.data === 'number') {
        tagId = response.body.data;
      } else {
        tagId = response.body.data.id;
      }
      
      expect(tagId).to.be.a('number');
      
      // 然后删除这个标签
      cy.request({
        method: 'DELETE',
        url: `/api/tags/${tagId}`,
        headers: {
          'Authorization': authToken
        }
      }).then(deleteResponse => {
        expect(deleteResponse.status).to.eq(200);
        expect(deleteResponse.body.code).to.eq(200);
        
        // 验证标签是否已被删除
        cy.request({
          method: 'GET',
          url: '/api/tags',
          headers: {
            'Authorization': authToken
          }
        }).then(listResponse => {
          const tags = listResponse.body.data;
          const deletedTag = tags.find(t => t.id === tagId);
          expect(deletedTag).to.be.undefined;
        });
      });
    });
  });
});