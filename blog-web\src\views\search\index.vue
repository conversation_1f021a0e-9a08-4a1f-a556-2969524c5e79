<template>
  <div class="search-page">
    <h1>搜索</h1>
    
    <div class="search-form">
      <el-input
        v-model="keyword"
        placeholder="请输入关键词"
        clearable
        @keyup.enter="handleSearch">
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>
    </div>
    
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="!keyword" class="empty">
      <el-empty description="请输入关键词搜索"></el-empty>
    </div>
    <div v-else-if="articles.length === 0" class="empty">
      <el-empty :description="`没有找到与 '${keyword}' 相关的文章`"></el-empty>
    </div>
    <div v-else class="search-result">
      <div class="result-info">
        找到 {{ pagination.total }} 篇与 <span class="keyword">"{{ keyword }}"</span> 相关的文章
      </div>
      
      <div class="article-list">
        <div v-for="article in articles" :key="article.id" class="article-item">
          <h2 class="article-title">
            <router-link :to="`/article/${article.id}`">{{ article.title }}</router-link>
          </h2>
          <div class="article-info">
            <span>作者: {{ article.author }}</span>
            <span>发布时间: {{ article.createTime }}</span>
            <span>分类: {{ article.categoryName }}</span>
          </div>
          <div class="article-summary">{{ article.summary }}</div>
          <div class="article-footer">
            <router-link :to="`/article/${article.id}`">阅读全文</router-link>
          </div>
        </div>
      </div>
      
      <div v-if="pagination.total > 0" class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          layout="prev, pager, next"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'SearchPage',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const loading = ref(false)
    const keyword = ref('')
    
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    
    const articles = ref([])
    
    const search = async () => {
      if (!keyword.value) {
        articles.value = []
        pagination.total = 0
        return
      }

      loading.value = true
      try {
        // 调用搜索API
        const response = await fetch(`/api/articles?current=${pagination.currentPage}&size=${pagination.pageSize}&keyword=${encodeURIComponent(keyword.value)}`)
        const result = await response.json()

        if (result.code === 200) {
          articles.value = result.data.records || []
          pagination.total = result.data.total || 0
        } else {
          console.error('搜索失败', result.message)
          articles.value = []
          pagination.total = 0
        }
      } catch (error) {
        console.error('搜索失败', error)
        articles.value = []
        pagination.total = 0
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      pagination.currentPage = 1
      router.push({
        path: '/search',
        query: { q: keyword.value }
      })
    }
    
    const handleCurrentChange = (page) => {
      pagination.currentPage = page
      search()
    }
    
    // 监听路由参数变化
    watch(() => route.query.q, (newKeyword) => {
      if (newKeyword !== undefined) {
        keyword.value = newKeyword
        search()
      }
    }, { immediate: true })
    
    return {
      loading,
      keyword,
      pagination,
      articles,
      handleSearch,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.search-page {
  padding: 20px;
}

.search-form {
  max-width: 600px;
  margin: 20px auto;
}

.loading, .empty {
  margin-top: 30px;
  text-align: center;
}

.search-result {
  margin-top: 30px;
}

.result-info {
  margin-bottom: 20px;
  font-size: 16px;
}

.keyword {
  color: #409eff;
  font-weight: bold;
}

.article-list {
  margin-top: 20px;
}

.article-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.article-title {
  margin-bottom: 10px;
}

.article-title a {
  color: #333;
  text-decoration: none;
}

.article-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
}

.article-summary {
  margin-bottom: 15px;
  line-height: 1.6;
}

.article-footer a {
  color: #409eff;
  text-decoration: none;
}

.pagination {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
</style> 