import request from '@/utils/request'

// 获取文章列表
export function getArticles(params) {
  return request({
    url: '/articles',
    method: 'get',
    params
  })
}

// 获取文章详情
export function getArticleDetail(id) {
  return request({
    url: `/articles/${id}`,
    method: 'get'
  })
}

// 创建文章
export function createArticle(data) {
  return request({
    url: '/articles',
    method: 'post',
    data
  })
}

// 更新文章
export function updateArticle(id, data) {
  return request({
    url: `/articles/${id}`,
    method: 'put',
    data
  })
}

// 删除文章
export function deleteArticle(id) {
  return request({
    url: `/articles/${id}`,
    method: 'delete'
  })
}

// 获取推荐文章列表
export function getRecommendArticles(limit = 10) {
  return request({
    url: '/articles/recommend',
    method: 'get',
    params: { limit }
  })
}

// 获取当前用户的文章列表
export function getMyArticles(params) {
  return request({
    url: '/articles/my',
    method: 'get',
    params
  })
} 