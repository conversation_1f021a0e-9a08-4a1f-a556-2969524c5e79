import Cookies from 'js-cookie'

const TokenKey = 'blog_token'
const UserInfoKey = 'blog_user_info'

// Cookie过期时间设置（半小时）
const cookieOptions = {
  expires: 0.5/24,
  path: '/',
  // 如果是https环境，可以设置secure: true
  secure: window.location.protocol === 'https:'
}

// 获取Token
export function getToken() {
  return Cookies.get(TokenKey)
}

// 设置Token
export function setToken(token) {
  return Cookies.set(TokenKey, token, cookieOptions)
}

// 删除Token
export function removeToken() {
  return Cookies.remove(TokenKey, { path: '/' })
}

// 获取用户信息
export function getUserInfo() {
  const userInfo = Cookies.get(UserInfoKey)
  return userInfo ? JSON.parse(userInfo) : null
}

// 设置用户信息
export function setUserInfo(userInfo) {
  return Cookies.set(UserInfoKey, JSON.stringify(userInfo), cookieOptions)
}

// 删除用户信息
export function removeUserInfo() {
  return Cookies.remove(UserInfoKey, { path: '/' })
}

// 清除所有认证信息
export function clearAuth() {
  removeToken()
  removeUserInfo()
} 