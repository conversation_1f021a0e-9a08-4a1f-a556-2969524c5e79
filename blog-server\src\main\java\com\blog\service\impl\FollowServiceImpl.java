package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.blog.entity.User;
import com.blog.entity.UserFollow;
import com.blog.enums.FollowResultType;
import com.blog.mapper.UserFollowMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.FollowService;
import com.blog.service.NotificationService;
import com.blog.vo.UserVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关注服务实现类
 */
@Service
public class FollowServiceImpl implements FollowService {

    @Autowired
    private UserFollowMapper userFollowMapper;
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private NotificationService notificationService;

    @Override
    @Transactional
    public FollowResultType follow(Long followerId, Long followedId) {
        // 不能关注自己
        if (followerId.equals(followedId)) {
            return FollowResultType.CANNOT_FOLLOW_SELF;
        }
        
        // 检查两个用户是否存在
        User follower = userMapper.selectById(followerId);
        User followed = userMapper.selectById(followedId);
        if (follower == null || followed == null) {
            return FollowResultType.USER_NOT_FOUND;
        }
        
        // 检查是否已关注
        if (isFollowing(followerId, followedId)) {
            return FollowResultType.ALREADY_FOLLOWING;
        }
        
        // 创建关注记录
        UserFollow userFollow = new UserFollow();
        userFollow.setFollowerId(followerId);
        userFollow.setFollowedId(followedId);
        userFollow.setCreateTime(new Date());
        
        // 插入关注记录
        boolean success = userFollowMapper.insert(userFollow) > 0;

        if (success) {
            // 发送关注通知
            notificationService.sendFollowNotification(followedId, followerId);
        }

        return success ? FollowResultType.SUCCESS : FollowResultType.FAILED;
    }

    @Override
    @Transactional
    public FollowResultType unfollow(Long followerId, Long followedId) {
        // 检查两个用户是否存在
        User follower = userMapper.selectById(followerId);
        User followed = userMapper.selectById(followedId);
        if (follower == null || followed == null) {
            return FollowResultType.USER_NOT_FOUND;
        }

        // 检查是否已关注
        if (!isFollowing(followerId, followedId)) {
            return FollowResultType.NOT_FOLLOWING;
        }
        
        // 构建删除条件
        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowedId, followedId);
        
        // 删除关注记录
        boolean success = userFollowMapper.delete(queryWrapper) > 0;
        return success ? FollowResultType.SUCCESS : FollowResultType.FAILED;
    }

    @Override
    public boolean isFollowing(Long followerId, Long followedId) {
        // 查询用户关注状态
        int count = userFollowMapper.selectIsFollowing(followerId, followedId);
        return count > 0;
    }

    @Override
    public List<UserVO> getFollowingList(Long userId) {
        // 查询用户关注的用户ID列表
        List<Long> followingIds = userFollowMapper.selectFollowingIds(userId);
        if (followingIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询用户详情并转换为VO
        return followingIds.stream()
                .map(followingId -> {
                    User user = userMapper.selectById(followingId);
                    if (user == null) {
                        return null;
                    }
                    
                    UserVO userVO = new UserVO();
                    BeanUtils.copyProperties(user, userVO);
                    
                    // 设置关注状态和统计数据
                    userVO.setIsFollowed(true); // 当前用户已关注这些用户
                    userVO.setFollowingCount(getFollowingCount(followingId));
                    userVO.setFollowerCount(getFollowerCount(followingId));
                    
                    return userVO;
                })
                .filter(userVO -> userVO != null)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserVO> getFollowerList(Long userId) {
        // 查询用户的粉丝ID列表
        List<Long> followerIds = userFollowMapper.selectFollowerIds(userId);
        if (followerIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询用户详情并转换为VO
        return followerIds.stream()
                .map(followerId -> {
                    User user = userMapper.selectById(followerId);
                    if (user == null) {
                        return null;
                    }
                    
                    UserVO userVO = new UserVO();
                    BeanUtils.copyProperties(user, userVO);
                    
                    // 设置关注状态和统计数据
                    userVO.setIsFollowed(isFollowing(userId, followerId)); // 检查当前用户是否已关注此粉丝
                    userVO.setFollowingCount(getFollowingCount(followerId));
                    userVO.setFollowerCount(getFollowerCount(followerId));
                    
                    return userVO;
                })
                .filter(userVO -> userVO != null)
                .collect(Collectors.toList());
    }

    @Override
    public int getFollowingCount(Long userId) {
        // 查询用户关注数量
        return userFollowMapper.selectFollowingCount(userId);
    }

    @Override
    public int getFollowerCount(Long userId) {
        // 查询用户粉丝数量
        return userFollowMapper.selectFollowerCount(userId);
    }
} 