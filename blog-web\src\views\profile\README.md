# 个人资料页面组件

## 组件概述

`profile/index.vue` 是用户个人资料管理页面，提供了查看和编辑个人信息、修改密码和更新头像的功能。该组件使用 Element Plus 组件库构建用户界面，通过 API 调用与后端交互。

## 主要功能

1. **个人资料查看**：显示用户的基本信息，包括用户名、昵称、邮箱、角色和注册时间
2. **个人资料编辑**：允许用户修改昵称和邮箱
3. **密码修改**：提供修改密码的功能，需要输入当前密码和新密码
4. **头像上传**：支持上传和更新用户头像

## 组件结构

### 模板结构

```
profile-container
└── profile-card
    ├── profile-header
    │   ├── avatar-container (头像和上传按钮)
    │   └── user-info (用户信息和操作按钮)
    ├── divider
    ├── profile-form (编辑模式)
    └── profile-detail (查看模式)
└── password-dialog (密码修改对话框)
```

### 主要状态

- `userInfo`：用户信息对象
- `isEditMode`：是否处于编辑模式
- `editForm`：编辑表单数据
- `passwordForm`：密码修改表单数据
- `passwordDialogVisible`：密码对话框是否可见

## API 调用

组件使用以下 API 与后端交互：

- `getUserInfo()`：获取用户信息
- `updateUserInfo(data)`：更新用户资料
- `updatePassword(data)`：修改密码
- `updateAvatar(data)`：更新用户头像

## 使用方法

### 查看个人资料

1. 访问个人资料页面
2. 页面自动加载并显示当前登录用户的信息

### 编辑个人资料

1. 点击"编辑资料"按钮进入编辑模式
2. 修改昵称和邮箱信息
3. 点击"保存"按钮提交修改，或点击"取消"放弃修改

### 修改密码

1. 点击"修改密码"按钮打开密码修改对话框
2. 输入当前密码、新密码和确认新密码
3. 点击"确认修改"按钮提交修改
4. 密码修改成功后，系统将在 1.5 秒后自动退出登录

### 更新头像

1. 在编辑模式下，点击"更换头像"按钮或直接点击头像
2. 选择本地图片文件（支持 JPG/PNG/GIF 格式，大小不超过 2MB）
3. 系统自动上传并更新头像

## 表单验证规则

### 个人资料表单

- `nickname`：昵称长度在 2-20 个字符之间
- `email`：必须是有效的邮箱地址格式

### 密码表单

- `currentPassword`：当前密码不能为空
- `newPassword`：新密码不能为空，长度在 6-20 个字符之间
- `confirmPassword`：确认密码必须与新密码一致

## 注意事项

1. 头像上传前会进行文件类型和大小的校验
2. 密码修改成功后会自动退出登录，需要用户重新登录
3. 用户角色信息只能显示，不能修改
4. 用户名不可修改，只能显示 