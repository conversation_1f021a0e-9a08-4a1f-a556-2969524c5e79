package com.blog.service;

import com.blog.entity.Article;
import com.blog.entity.Comment;
import com.blog.entity.Notification;
import com.blog.entity.User;
import com.blog.enums.NotificationType;
import com.blog.enums.ResourceType;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.CommentMapper;
import com.blog.mapper.NotificationMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.impl.NotificationServiceImpl;
import com.blog.vo.NotificationVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 通知服务测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("通知服务测试")
class NotificationServiceTest {

    @Mock
    private NotificationMapper notificationMapper;

    @Mock
    private UserMapper userMapper;

    @Mock
    private ArticleMapper articleMapper;

    @Mock
    private CommentMapper commentMapper;

    @InjectMocks
    private NotificationServiceImpl notificationService;

    private User testUser;
    private User testFromUser;
    private Article testArticle;
    private Comment testComment;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setNickname("测试用户");

        testFromUser = new User();
        testFromUser.setId(2L);
        testFromUser.setUsername("fromuser");
        testFromUser.setNickname("发送者");

        testArticle = new Article();
        testArticle.setId(1L);
        testArticle.setTitle("测试文章");
        testArticle.setAuthorId(1L);

        testComment = new Comment();
        testComment.setId(1L);
        testComment.setContent("测试评论");
        testComment.setUserId(1L);
        testComment.setArticleId(1L);
    }

    @Test
    @DisplayName("发送点赞通知 - 成功")
    void testSendLikeNotification_Success() {
        // 准备测试数据
        Long articleId = 1L;
        Long fromUserId = 2L;

        // 模拟数据库查询
        when(articleMapper.selectById(articleId)).thenReturn(testArticle);
        when(userMapper.selectById(fromUserId)).thenReturn(testFromUser);
        when(notificationMapper.countSimilarNotifications(any(), any(), any(), any(), any())).thenReturn(0);

        // 执行测试
        notificationService.sendLikeNotification(articleId, fromUserId);

        // 验证结果
        verify(articleMapper).selectById(articleId);
        verify(userMapper).selectById(fromUserId);
        verify(notificationMapper).countSimilarNotifications(
                eq(testArticle.getAuthorId()),
                eq(fromUserId),
                eq(NotificationType.LIKE.getCode()),
                eq(articleId),
                eq(ResourceType.ARTICLE.getCode())
        );
    }

    @Test
    @DisplayName("发送点赞通知 - 自己点赞自己的文章，不发送通知")
    void testSendLikeNotification_SelfLike() {
        // 准备测试数据
        Long articleId = 1L;
        Long fromUserId = 1L; // 与文章作者相同

        // 模拟数据库查询
        when(articleMapper.selectById(articleId)).thenReturn(testArticle);

        // 执行测试
        notificationService.sendLikeNotification(articleId, fromUserId);

        // 验证结果 - 不应该发送通知
        verify(articleMapper).selectById(articleId);
        verify(userMapper, never()).selectById(any());
        verify(notificationMapper, never()).countSimilarNotifications(any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("发送评论通知 - 成功")
    void testSendCommentNotification_Success() {
        // 准备测试数据
        Long articleId = 1L;
        Long commentId = 1L;
        Long fromUserId = 2L;

        // 模拟数据库查询
        when(articleMapper.selectById(articleId)).thenReturn(testArticle);
        when(userMapper.selectById(fromUserId)).thenReturn(testFromUser);
        when(notificationMapper.countSimilarNotifications(any(), any(), any(), any(), any())).thenReturn(0);

        // 执行测试
        notificationService.sendCommentNotification(articleId, commentId, fromUserId);

        // 验证结果
        verify(articleMapper).selectById(articleId);
        verify(userMapper).selectById(fromUserId);
        verify(notificationMapper).countSimilarNotifications(
                eq(testArticle.getAuthorId()),
                eq(fromUserId),
                eq(NotificationType.COMMENT.getCode()),
                eq(commentId),
                eq(ResourceType.COMMENT.getCode())
        );
    }

    @Test
    @DisplayName("发送关注通知 - 成功")
    void testSendFollowNotification_Success() {
        // 准备测试数据
        Long followedUserId = 1L;
        Long fromUserId = 2L;

        // 模拟数据库查询
        when(userMapper.selectById(fromUserId)).thenReturn(testFromUser);
        when(notificationMapper.countSimilarNotifications(any(), any(), any(), any(), any())).thenReturn(0);

        // 执行测试
        notificationService.sendFollowNotification(followedUserId, fromUserId);

        // 验证结果
        verify(userMapper).selectById(fromUserId);
        verify(notificationMapper).countSimilarNotifications(
                eq(followedUserId),
                eq(fromUserId),
                eq(NotificationType.FOLLOW.getCode()),
                eq(fromUserId),
                eq(ResourceType.USER.getCode())
        );
    }

    @Test
    @DisplayName("发送关注通知 - 不能关注自己")
    void testSendFollowNotification_CannotFollowSelf() {
        // 准备测试数据
        Long userId = 1L;

        // 执行测试
        notificationService.sendFollowNotification(userId, userId);

        // 验证结果 - 不应该发送通知
        verify(userMapper, never()).selectById(any());
        verify(notificationMapper, never()).countSimilarNotifications(any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("获取用户通知列表")
    void testGetUserNotifications() {
        // 准备测试数据
        Long userId = 1L;
        Integer isRead = null;
        Integer limit = 20;

        NotificationVO notification1 = new NotificationVO();
        notification1.setId(1L);
        notification1.setTitle("测试通知1");

        NotificationVO notification2 = new NotificationVO();
        notification2.setId(2L);
        notification2.setTitle("测试通知2");

        List<NotificationVO> expectedNotifications = Arrays.asList(notification1, notification2);

        // 模拟数据库查询
        when(notificationMapper.selectUserNotifications(userId, isRead, limit))
                .thenReturn(expectedNotifications);

        // 执行测试
        List<NotificationVO> result = notificationService.getUserNotifications(userId, isRead, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试通知1", result.get(0).getTitle());
        assertEquals("测试通知2", result.get(1).getTitle());
        verify(notificationMapper).selectUserNotifications(userId, isRead, limit);
    }

    @Test
    @DisplayName("获取未读通知数量")
    void testGetUnreadCount() {
        // 准备测试数据
        Long userId = 1L;
        int expectedCount = 5;

        // 模拟数据库查询
        when(notificationMapper.countUnreadNotifications(userId)).thenReturn(expectedCount);

        // 执行测试
        int result = notificationService.getUnreadCount(userId);

        // 验证结果
        assertEquals(expectedCount, result);
        verify(notificationMapper).countUnreadNotifications(userId);
    }

    @Test
    @DisplayName("标记通知为已读")
    void testMarkAsRead() {
        // 准备测试数据
        Long userId = 1L;
        List<Long> notificationIds = Arrays.asList(1L, 2L, 3L);

        // 模拟数据库操作
        when(notificationMapper.markNotificationsAsRead(userId, notificationIds)).thenReturn(3);

        // 执行测试
        boolean result = notificationService.markAsRead(userId, notificationIds);

        // 验证结果
        assertTrue(result);
        verify(notificationMapper).markNotificationsAsRead(userId, notificationIds);
    }

    @Test
    @DisplayName("标记所有通知为已读")
    void testMarkAllAsRead() {
        // 准备测试数据
        Long userId = 1L;

        // 模拟数据库操作
        when(notificationMapper.markAllNotificationsAsRead(userId)).thenReturn(5);

        // 执行测试
        boolean result = notificationService.markAllAsRead(userId);

        // 验证结果
        assertTrue(result);
        verify(notificationMapper).markAllNotificationsAsRead(userId);
    }

    @Test
    @DisplayName("删除通知")
    void testDeleteNotifications() {
        // 准备测试数据
        Long userId = 1L;
        List<Long> notificationIds = Arrays.asList(1L, 2L);

        // 模拟数据库操作
        when(notificationMapper.deleteUserNotifications(userId, notificationIds)).thenReturn(2);

        // 执行测试
        boolean result = notificationService.deleteNotifications(userId, notificationIds);

        // 验证结果
        assertTrue(result);
        verify(notificationMapper).deleteUserNotifications(userId, notificationIds);
    }

    @Test
    @DisplayName("创建通知 - 防止重复通知")
    void testCreateNotification_PreventDuplicate() {
        // 准备测试数据
        Long userId = 1L;
        Long fromUserId = 2L;
        String type = NotificationType.LIKE.getCode();
        String title = "测试通知";
        String content = "测试内容";
        Long resourceId = 1L;
        String resourceType = ResourceType.ARTICLE.getCode();

        // 模拟已存在相同通知
        when(notificationMapper.countSimilarNotifications(userId, fromUserId, type, resourceId, resourceType))
                .thenReturn(1);

        // 执行测试
        boolean result = notificationService.createNotification(
                userId, fromUserId, type, title, content, resourceId, resourceType);

        // 验证结果 - 应该返回true但不创建新通知
        assertTrue(result);
        verify(notificationMapper).countSimilarNotifications(userId, fromUserId, type, resourceId, resourceType);
        verify(notificationMapper, never()).insert(any());
    }
}
