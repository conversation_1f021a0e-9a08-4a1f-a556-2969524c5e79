/**
 * 测试套件：文章、分类与标签关联功能
 * 描述：覆盖创建文章时，正确关联分类和标签的核心场景
 */
describe('文章、分类与标签的关联功能', () => {
  let category;
  let fullToken;

  // 测试分类创建API是否正常工作
  it('应能成功创建分类', () => {
    // 直接使用API登录获取token
    cy.request({
      method: 'POST',
      url: '/api/auth/login',
      body: {
        username: 'admin',
        password: 'admin123'
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      cy.log('Login Response:', JSON.stringify(response.body));
      
      // 从登录响应中正确提取Token信息
      const { token, tokenHead } = response.body.data;
      expect(token).to.exist;

      // 组装成标准的Authorization请求头，并存储以备后用
      fullToken = `${tokenHead} ${token}`;
      const headers = { 'Authorization': fullToken };

      // 创建分类
      const categoryName = `测试分类-${Date.now()}`;
      cy.request({
        method: 'POST',
        url: '/api/categories',
        headers,
        body: { name: categoryName, order: 1 }
      }).then(catResponse => {
        expect(catResponse.status).to.eq(200);
        cy.log('Category Response:', JSON.stringify(catResponse.body));
        
        // 验证返回的数据
        expect(catResponse.body.code).to.eq(200);
        expect(catResponse.body.data).to.exist;
        
        category = catResponse.body.data;
        
        // 验证分类对象
        expect(category).to.exist;
        expect(category).to.have.property('id');
        expect(category.id).to.be.a('number');
        expect(category.name).to.eq(categoryName);
      });
    });
  });
}); 