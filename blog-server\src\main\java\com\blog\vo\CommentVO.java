package com.blog.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论视图对象
 */
@Data
public class CommentVO {

    private Long id;
    private String content;
    private Long articleId;
    private Long parentId;
    private Long userId;
    private String username; // 评论用户名
    private String nickname; // 评论用户昵称（注册用户昵称或匿名昵称）
    private String avatar;   // 评论用户头像
    private Long toUserId;
    private String toNickname; // 回复用户昵称
    private Integer status;

    // 匿名评论相关字段
    private String anonymousNickname; // 匿名用户昵称
    private String anonymousEmail;    // 匿名用户邮箱
    private Integer isAnonymous;      // 是否匿名评论（0-注册用户，1-匿名用户）
    private LocalDateTime createTime;
    private List<CommentVO> children; // 子评论

    // 新增字段
    private String articleTitle;
    private boolean articleDeleted;
} 