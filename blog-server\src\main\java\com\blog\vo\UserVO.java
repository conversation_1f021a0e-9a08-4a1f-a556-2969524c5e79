package com.blog.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户视图对象
 */
@Data
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 关注数量
     */
    private Integer followingCount;

    /**
     * 粉丝数量
     */
    private Integer followerCount;
    
    /**
     * 是否已关注（当前登录用户是否已关注该用户）
     */
    private Boolean isFollowed;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 