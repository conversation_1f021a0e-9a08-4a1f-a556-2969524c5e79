import request from '@/utils/request';

/**
 * 获取所有标签列表
 * @returns {Promise}
 */
export function getTags() {
    return request({
        url: '/tags',
        method: 'get'
    });
}

/**
 * 新增一个标签
 * @param {object} data - 标签信息，例如 { name: '新的标签' }
 * @returns {Promise}
 */
export function addTag(data) {
    return request({
        url: '/tags',
        method: 'post',
        data
    });
}

/**
 * 更新一个标签
 * @param {number} id - 标签ID
 * @param {object} data - 更新的标签信息，例如 { id: 1, name: '更新后的标签' }
 * @returns {Promise}
 */
export function updateTag(id, data) {
    return request({
        url: `/tags/${id}`,
        method: 'put',
        data
    });
}

/**
 * 删除一个标签
 * @param {number} id - 标签ID
 * @returns {Promise}
 */
export function deleteTag(id) {
    return request({
        url: `/tags/${id}`,
        method: 'delete'
    });
} 