import request from '@/utils/request'

// 获取当前登录用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/user/info',
    method: 'put',
    data
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: '/user/password',
    method: 'put',
    data
  })
}

// 更新用户头像
export function updateAvatar(data) {
  return request({
    url: '/user/avatar',
    method: 'put',
    params: data
  })
}

// 获取用户收藏列表
export function getUserCollections() {
  return request({
    url: '/user/collections',
    method: 'get'
  })
}

// 获取用户评论列表
export function getUserComments() {
  return request({
    url: '/user/comments',
    method: 'get'
  })
} 