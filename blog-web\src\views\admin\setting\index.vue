<template>
  <div class="setting">
    <h1>系统设置</h1>
    
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本设置" name="basic">
        <el-form :model="basicForm" label-width="120px">
          <el-form-item label="网站标题">
            <el-input v-model="basicForm.siteTitle" placeholder="请输入网站标题"></el-input>
          </el-form-item>
          <el-form-item label="网站描述">
            <el-input 
              v-model="basicForm.siteDescription" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入网站描述">
            </el-input>
          </el-form-item>
          <el-form-item label="网站关键词">
            <el-input v-model="basicForm.siteKeywords" placeholder="请输入网站关键词，多个关键词用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="网站Logo">
            <el-upload
              class="avatar-uploader"
              action="/api/upload"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleLogoSuccess"
              :before-upload="beforeLogoUpload">
              <img v-if="basicForm.siteLogo" :src="basicForm.siteLogo" class="avatar">
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="ICP备案号">
            <el-input v-model="basicForm.icp" placeholder="请输入ICP备案号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="评论设置" name="comment">
        <el-form :model="commentForm" label-width="120px">
          <el-form-item label="评论审核">
            <el-switch v-model="commentForm.commentAudit"></el-switch>
          </el-form-item>
          <el-form-item label="允许匿名评论">
            <el-switch v-model="commentForm.allowAnonymousComment"></el-switch>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveCommentSettings">保存设置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="邮件设置" name="email">
        <el-form :model="emailForm" label-width="120px">
          <el-form-item label="SMTP服务器">
            <el-input v-model="emailForm.smtpServer" placeholder="请输入SMTP服务器地址"></el-input>
          </el-form-item>
          <el-form-item label="SMTP端口">
            <el-input v-model="emailForm.smtpPort" placeholder="请输入SMTP端口"></el-input>
          </el-form-item>
          <el-form-item label="发件人邮箱">
            <el-input v-model="emailForm.fromEmail" placeholder="请输入发件人邮箱"></el-input>
          </el-form-item>
          <el-form-item label="邮箱用户名">
            <el-input v-model="emailForm.username" placeholder="请输入邮箱用户名"></el-input>
          </el-form-item>
          <el-form-item label="邮箱密码">
            <el-input v-model="emailForm.password" type="password" placeholder="请输入邮箱密码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveEmailSettings">保存设置</el-button>
            <el-button @click="testEmailSettings">测试连接</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'
import { refreshSiteConfig } from '@/utils/siteConfig'

export default {
  name: 'Setting',
  setup() {
    const activeTab = ref('basic')

    const basicForm = reactive({
      siteTitle: '',
      siteDescription: '',
      siteKeywords: '',
      siteLogo: '',
      icp: ''
    })

    // 上传请求头
    const uploadHeaders = computed(() => {
      return {
        'Authorization': `Bearer ${getToken()}`
      }
    })
    
    const commentForm = reactive({
      commentAudit: true,
      allowAnonymousComment: false
    })
    
    const emailForm = reactive({
      smtpServer: '',
      smtpPort: '',
      fromEmail: '',
      username: '',
      password: ''
    })
    
    const fetchSettings = async () => {
      try {
        // 获取网站基本信息
        const siteResponse = await fetch('/api/config/site', {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })
        const siteResult = await siteResponse.json()

        // 后端返回格式：{code: 200, message: "success", data: {...}}
        if (siteResult.code === 200) {
          const siteData = siteResult.data || {}
          basicForm.siteTitle = siteData.site_title || '个人动态博客'
          basicForm.siteDescription = siteData.site_description || '一个简单的个人动态博客系统'
          basicForm.siteKeywords = siteData.site_keywords || '博客,个人博客,动态博客'

          // 处理Logo URL，确保是完整的访问路径
          let logoUrl = siteData.site_logo || ''
          if (logoUrl && !logoUrl.startsWith('http') && !logoUrl.startsWith('/api')) {
            logoUrl = '/api' + logoUrl
          }
          basicForm.siteLogo = logoUrl

          basicForm.icp = siteData.icp || ''
        }

        // 获取评论设置
        const commentResponse = await fetch('/api/config/comment', {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })
        const commentResult = await commentResponse.json()

        if (commentResult.code === 200) {
          const commentData = commentResult.data || {}
          commentForm.commentAudit = commentData.comment_audit === 'true'
          commentForm.allowAnonymousComment = commentData.allow_anonymous_comment === 'true'
        }

        // 获取邮件设置
        const emailResponse = await fetch('/api/config/email', {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })
        const emailResult = await emailResponse.json()

        if (emailResult.code === 200) {
          const emailData = emailResult.data || {}
          emailForm.smtpServer = emailData.smtp_server || ''
          emailForm.smtpPort = emailData.smtp_port || '587'
          emailForm.fromEmail = emailData.from_email || ''
          emailForm.username = emailData.email_username || ''
          emailForm.password = emailData.email_password || ''
        }

      } catch (error) {
        console.error('获取设置失败', error)
        ElMessage.error('获取设置失败')
      }
    }
    
    const saveBasicSettings = async () => {
      try {
        // 构建设置数据
        const settings = {
          site_title: basicForm.siteTitle,
          site_description: basicForm.siteDescription,
          site_keywords: basicForm.siteKeywords,
          site_logo: basicForm.siteLogo,
          icp: basicForm.icp
        }

        const response = await fetch('/api/config/site', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(settings)
        })

        const result = await response.json()

        // 后端返回格式：{code: 200, message: "success", data: true/false}
        if (result.code === 200) {
          ElMessage.success('基本设置保存成功')
          // 刷新全局网站配置，让其他页面立即生效
          await refreshSiteConfig()
        } else {
          ElMessage.error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存基本设置失败', error)
        ElMessage.error('保存失败')
      }
    }

    const saveCommentSettings = async () => {
      try {
        // 构建设置数据
        const settings = {
          comment_audit: commentForm.commentAudit.toString(),
          allow_anonymous_comment: commentForm.allowAnonymousComment.toString()
        }

        const response = await fetch('/api/config/comment', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(settings)
        })

        const result = await response.json()

        if (result.code === 200) {
          ElMessage.success('评论设置保存成功')
        } else {
          ElMessage.error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存评论设置失败', error)
        ElMessage.error('保存失败')
      }
    }

    const saveEmailSettings = async () => {
      try {
        // 构建设置数据
        const settings = {
          smtp_server: emailForm.smtpServer,
          smtp_port: emailForm.smtpPort,
          from_email: emailForm.fromEmail,
          email_username: emailForm.username,
          email_password: emailForm.password
        }

        const response = await fetch('/api/config/email', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(settings)
        })

        const result = await response.json()

        if (result.code === 200) {
          ElMessage.success('邮件设置保存成功')
        } else {
          ElMessage.error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存邮件设置失败', error)
        ElMessage.error('保存失败')
      }
    }

    const testEmailSettings = async () => {
      try {
        // 先保存当前配置，然后测试
        const settings = {
          smtp_server: emailForm.smtpServer,
          smtp_port: emailForm.smtpPort,
          from_email: emailForm.fromEmail,
          email_username: emailForm.username,
          email_password: emailForm.password
        }

        const response = await fetch('/api/config/email/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(settings)
        })

        const result = await response.json()

        if (result.code === 200) {
          ElMessage.success('邮件测试成功')
        } else {
          ElMessage.error(result.message || '邮件测试失败')
        }
      } catch (error) {
        console.error('测试邮件设置失败', error)
        ElMessage.error('邮件测试失败')
      }
    }
    
    // Logo上传成功处理
    const handleLogoSuccess = (response) => {
      if (response.code === 200) {
        // 确保图片URL是完整的访问路径
        let logoUrl = response.data
        if (logoUrl && !logoUrl.startsWith('http') && !logoUrl.startsWith('/api')) {
          logoUrl = '/api' + logoUrl
        }
        basicForm.siteLogo = logoUrl
        ElMessage.success('Logo上传成功')
      } else {
        ElMessage.error(response.message || 'Logo上传失败')
      }
    }

    // Logo上传前验证
    const beforeLogoUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    }

    onMounted(() => {
      fetchSettings()
    })

    return {
      activeTab,
      basicForm,
      commentForm,
      emailForm,
      uploadHeaders,
      handleLogoSuccess,
      beforeLogoUpload,
      saveBasicSettings,
      saveCommentSettings,
      saveEmailSettings,
      testEmailSettings
    }
  }
}
</script>

<style scoped>
.setting {
  padding: 20px;
}

.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style> 