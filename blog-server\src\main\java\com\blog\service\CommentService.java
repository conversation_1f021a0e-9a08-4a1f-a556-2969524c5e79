package com.blog.service;

import com.blog.dto.CommentDTO;
import com.blog.entity.Comment;
import com.blog.vo.CommentVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 评论服务接口
 */
public interface CommentService extends IService<Comment> {

    /**
     * 创建评论
     *
     * @param commentDTO 评论信息
     * @param userId 当前登录用户ID（匿名评论时为null）
     * @return 评论ID
     */
    Long createComment(CommentDTO commentDTO, Long userId);
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 当前登录用户ID
     * @return 是否删除成功
     */
    boolean deleteComment(Long commentId, Long userId);
    
    /**
     * 获取文章评论列表（树形结构）
     * 
     * @param articleId 文章ID
     * @return 评论树形列表
     */
    List<CommentVO> getArticleComments(Long articleId);
    
    /**
     * 获取用户的所有评论
     * 
     * @param userId 用户ID
     * @return 评论列表
     */
    List<CommentVO> getUserComments(Long userId);
    
    /**
     * 更新评论状态（审核/取消审核）
     * 
     * @param commentId 评论ID
     * @param status 状态值
     * @return 是否更新成功
     */
    boolean updateCommentStatus(Long commentId, Integer status);
    
    /**
     * 获取所有评论（管理员使用）
     *
     * @return 所有评论列表
     */
    List<CommentVO> getAllComments();

    /**
     * 将评论实体转换为VO
     *
     * @param comment 评论实体
     * @return 评论VO
     */
    CommentVO convertToVO(Comment comment);
} 