package com.blog.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 标签数据传输对象，用于接收前端传来的标签数据
 */
@Data
public class TagDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 标签ID，添加时不需要，更新时必填
     */
    private Long id;
    
    /**
     * 标签名称，必填
     */
    @NotBlank(message = "标签名称不能为空")
    private String name;
} 