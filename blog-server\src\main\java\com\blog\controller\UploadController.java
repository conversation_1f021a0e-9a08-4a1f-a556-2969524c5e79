package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.config.SystemConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/upload")
@Api(tags = "文件上传接口")
@Slf4j
public class UploadController {

    @Autowired
    private SystemConfig systemConfig;

    /**
     * 上传头像
     * @param file 文件
     * @return 上传结果
     */
    @ApiOperation("上传头像")
    @PostMapping("/avatar")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("请选择文件");
        }

        // 文件原始名称
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        
        // 检查是否为图片
        if (!isImageFile(suffix)) {
            return Result.failed("只能上传图片文件");
        }

        // 生成新的文件名
        String newFilename = UUID.randomUUID().toString().replace("-", "") + suffix;
        
        // 上传目录
        String uploadDir = systemConfig.getUploadDir() + "/avatar";
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        // 保存文件
        File destFile = new File(uploadDir + "/" + newFilename);
        try {
            file.transferTo(destFile);
            log.info("文件上传成功：{}", destFile.getAbsolutePath());
            
            // 返回访问URL，需要手动加上 /api 前缀以匹配 context-path
            String fileUrl = "/api" + systemConfig.getResourceUrl() + "/avatar/" + newFilename;
            return Result.success(fileUrl, "上传成功");
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.failed("文件上传失败");
        }
    }

    /**
     * 检查是否为图片文件
     * @param suffix 文件后缀
     * @return 是否为图片
     */
    private boolean isImageFile(String suffix) {
        suffix = suffix.toLowerCase();
        return suffix.equals(".jpg") || suffix.equals(".jpeg") || 
               suffix.equals(".png") || suffix.equals(".gif") || 
               suffix.equals(".bmp");
    }
} 