// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

/**
 * 自定义命令：以管理员身份登录
 * 用法：cy.loginAsAdmin()
 */
Cypress.Commands.add('loginAsAdmin', () => {
  // 使用API直接登录，而不是通过UI
  cy.request({
    method: 'POST',
    url: '/api/auth/login',
    body: {
      username: 'admin',
      password: 'admin123'
    }
  }).then(response => {
    expect(response.status).to.eq(200);
    
    const { token, tokenHead } = response.body.data;
    
    // 将token存储到localStorage中
    window.localStorage.setItem('blog_token', token);
    
    // 存储用户信息
    const userInfo = {
      userId: response.body.data.userId,
      username: response.body.data.username,
      nickname: response.body.data.nickname,
      role: response.body.data.role
    };
    window.localStorage.setItem('blog_user_info', JSON.stringify(userInfo));
    
    // 访问管理员仪表盘
    cy.visit('/admin/dashboard');
    cy.url().should('include', '/admin/dashboard');
  });
});

/**
 * 自定义命令：获取JWT令牌并保存到localStorage
 * 用法：cy.getAndStoreAdminToken()
 */
Cypress.Commands.add('getAndStoreAdminToken', () => {
  return cy.request({
    method: 'POST',
    url: '/api/auth/login',
    body: {
      username: 'admin',
      password: 'admin123'
    }
  }).then(response => {
    expect(response.status).to.eq(200);
    
    // 打印响应内容，用于调试
    cy.log('Login API Response:', JSON.stringify(response.body));
    
    // 检查响应中是否包含必要的数据
    expect(response.body.code).to.eq(200);
    expect(response.body.data).to.exist;
    
    const { token, tokenHead, userId, username, nickname, role } = response.body.data;
    
    // 确保token存在
    expect(token).to.exist;
    
    // 将完整的token存储到localStorage中，以便在测试中使用
    window.localStorage.setItem('blog_token', token);
    
    // 存储用户信息
    const userInfo = {
      userId,
      username,
      nickname,
      role
    };
    window.localStorage.setItem('blog_user_info', JSON.stringify(userInfo));
    
    // 同时也存储到cookie中，因为前端可能使用cookie存储
    cy.setCookie('blog_token', token);
    cy.setCookie('blog_user_info', JSON.stringify(userInfo));
  });
});