<template>
  <div class="user-manage">
    <div class="header">
      <h1>用户管理</h1>
    </div>
    
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="正常" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchUsers">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table :data="users" v-loading="loading" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="username" label="用户名"></el-table-column>
      <el-table-column prop="nickname" label="昵称"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
      <el-table-column prop="role" label="角色" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'primary'">
            {{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="注册时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            size="small"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            :disabled="scope.row.role === 'admin' && scope.row.status === 1"
            @click="toggleUserStatus(scope.row)">
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button
            size="small"
            type="danger"
            :disabled="scope.row.role === 'admin'"
            @click="deleteUser(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { formatDateTime } from '@/utils/format'

export default {
  name: 'UserManage',
  setup() {
    const loading = ref(false)
    
    const searchForm = reactive({
      username: '',
      status: ''  // 使用空字符串，与字符串值匹配
    })
    
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    
    const users = ref([])
    
    const fetchUsers = async () => {
      loading.value = true
      try {
        // 构建查询参数
        const params = new URLSearchParams({
          current: pagination.currentPage.toString(),
          size: pagination.pageSize.toString()
        })

        if (searchForm.username) {
          params.append('username', searchForm.username)
        }

        if (searchForm.status !== '') {
          params.append('status', searchForm.status)
        }

        const token = getToken()
        console.log('Token:', token)
        console.log('Request URL:', `/api/users?${params}`)

        // 调用用户列表API
        const response = await fetch(`/api/users?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        console.log('Response status:', response.status)
        const result = await response.json()
        console.log('Response result:', result)

        if (result.code === 200) {
          users.value = result.data.records || []
          pagination.total = result.data.total || 0
        } else {
          console.error('获取用户列表失败', result.message)
          ElMessage.error(result.message || '获取用户列表失败')
          users.value = []
          pagination.total = 0
        }
      } catch (error) {
        console.error('获取用户列表失败', error)
        ElMessage.error('获取用户列表失败')
        users.value = []
        pagination.total = 0
      } finally {
        loading.value = false
      }
    }
    
    const searchUsers = () => {
      pagination.currentPage = 1
      fetchUsers()
    }
    
    const resetSearch = () => {
      searchForm.username = ''
      searchForm.status = ''  // 重置为空字符串
      searchUsers()
    }
    
    const handleSizeChange = (size) => {
      pagination.pageSize = size
      fetchUsers()
    }
    
    const handleCurrentChange = (page) => {
      pagination.currentPage = page
      fetchUsers()
    }
    
    const toggleUserStatus = async (row) => {
      try {
        // 检查是否尝试禁用管理员
        const newStatus = row.status === 1 ? 0 : 1
        if (row.role === 'admin' && newStatus === 0) {
          ElMessage.warning('不允许禁用管理员账户')
          return
        }

        const response = await fetch(`/api/users/${row.id}/status?status=${newStatus}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })

        const result = await response.json()

        if (result.code === 200) {
          ElMessage.success(`用户${newStatus === 1 ? '启用' : '禁用'}成功`)
          fetchUsers()
        } else {
          ElMessage.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('切换用户状态失败', error)
        ElMessage.error('操作失败')
      }
    }

    const deleteUser = async (row) => {
      try {
        // 检查是否尝试删除管理员
        if (row.role === 'admin') {
          ElMessage.warning('不允许删除管理员账户')
          return
        }

        // 确认删除
        await ElMessageBox.confirm(`确定要删除用户"${row.username}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await fetch(`/api/users/${row.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })

        const result = await response.json()

        if (result.code === 200) {
          ElMessage.success('删除成功')
          fetchUsers()
        } else {
          ElMessage.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    onMounted(() => {
      fetchUsers()
    })
    
    return {
      loading,
      searchForm,
      pagination,
      users,
      searchUsers,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      toggleUserStatus,
      deleteUser,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.user-manage {
  padding: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 