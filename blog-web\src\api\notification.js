import request from '@/utils/request'

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.isRead - 是否已读（0-未读，1-已读，null-全部）
 * @param {number} params.limit - 限制数量，默认20
 * @returns {Promise}
 */
export function getNotifications(params = {}) {
  return request({
    url: '/notifications',
    method: 'get',
    params
  })
}

/**
 * 获取未读通知数量
 * @returns {Promise}
 */
export function getUnreadCount() {
  return request({
    url: '/notifications/unread-count',
    method: 'get'
  })
}

/**
 * 标记通知为已读
 * @param {Array} notificationIds - 通知ID列表
 * @returns {Promise}
 */
export function markAsRead(notificationIds) {
  return request({
    url: '/notifications/mark-read',
    method: 'put',
    data: notificationIds
  })
}

/**
 * 标记所有通知为已读
 * @returns {Promise}
 */
export function markAllAsRead() {
  return request({
    url: '/notifications/mark-all-read',
    method: 'put'
  })
}

/**
 * 删除通知
 * @param {Array} notificationIds - 通知ID列表
 * @returns {Promise}
 */
export function deleteNotifications(notificationIds) {
  return request({
    url: '/notifications',
    method: 'delete',
    data: notificationIds
  })
}
