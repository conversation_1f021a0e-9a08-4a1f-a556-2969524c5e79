package com.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.entity.SystemSetting;
import com.blog.mapper.SystemSettingMapper;
import com.blog.service.SystemSettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统设置服务实现类
 */
@Service
public class SystemSettingServiceImpl extends ServiceImpl<SystemSettingMapper, SystemSetting> implements SystemSettingService {

    @Override
    public String getValueByKey(String settingKey) {
        return baseMapper.getValueByKey(settingKey);
    }

    @Override
    public String getValueByKey(String settingKey, String defaultValue) {
        String value = getValueByKey(settingKey);
        return value != null ? value : defaultValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setSetting(String settingKey, String settingValue, String description, String settingGroup) {
        // 查询是否已存在
        LambdaQueryWrapper<SystemSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemSetting::getSettingKey, settingKey);
        SystemSetting existingSetting = getOne(queryWrapper);

        if (existingSetting != null) {
            // 更新现有设置
            existingSetting.setSettingValue(settingValue);
            if (description != null) {
                existingSetting.setDescription(description);
            }
            if (settingGroup != null) {
                existingSetting.setSettingGroup(settingGroup);
            }
            return updateById(existingSetting);
        } else {
            // 创建新设置
            SystemSetting newSetting = new SystemSetting();
            newSetting.setSettingKey(settingKey);
            newSetting.setSettingValue(settingValue);
            newSetting.setDescription(description);
            newSetting.setSettingGroup(settingGroup);
            return save(newSetting);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetSettings(Map<String, String> settings, String settingGroup) {
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            if (!setSetting(entry.getKey(), entry.getValue(), null, settingGroup)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<SystemSetting> getSettingsByGroup(String settingGroup) {
        LambdaQueryWrapper<SystemSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemSetting::getSettingGroup, settingGroup);
        return list(queryWrapper);
    }

    @Override
    public Map<String, String> getAllSettingsMap() {
        List<SystemSetting> allSettings = list();
        Map<String, String> settingsMap = new HashMap<>();
        for (SystemSetting setting : allSettings) {
            settingsMap.put(setting.getSettingKey(), setting.getSettingValue());
        }
        return settingsMap;
    }
}
