package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.common.utils.SecurityUtils;
import com.blog.entity.User;
import com.blog.enums.FollowResultType;
import com.blog.service.FollowService;
import com.blog.service.UserService;
import com.blog.vo.UserVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 关注控制器测试类
 */
@SpringBootTest
@AutoConfigureMockMvc
public class FollowControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @MockBean
    private FollowService followService;
    
    @MockBean
    private UserService userService;
    
    @MockBean
    private SecurityUtils securityUtils;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    private User testUser1;
    private User testUser2;
    private UserVO testUserVO1;
    private UserVO testUserVO2;
    
    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
        
        // 初始化测试用户
        testUser1 = new User();
        testUser1.setId(1L);
        testUser1.setUsername("user1");
        testUser1.setNickname("用户1");
        testUser1.setAvatar("avatar1.jpg");
        
        testUser2 = new User();
        testUser2.setId(2L);
        testUser2.setUsername("user2");
        testUser2.setNickname("用户2");
        testUser2.setAvatar("avatar2.jpg");
        
        // 初始化用户VO
        testUserVO1 = new UserVO();
        testUserVO1.setId(1L);
        testUserVO1.setUsername("user1");
        testUserVO1.setNickname("用户1");
        testUserVO1.setAvatar("avatar1.jpg");
        testUserVO1.setFollowingCount(5);
        testUserVO1.setFollowerCount(10);
        
        testUserVO2 = new UserVO();
        testUserVO2.setId(2L);
        testUserVO2.setUsername("user2");
        testUserVO2.setNickname("用户2");
        testUserVO2.setAvatar("avatar2.jpg");
        testUserVO2.setFollowingCount(3);
        testUserVO2.setFollowerCount(7);
    }
    
    @Test
    @DisplayName("未登录用户关注操作测试 - 应返回401")
    public void testFollowUnauthorized() throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("userId", 2L);
        
        mockMvc.perform(post("/follow")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("已登录用户关注操作测试 - 成功")
    public void testFollowSuccess() throws Exception {
        // 准备测试数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("userId", 2L);
        
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟关注操作成功
        when(followService.follow(1L, 2L)).thenReturn(FollowResultType.SUCCESS);
        
        // 执行测试
        mockMvc.perform(post("/follow")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
        
        // 验证服务方法被调用
        verify(followService).follow(1L, 2L);
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("已登录用户关注操作测试 - 失败")
    public void testFollowFail() throws Exception {
        // 准备测试数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("userId", 2L);
        
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟关注操作失败
        when(followService.follow(1L, 2L)).thenReturn(FollowResultType.FAILED);
        
        // 执行测试
        mockMvc.perform(post("/follow")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));
        
        // 验证服务方法被调用
        verify(followService).follow(1L, 2L);
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("已登录用户取消关注操作测试 - 成功")
    public void testUnfollowSuccess() throws Exception {
        // 准备测试数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("userId", 2L);
        
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟取消关注操作成功
        when(followService.unfollow(1L, 2L)).thenReturn(FollowResultType.SUCCESS);
        
        // 执行测试
        mockMvc.perform(post("/follow/unfollow")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
        
        // 验证服务方法被调用
        verify(followService).unfollow(1L, 2L);
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取用户关系状态测试")
    public void testGetRelationship() throws Exception {
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟关注状态
        when(followService.isFollowing(1L, 2L)).thenReturn(true);
        when(followService.getFollowingCount(2L)).thenReturn(5);
        when(followService.getFollowerCount(2L)).thenReturn(10);
        
        // 执行测试
        mockMvc.perform(get("/follow/status/2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.isFollowing").value(true))
                .andExpect(jsonPath("$.data.followingCount").value(5))
                .andExpect(jsonPath("$.data.followerCount").value(10));
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取用户关注列表测试 - 成功")
    public void testGetFollowingListSuccess() throws Exception {
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟关注列表
        when(followService.getFollowingList(1L)).thenReturn(Arrays.asList(testUserVO2));
        
        // 执行测试
        mockMvc.perform(get("/follow/following"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(2))
                .andExpect(jsonPath("$.data[0].nickname").value("用户2"));
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取用户关注列表测试 - 空列表")
    public void testGetEmptyFollowingList() throws Exception {
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟空关注列表
        when(followService.getFollowingList(1L)).thenReturn(Collections.emptyList());
        
        // 执行测试
        mockMvc.perform(get("/follow/following"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data").isEmpty());
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取用户粉丝列表测试 - 成功")
    public void testGetFollowerListSuccess() throws Exception {
        // 模拟当前登录用户
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟粉丝列表
        when(followService.getFollowerList(1L)).thenReturn(Arrays.asList(testUserVO2));
        
        // 执行测试
        mockMvc.perform(get("/follow/followers"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(2))
                .andExpect(jsonPath("$.data[0].nickname").value("用户2"));
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取指定用户的关注列表测试 - 公共接口")
    public void testGetUserFollowingList() throws Exception {
        // 模拟当前用户(可选，因为这是公共接口)
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟关注列表
        when(followService.getFollowingList(2L)).thenReturn(Arrays.asList(testUserVO1));
        
        // 执行测试
        mockMvc.perform(get("/follow/user/2/following"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].nickname").value("用户1"));
    }
    
    @Test
    @WithMockUser(username = "user1")
    @DisplayName("获取指定用户的粉丝列表测试 - 公共接口")
    public void testGetUserFollowerList() throws Exception {
        // 模拟当前用户(可选，因为这是公共接口)
        when(securityUtils.getCurrentUserId()).thenReturn(1L);
        
        // 模拟粉丝列表
        when(followService.getFollowerList(2L)).thenReturn(Arrays.asList(testUserVO1));
        
        // 执行测试
        mockMvc.perform(get("/follow/user/2/followers"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].nickname").value("用户1"));
    }
} 