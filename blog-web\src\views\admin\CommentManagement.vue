<template>
  <div class="comment-management" data-cy="comment-management">
    <h1 class="page-title">评论管理</h1>
    
    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-input
        v-model="searchText"
        placeholder="搜索评论内容"
        prefix-icon="el-icon-search"
        @input="handleSearch"
        class="search-input"
      ></el-input>
      
      <el-select v-model="statusFilter" placeholder="评论状态" @change="handleSearch" class="filter-select">
        <el-option label="全部" value=""></el-option>
        <el-option label="已发布" :value="1"></el-option>
        <el-option label="待审核" :value="0"></el-option>
      </el-select>
    </div>
    
    <!-- 评论列表 -->
    <el-table
      :data="filteredComments"
      border
      style="width: 100%"
      v-loading="loading"
      data-cy="comment-table"
    >
      <el-table-column
        label="ID"
        prop="id"
        width="80"
      ></el-table-column>
      
      <el-table-column
        label="用户"
        width="120"
      >
        <template #default="scope">
          <div class="user-info">
            <el-avatar :size="30" :src="scope.row.avatar"></el-avatar>
            <span>{{ scope.row.nickname }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="评论内容"
      >
        <template #default="scope">
          <div>
            <div>{{ scope.row.content }}</div>
            <div class="article-link">
              文章：
              <router-link
                v-if="!scope.row.articleDeleted"
                :to="`/article/${scope.row.articleId}`"
                class="article-title-link"
              >
                {{ scope.row.articleTitle }}
              </router-link>
              <span
                v-else
                class="deleted-article-title"
              >
                {{ scope.row.articleTitle }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="评论时间"
        width="180"
      >
        <template #default="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column
        label="状态"
        width="100"
      >
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '已发布' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="200"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            size="mini"
            type="success"
            @click="approveComment(scope.row)"
            data-cy="approve-btn"
          >
            批准
          </el-button>
          
          <el-button
            v-if="scope.row.status === 1"
            size="mini"
            type="info"
            @click="rejectComment(scope.row)"
            data-cy="reject-btn"
          >
            驳回
          </el-button>
          
          <el-button
            size="mini"
            type="danger"
            @click="deleteComment(scope.row)"
            data-cy="delete-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        background
        layout="prev, pager, next, sizes, total"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllComments, updateCommentStatus, deleteComment as deleteCommentApi } from '@/api/comment'
import { formatDateTime } from '@/utils/format'

export default {
  name: 'CommentManagement',
  setup() {
    const loading = ref(false)
    const comments = ref([])
    const searchText = ref('')
    const statusFilter = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    // 筛选后的评论
    const filteredComments = computed(() => {
      let result = [...comments.value]
      
      // 状态筛选
      if (statusFilter.value !== '') {
        result = result.filter(item => item.status === statusFilter.value)
      }
      
      // 内容搜索
      if (searchText.value) {
        const keyword = searchText.value.toLowerCase()
        result = result.filter(item => 
          item.content.toLowerCase().includes(keyword) || 
          item.nickname.toLowerCase().includes(keyword)
        )
      }
      
      total.value = result.length
      
      // 分页
      const startIndex = (currentPage.value - 1) * pageSize.value
      return result.slice(startIndex, startIndex + pageSize.value)
    })
    
    // 获取所有评论
    const fetchAllComments = async () => {
      loading.value = true
      try {
        // 使用管理员接口获取所有评论
        const res = await getAllComments()
        if (res.code === 200) {
          comments.value = res.data || []
          total.value = comments.value.length
        }
      } catch (error) {
        console.error('获取评论列表失败', error)
        ElMessage.error('获取评论列表失败：' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }
    
    // 审核评论
    const approveComment = async (comment) => {
      try {
        const res = await updateCommentStatus(comment.id, 1)
        if (res.code === 200) {
          ElMessage.success('评论已批准')
          comment.status = 1
        }
      } catch (error) {
        console.error('批准评论失败', error)
        ElMessage.error('批准评论失败：' + (error.message || '未知错误'))
      }
    }
    
    // 驳回评论
    const rejectComment = async (comment) => {
      try {
        const res = await updateCommentStatus(comment.id, 0)
        if (res.code === 200) {
          ElMessage.success('评论已驳回')
          comment.status = 0
        }
      } catch (error) {
        console.error('驳回评论失败', error)
        ElMessage.error('驳回评论失败：' + (error.message || '未知错误'))
      }
    }
    
    // 删除评论
    const deleteComment = async (comment) => {
      ElMessageBox.confirm('确定要删除这条评论吗？此操作不可撤销', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteCommentApi(comment.id)
          if (res.code === 200) {
            ElMessage.success('评论已删除')
            // 从列表中移除
            const index = comments.value.findIndex(item => item.id === comment.id)
            if (index !== -1) {
              comments.value.splice(index, 1)
              total.value -= 1
            }
          }
        } catch (error) {
          console.error('删除评论失败', error)
          // 如果是404错误（评论不存在），则从列表中移除该评论
          if (error.response && error.response.status === 404) {
            ElMessage({
              type: 'warning',
              message: '评论已不存在，可能已被删除'
            })
            // 从列表中移除
            const index = comments.value.findIndex(item => item.id === comment.id)
            if (index !== -1) {
              comments.value.splice(index, 1)
              total.value -= 1
            }
            // 刷新评论列表
            fetchAllComments()
          } else {
            ElMessage.error('删除评论失败：' + (error.message || '未知错误'))
          }
        }
      }).catch(() => {})
    }
    
    // 搜索处理
    const handleSearch = () => {
      currentPage.value = 1
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
    }
    
    onMounted(() => {
      fetchAllComments()
    })
    
    return {
      loading,
      comments,
      filteredComments,
      searchText,
      statusFilter,
      currentPage,
      pageSize,
      total,
      approveComment,
      rejectComment,
      deleteComment,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.comment-management {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.article-link {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.article-title-link {
  color: #409eff;
  text-decoration: none;
}

.article-title-link:hover {
  text-decoration: underline;
}

.deleted-article-title {
  color: #999;
  font-style: italic;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 