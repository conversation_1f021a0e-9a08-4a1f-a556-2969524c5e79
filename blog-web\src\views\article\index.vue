<template>
  <div class="article-detail">
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else>
      <h1 class="article-title" data-cy="article-title">{{ article.title }}</h1>
      <div class="article-info">
        <div class="article-author">
          <span>作者: {{ article.author }}</span>
          <el-button 
            v-if="article.authorId && article.authorId !== currentUser?.userId"
            size="small" 
            :type="followed ? 'primary' : 'default'"
            class="follow-btn"
            @click="followAuthor">
            {{ followed ? '已关注' : '关注' }}
          </el-button>
        </div>
        <span>发布时间: {{ formatDate(article.createTime) }}</span>
        <span>分类: {{ article.categoryName }}</span>
        <span>浏览量: {{ article.viewCount }}</span>
      </div>
      <div class="article-content" v-html="article.content"></div>
      
      <div class="article-tags">
        <el-tag v-for="tag in article.tags" :key="tag.id" size="small" class="tag">
          {{ tag.name }}
        </el-tag>
      </div>
      
      <div class="article-actions">
        <el-button 
          :type="liked ? 'danger' : 'default'" 
          size="small" 
          @click="likeArticle" 
          :loading="isLikeLoading"
          data-cy="like-button">
          <i :class="liked ? 'el-icon-thumb' : 'el-icon-thumb'"></i> 
          {{ liked ? '已点赞' : '点赞' }} ({{ article.likeCount }})
        </el-button>
        <el-button 
          :type="collected ? 'warning' : 'default'" 
          size="small" 
          @click="collectArticle" 
          :loading="isCollectLoading"
          data-cy="collect-button">
          <i :class="collected ? 'el-icon-star-on' : 'el-icon-star-off'"></i> 
          {{ collected ? '已收藏' : '收藏' }} ({{ article.collectCount }})
        </el-button>
      </div>
      
      <div class="comment-section">
        <h2>评论 ({{ article.commentCount }})</h2>
        
        <div class="comment-form">
          <!-- 如果未登录且不允许匿名评论，显示登录提示 -->
          <div v-if="!isLoggedIn && !allowAnonymousComment && configLoaded" class="login-tip">
            <p>请 <router-link to="/login">登录</router-link> 后发表评论</p>
          </div>

          <!-- 配置加载中 -->
          <div v-else-if="!configLoaded" class="loading-tip">
            <p>加载中...</p>
          </div>

          <!-- 评论表单 -->
          <div v-else>
            <!-- 匿名用户信息表单 -->
            <div v-if="!isLoggedIn && allowAnonymousComment" class="anonymous-form">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-input
                    v-model="anonymousNickname"
                    placeholder="请输入昵称"
                    maxlength="50">
                  </el-input>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="anonymousEmail"
                    placeholder="请输入邮箱"
                    maxlength="100">
                  </el-input>
                </el-col>
              </el-row>
            </div>

            <!-- 评论内容 -->
            <el-input
              v-model="commentContent"
              type="textarea"
              :rows="3"
              placeholder="请输入评论内容"
              data-cy="comment-input"
              class="comment-textarea">
            </el-input>

            <div class="comment-button">
              <el-button type="primary" @click="submitComment" data-cy="submit-comment">发表评论</el-button>
            </div>
          </div>
        </div>
        
        <div class="comment-list" data-cy="comment-list">
          <div v-if="comments.length === 0" class="no-comment">
            暂无评论，快来发表第一条评论吧！
          </div>
          <div v-else>
            <!-- 递归显示评论 -->
            <div v-for="comment in comments" :key="comment.id" class="comment-item">
              <div class="comment-user">
                <el-avatar :size="40" :src="comment.avatar" class="avatar"></el-avatar>
                <div class="user-info">
                  <span class="username">{{ comment.nickname }}</span>
                  <span class="time">{{ formatDate(comment.createTime) }}</span>
                </div>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
              
              <!-- 回复按钮 -->
              <div class="comment-actions">
                <span class="reply-btn" @click="showReplyForm(comment.id)">回复</span>
                <span v-if="canDelete(comment)" class="delete-btn" @click="deleteUserComment(comment.id)">删除</span>
              </div>
              
              <!-- 回复表单 -->
              <div v-if="replyingTo === comment.id" class="reply-form">
                <el-input
                  v-model="replyContent"
                  type="textarea"
                  :rows="2"
                  placeholder="回复评论">
                </el-input>
                <div class="reply-actions">
                  <el-button size="small" @click="cancelReply">取消</el-button>
                  <el-button size="small" type="primary" @click="submitReply(comment)">回复</el-button>
                </div>
              </div>
              
              <!-- 子评论 -->
              <div v-if="comment.children && comment.children.length > 0" class="comment-children">
                <div v-for="child in comment.children" :key="child.id" class="comment-child">
                  <div class="reply-indicator"></div>
                  <div class="comment-user">
                    <el-avatar :size="30" :src="child.avatar" class="avatar"></el-avatar>
                    <div class="user-info">
                      <span class="username">
                        {{ child.nickname }} 
                        <span class="reply-to">回复</span> 
                        <span class="reply-username">{{ child.toNickname || comment.nickname }}</span>
                      </span>
                      <span class="time">{{ formatDate(child.createTime) }}</span>
                    </div>
                  </div>
                  <div class="comment-content">
                    <div class="reply-quote">
                      <span class="quote-username">@{{ child.toNickname || comment.nickname }}:</span>
                      <span v-if="child.toUserId === comment.userId">{{ comment.content.length > 30 ? comment.content.substring(0, 30) + '...' : comment.content }}</span>
                      <span v-else>
                        {{ findReplyContent(comment.children, child.toUserId) }}
                      </span>
                    </div>
                    {{ child.content }}
                  </div>
                  
                  <!-- 子评论的回复按钮 -->
                  <div class="comment-actions">
                    <span class="reply-btn" @click="showReplyForm(child.id, comment.id)">回复</span>
                    <span v-if="canDelete(child)" class="delete-btn" @click="deleteUserComment(child.id)">删除</span>
                  </div>
                  
                  <!-- 子评论的回复表单 -->
                  <div v-if="replyingTo === child.id" class="reply-form">
                    <el-input
                      v-model="replyContent"
                      type="textarea"
                      :rows="2"
                      placeholder="回复评论">
                    </el-input>
                    <div class="reply-actions">
                      <el-button size="small" @click="cancelReply">取消</el-button>
                      <el-button size="small" type="primary" @click="submitReply(child, comment.id)">回复</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getArticleDetail } from '@/api/article'
import { getArticleComments, createComment, deleteComment } from '@/api/comment'
import { getInteractionStatus, addInteraction, cancelInteraction } from '@/api/interaction'
import { followUser, unfollowUser, getFollowStatus } from '@/api/follow'
import { useUserStore } from '@/store/user'
import { siteConfig, fetchCommentConfig } from '@/utils/siteConfig'

export default {
  name: 'ArticleDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const loading = ref(true)
    const liked = ref(false)
    const collected = ref(false)
    const followed = ref(false)
    const commentContent = ref('')
    const replyContent = ref('')
    const replyingTo = ref(null)
    const parentCommentId = ref(null)
    const isLikeLoading = ref(false) // 添加点赞状态锁定变量
    const isCollectLoading = ref(false) // 添加收藏状态锁定变量

    // 匿名评论相关状态
    const anonymousNickname = ref('')
    const anonymousEmail = ref('')
    const configLoaded = ref(false)
    const allowAnonymousComment = computed(() => {
      return siteConfig.value.allowAnonymousComment === 'true'
    })
    
    const article = reactive({
      id: '',
      title: '',
      author: '',
      authorId: null,
      createTime: '',
      categoryName: '',
      viewCount: 0,
      likeCount: 0,
      collectCount: 0,
      commentCount: 0,
      content: '',
      tags: []
    })
    
    const comments = ref([])
    
    // 获取当前用户信息
    const currentUser = computed(() => userStore.userInfo)
    const isLoggedIn = computed(() => userStore.isLogin)
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }

    // 判断是否可以删除评论
    const canDelete = (comment) => {
      return isLoggedIn.value && (
        currentUser.value.userId === comment.userId || 
        currentUser.value.role === 'admin'
      )
    }
    
    const fetchArticle = async (id) => {
      loading.value = true
      try {
        const res = await getArticleDetail(id)
        if (res.code === 200 && res.data) {
          Object.assign(article, res.data);
          // 获取文章互动状态
          fetchInteractionStatus(id);
          // 获取评论列表
          fetchComments(id);
        }
      } catch (error) {
        console.error('获取文章详情失败', error)
        // 检查错误类型
        if (error.response) {
          const status = error.response.status
          if (status === 401) {
            // 如果是未授权错误，不需要跳转到登录页面，提示用户可以阅读但不能互动
            ElMessage.info('当前为游客模式，您可以阅读文章但无法点赞、评论等')
          } else if (status === 404) {
            // 文章不存在，跳转到404页面
            ElMessage.error('文章不存在或已被删除')
            router.push('/404')
          } else {
            // 其他服务器错误
            ElMessage.error('获取文章详情失败，请稍后重试')
          }
        } else {
          // 网络错误或其他错误
          ElMessage.error('网络错误，请检查网络连接')
        }
      } finally {
        loading.value = false
      }
    }
    
    // 获取文章互动状态（点赞、收藏）
    const fetchInteractionStatus = async (articleId) => {
      if (!articleId) return;
      
      try {
        const res = await getInteractionStatus(articleId);
        if (res.code === 200 && res.data) {
          // 对于已登录用户，设置互动状态
          if (isLoggedIn.value) {
            liked.value = res.data.isLiked;
            collected.value = res.data.isCollected;
          } else {
            // 未登录用户，默认未互动状态
            liked.value = false;
            collected.value = false;
          }
          
          // 不论是否登录，都更新点赞和收藏数
          article.likeCount = res.data.likeCount;
          article.collectCount = res.data.collectCount;
        }
      } catch (error) {
        console.error('获取互动状态失败', error);
      }
      
      // 如果文章有作者ID且不是当前用户，获取关注状态
      if (article.authorId && isLoggedIn.value && article.authorId !== currentUser.value?.userId) {
        fetchFollowStatus(article.authorId);
      }
    }
    
    // 获取关注状态
    const fetchFollowStatus = async (authorId) => {
      try {
        const res = await getFollowStatus(authorId);
        if (res.code === 200 && res.data) {
          followed.value = res.data.isFollowing;
        }
      } catch (error) {
        console.error('获取关注状态失败', error);
      }
    }
    
    const fetchComments = async (articleId) => {
      try {
        const res = await getArticleComments(articleId)
        if (res.code === 200 && res.data) {
          comments.value = res.data
        }
      } catch (error) {
        console.error('获取评论失败', error)
      }
    }
    
    const likeArticle = async () => {
      // 如果正在处理中，直接返回，防止重复点击
      if (isLikeLoading.value) return
      
      if (!isLoggedIn.value) {
        ElMessageBox.confirm('登录后才能点赞，是否立即登录？', '提示', {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          localStorage.setItem('redirectUrl', route.fullPath)
          router.push('/login')
        }).catch(() => {})
        return
      }
      
      try {
        isLikeLoading.value = true // 开始处理前锁定
        
        if (!liked.value) {
          // 点赞文章
          const res = await addInteraction({ articleId: article.id, type: 'like' });
          if (res.code === 200) {
            liked.value = true;
            article.likeCount++;
            ElMessage.success('点赞成功');
          }
        } else {
          // 取消点赞
          const res = await cancelInteraction({ articleId: article.id, type: 'like' });
          if (res.code === 200) {
            liked.value = false;
            article.likeCount = Math.max(0, article.likeCount - 1);
            ElMessage.success('已取消点赞');
          }
        }
      } catch (error) {
        console.error('点赞操作失败', error);
      } finally {
        isLikeLoading.value = false // 处理完成后解锁
      }
    }
    
    const collectArticle = async () => {
      // 如果正在处理中，直接返回，防止重复点击
      if (isCollectLoading.value) return
      
      if (!isLoggedIn.value) {
        ElMessageBox.confirm('登录后才能收藏，是否立即登录？', '提示', {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          localStorage.setItem('redirectUrl', route.fullPath)
          router.push('/login')
        }).catch(() => {})
        return
      }
      
      try {
        isCollectLoading.value = true // 开始处理前锁定
        if (!collected.value) {
          // 收藏文章
          const res = await addInteraction({ articleId: article.id, type: 'collect' });
          if (res.code === 200) {
            collected.value = true;
            article.collectCount++;
            ElMessage.success('收藏成功');
          }
        } else {
          // 取消收藏
          const res = await cancelInteraction({ articleId: article.id, type: 'collect' });
          if (res.code === 200) {
            collected.value = false;
            article.collectCount = Math.max(0, article.collectCount - 1);
            ElMessage.success('已取消收藏');
          }
        }
      } catch (error) {
        console.error('收藏操作失败', error);
      } finally {
        isCollectLoading.value = false // 处理完成后解锁
      }
    }
    
    const submitComment = async () => {
      if (!commentContent.value.trim()) {
        ElMessage.warning('评论内容不能为空')
        return
      }

      // 如果未登录且不允许匿名评论，提示登录
      if (!isLoggedIn.value && !allowAnonymousComment.value) {
        ElMessageBox.confirm('登录后才能发表评论，是否立即登录？', '提示', {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          localStorage.setItem('redirectUrl', route.fullPath)
          router.push('/login')
        }).catch(() => {})
        return
      }

      // 如果是匿名评论，验证昵称和邮箱
      if (!isLoggedIn.value && allowAnonymousComment.value) {
        if (!anonymousNickname.value.trim()) {
          ElMessage.warning('请填写昵称')
          return
        }
        if (!anonymousEmail.value.trim()) {
          ElMessage.warning('请填写邮箱')
          return
        }
        // 简单的邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(anonymousEmail.value)) {
          ElMessage.warning('请填写正确的邮箱格式')
          return
        }
      }

      try {
        const commentData = {
          content: commentContent.value,
          articleId: article.id
        }

        // 如果是匿名评论，添加匿名用户信息
        if (!isLoggedIn.value && allowAnonymousComment.value) {
          commentData.anonymousNickname = anonymousNickname.value.trim()
          commentData.anonymousEmail = anonymousEmail.value.trim()
        }
        
        const res = await createComment(commentData)
        if (res.code === 200) {
          ElMessage.success('评论发表成功')
          commentContent.value = ''
          // 清空匿名用户信息
          if (!isLoggedIn.value) {
            anonymousNickname.value = ''
            anonymousEmail.value = ''
          }
          // 重新加载评论列表
          fetchComments(article.id)
          // 更新文章评论数
          article.commentCount++
        }
      } catch (error) {
        // 只记录错误，不再显示消息
        // 全局拦截器已经处理了错误提示
        console.error('提交评论失败', error)
      }
    }
    
    const showReplyForm = (commentId, parentId = null) => {
      if (!isLoggedIn.value) {
        ElMessageBox.confirm('登录后才能回复评论，是否立即登录？', '提示', {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          // 保存重定向地址的方式需要更新
          // 可以使用 localStorage 或创建专门的路由存储
          localStorage.setItem('redirectUrl', route.fullPath)
          router.push('/login')
        }).catch(() => {})
        return
      }
      
      replyingTo.value = commentId
      parentCommentId.value = parentId
      replyContent.value = ''
    }
    
    const cancelReply = () => {
      replyingTo.value = null
      parentCommentId.value = null
      replyContent.value = ''
    }
    
    const submitReply = async (comment, parentId = null) => {
      if (!replyContent.value.trim()) {
        ElMessage.warning('回复内容不能为空')
        return
      }
      
      try {
        const replyData = {
          content: replyContent.value,
          articleId: article.id,
          parentId: parentId || comment.id,
          toUserId: comment.userId  // 这里直接使用评论的userId作为回复目标
        }
        
        const res = await createComment(replyData)
        if (res.code === 200) {
          ElMessage.success('回复发表成功')
          cancelReply()
          // 重新加载评论列表
          fetchComments(article.id)
          // 更新文章评论数
          article.commentCount++
        }
      } catch (error) {
        // 只记录错误，不再显示消息
        // 全局拦截器已经处理了错误提示
        console.error('提交回复失败', error)
      }
    }
    
    const deleteUserComment = async (commentId) => {
      ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteComment(commentId)
          if (res.code === 200) {
            ElMessage.success('评论删除成功')
            // 重新加载评论列表
            fetchComments(article.id)
            // 更新文章评论数
            article.commentCount = Math.max(0, article.commentCount - 1)
          }
        } catch (error) {
          console.error('删除评论失败', error)
          // 全局拦截器已经处理了错误提示，这里不再重复显示
          // 只记录错误日志
        }
      }).catch(() => {})
    }
    
    // 查找被回复评论的内容
    const findReplyContent = (comments, userId) => {
      if (!comments || !userId) return '';
      const replyComment = comments.find(c => c.userId === userId);
      if (replyComment) {
        return replyComment.content.length > 30 ? 
          replyComment.content.substring(0, 30) + '...' : 
          replyComment.content;
      }
      return '';
    }

    // 关注/取消关注作者
    const followAuthor = async () => {
      if (!isLoggedIn.value) {
        router.push('/login');
        return;
      }
      
      try {
        if (followed.value) {
          // 取消关注
          const res = await unfollowUser(article.authorId);
          if (res.code === 200) {
            followed.value = false;
            ElMessage.success('已取消关注');
          }
        } else {
          // 关注
          const res = await followUser(article.authorId);
          if (res.code === 200) {
            followed.value = true;
            ElMessage.success('关注成功');
          }
        }
      } catch (error) {
        console.error('关注操作失败', error);
      }
    }

    onMounted(async () => {
      const id = route.params.id
      if (id) {
        fetchArticle(id)
      }
      // 获取评论配置
      try {
        await fetchCommentConfig()
      } catch (error) {
        console.error('获取评论配置失败:', error)
      } finally {
        configLoaded.value = true
      }
    })
    
    return {
      loading,
      article,
      comments,
      liked,
      collected,
      followed,
      commentContent,
      replyContent,
      replyingTo,
      currentUser,
      isLoggedIn,
      isLikeLoading,
      isCollectLoading,
      // 匿名评论相关
      anonymousNickname,
      anonymousEmail,
      allowAnonymousComment,
      configLoaded,
      likeArticle,
      collectArticle,
      followAuthor,
      submitComment,
      showReplyForm,
      cancelReply,
      submitReply,
      canDelete,
      deleteUserComment,
      formatDate,
      findReplyContent
    }
  }
}
</script>

<style scoped>
.article-detail {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.loading {
  padding: 20px;
}

.article-title {
  font-size: 28px;
  margin-bottom: 15px;
}

.article-info {
  margin: 15px 0;
  color: #666;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.article-author {
  display: flex;
  align-items: center;
  gap: 10px;
}

.follow-btn {
  margin-left: 5px;
}

.article-content {
  line-height: 1.8;
  margin-bottom: 30px;
}

.article-tags {
  margin-bottom: 20px;
}

.tag {
  margin-right: 10px;
}

.article-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.comment-section {
  margin-top: 40px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.comment-form {
  margin-bottom: 30px;
}

.login-tip {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #666;
}

.login-tip a {
  color: #409eff;
  text-decoration: none;
}

.login-tip a:hover {
  text-decoration: underline;
}

.loading-tip {
  text-align: center;
  padding: 20px;
  color: #999;
}

.anonymous-form {
  margin-bottom: 15px;
}

.comment-textarea {
  margin-top: 10px;
}

.comment-button {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.comment-list {
  margin-top: 20px;
}

.no-comment {
  color: #999;
  text-align: center;
  padding: 20px 0;
}

.comment-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.avatar {
  margin-right: 10px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: bold;
  color: #409EFF;
}

.time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  margin-bottom: 10px;
  line-height: 1.6;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.reply-btn, .delete-btn {
  font-size: 12px;
  color: #409EFF;
  cursor: pointer;
}

.delete-btn {
  color: #F56C6C;
}

.reply-form {
  margin: 10px 0 10px 40px;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.comment-children {
  margin-left: 40px;
  padding: 10px 0;
  border-left: 2px solid #eaeaea;
}

.comment-child {
  padding: 12px;
  margin-bottom: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  position: relative;
}

.reply-indicator {
  position: absolute;
  left: -12px;
  top: 20px;
  width: 10px;
  height: 2px;
  background-color: #eaeaea;
}

.reply-to {
  font-size: 12px;
  color: #999;
  margin: 0 4px;
}

.reply-username {
  color: #409EFF;
  font-weight: bold;
}

.reply-quote {
  background-color: #f0f0f0;
  border-left: 3px solid #409EFF;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  border-radius: 4px;
}

.quote-username {
  color: #409EFF;
  font-weight: bold;
  margin-right: 6px;
}
</style> 