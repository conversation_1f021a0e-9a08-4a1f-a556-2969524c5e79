import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/styles/index.scss'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { fetchSiteConfig } from '@/utils/siteConfig'

// 创建应用实例
const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 初始化应用状态
const initializeApp = async () => {
  // 首先加载网站配置
  await fetchSiteConfig()

  // 然后初始化用户状态
  const token = getToken()
  if (token) {
    // 如果存在token，尝试恢复用户会话
    const userStore = useUserStore()
    try {
      await userStore.getInfo()
      console.log('用户会话已恢复')
    } catch (error) {
      console.error('恢复用户会话失败', error)
      // 如果恢复失败，清除无效token
      userStore.resetState()
    }
  }
}

// 确保在路由导航前初始化应用状态
initializeApp().finally(() => {
  app.mount('#app')
})