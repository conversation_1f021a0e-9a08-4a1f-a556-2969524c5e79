<template>
  <div class="admin-layout">
    <el-container>
      <el-aside width="200px">
        <div class="logo">
          <router-link to="/admin/dashboard">博客管理系统</router-link>
        </div>
        <el-menu
          router
          :default-active="activeMenu"
          background-color="#304156"
          text-color="#fff"
          active-text-color="#409EFF">
          <el-menu-item index="/admin/dashboard">
            <i class="el-icon-s-home"></i>
            <span>仪表盘</span>
          </el-menu-item>
          <el-menu-item index="/admin/article">
            <i class="el-icon-document"></i>
            <span>文章管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/category">
            <i class="el-icon-collection"></i>
            <span>分类管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/tag">
            <i class="el-icon-collection-tag"></i>
            <span>标签管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/comment">
            <i class="el-icon-chat-dot-square"></i>
            <span>评论管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/user">
            <i class="el-icon-user"></i>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/setting">
            <i class="el-icon-setting"></i>
            <span>系统设置</span>
          </el-menu-item>
          <el-menu-item index="/admin/email-template">
            <i class="el-icon-message"></i>
            <span>邮件模板</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-container>
        <el-header>
          <div class="header-right">
            <el-dropdown trigger="click">
              <span class="user-dropdown" data-cy="admin-user-menu-trigger">
                {{ username }}<i class="el-icon-arrow-down"></i>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <router-link to="/user/profile">个人资料</router-link>
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="logout" data-cy="admin-logout-button">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <el-main>
          <router-view></router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { removeToken } from '@/utils/auth'

export default {
  name: 'AdminLayout',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const username = ref('管理员')
    
    const activeMenu = computed(() => {
      return route.path
    })
    
    const logout = () => {
      removeToken()
      router.push('/login')
    }
    
    return {
      username,
      activeMenu,
      logout
    }
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #304156;
  color: #fff;
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.logo a {
  color: #fff;
  text-decoration: none;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
  color: #333;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style> 