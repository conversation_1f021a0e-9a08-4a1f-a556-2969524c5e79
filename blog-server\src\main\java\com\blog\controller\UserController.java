package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.common.api.ResultCode;
import com.blog.common.exception.NotFoundException;
import com.blog.dto.PasswordUpdateDTO;
import com.blog.entity.User;
import com.blog.service.UserService;
import com.blog.vo.UserInfoVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.BeanUtils;
import com.blog.util.SecurityUtil;
import io.swagger.annotations.ApiOperation;
import com.blog.vo.ArticleVO;
import com.blog.service.CollectionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("")
public class UserController {
    
    @Resource
    private UserService userService;
    
    @Resource
    private CollectionService collectionService;
    
    /**
     * 获取当前登录用户信息
     */
    @ApiOperation("获取当前登录用户信息")
    @GetMapping("/user/info")
    public Result<UserInfoVO> getCurrentUserInfo() {
        String username = SecurityUtil.getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            return Result.failed(ResultCode.FAILED);
        }

        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        
        return Result.success(userInfoVO);
    }
    
    /**
     * 获取当前登录用户信息 - /users/info路径
     */
    @GetMapping("/users/info")
    public Result<?> getCurrentUser() {
        return getCurrentUserInfo();
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping({"/user/info", "/users/info"})
    public Result<?> updateUserInfo(@RequestBody User user) {
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User currentUser = userService.getUserByUsername(username);
        
        // 只允许修改自己的信息
        user.setId(currentUser.getId());
        // 不允许修改用户名和角色
        user.setUsername(currentUser.getUsername());
        user.setRole(currentUser.getRole());
        // 不允许修改密码（密码修改应该有单独的接口）
        user.setPassword(null);
        
        if (userService.updateById(user)) {
            return Result.success();
        }
        return Result.failed("更新用户信息失败");
    }

    @ApiOperation("修改密码")
    @PutMapping("/user/password")
    public Result<?> updatePassword(@RequestBody @Valid PasswordUpdateDTO passwordUpdateDTO) {
        String username = SecurityUtil.getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        boolean success = userService.updatePassword(user.getId(), passwordUpdateDTO);
        if (success) {
            return Result.success(null, "密码修改成功");
        } else {
            return Result.failed("密码修改失败");
        }
    }
    
    @ApiOperation("更新用户头像")
    @PutMapping("/user/avatar")
    public Result<?> updateAvatar(@RequestParam String avatar) {
        String username = SecurityUtil.getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        user.setAvatar(avatar);
        boolean success = userService.updateById(user);
        if (success) {
            return Result.success(null, "头像更新成功");
        } else {
            return Result.failed("头像更新失败");
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/user/collections")
    @PreAuthorize("isAuthenticated()")
    public Result<?> getUserCollections() {
        // 获取当前登录用户
        String username = SecurityUtil.getCurrentUsername();
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 获取用户收藏列表
        List<ArticleVO> collections = collectionService.getUserCollections(user.getId());
        return Result.success(collections);
    }

    /**
     * 获取指定用户信息（管理员权限）
     */
    @GetMapping({"/user/detail/{id}", "/users/{id}"})
    @PreAuthorize("hasRole('ADMIN')")
    public Result<?> getUserById(@PathVariable Long id) {
        User user = userService.getById(id);
        if (user != null) {
            // 清除敏感信息
            user.setPassword(null);
            return Result.success(user);
        }
        return Result.failed("用户不存在");
    }
    
    /**
     * 禁用/启用用户（管理员权限）
     */
    @PutMapping({"/user/{id}/status", "/users/{id}/status"})
    @PreAuthorize("hasRole('ADMIN')")
    public Result<?> updateUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        // 检查要修改的用户
        User targetUser = userService.getById(id);
        if (targetUser == null) {
            return Result.failed("用户不存在");
        }

        // 不允许禁用管理员账户
        if ("admin".equals(targetUser.getRole()) && status == 0) {
            return Result.failed("不允许禁用管理员账户");
        }

        User user = new User();
        user.setId(id);
        user.setStatus(status);

        if (userService.updateById(user)) {
            return Result.success();
        }
        return Result.failed("更新用户状态失败");
    }

    /**
     * 获取用户列表（管理员权限）
     */
    @GetMapping("/users")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<IPage<User>> getUserList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Integer status) {

        Page<User> page = new Page<>(current, size);

        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // 用户名模糊查询
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like(User::getUsername, username.trim());
        }

        // 状态筛选
        if (status != null) {
            queryWrapper.eq(User::getStatus, status);
        }

        // 排序
        queryWrapper.orderByDesc(User::getCreateTime);

        // 分页查询
        IPage<User> userPage = userService.page(page, queryWrapper);

        // 清除敏感信息
        userPage.getRecords().forEach(user -> user.setPassword(null));

        return Result.success(userPage);
    }

    /**
     * 删除用户（管理员权限）
     */
    @DeleteMapping("/users/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<?> deleteUser(@PathVariable Long id) {
        // 不允许删除管理员账户
        User user = userService.getById(id);
        if (user != null && "admin".equals(user.getRole())) {
            return Result.failed("不允许删除管理员账户");
        }

        if (userService.removeById(id)) {
            return Result.success();
        }
        return Result.failed("删除用户失败");
    }
}