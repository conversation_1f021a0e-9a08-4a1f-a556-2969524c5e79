package com.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.dto.EmailTemplateDTO;
import com.blog.entity.EmailTemplate;
import com.blog.vo.EmailTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * 邮件模板服务接口
 */
public interface EmailTemplateService extends IService<EmailTemplate> {

    /**
     * 添加邮件模板
     * @param templateDTO 模板DTO
     * @return 新增模板的ID
     */
    Long addTemplate(EmailTemplateDTO templateDTO);

    /**
     * 更新邮件模板
     * @param templateDTO 模板DTO
     * @return 是否更新成功
     */
    boolean updateTemplate(EmailTemplateDTO templateDTO);

    /**
     * 删除邮件模板
     * @param id 模板ID
     * @return 是否删除成功
     */
    boolean deleteTemplate(Long id);

    /**
     * 获取邮件模板详情
     * @param id 模板ID
     * @return 模板VO
     */
    EmailTemplateVO getTemplateDetail(Long id);

    /**
     * 获取邮件模板列表
     * @return 模板VO列表
     */
    List<EmailTemplateVO> getTemplateList();

    /**
     * 根据模板代码获取模板
     * @param templateCode 模板代码
     * @return 邮件模板
     */
    EmailTemplate getTemplateByCode(String templateCode);

    /**
     * 渲染邮件模板
     * @param templateCode 模板代码
     * @param variables 变量Map
     * @return 渲染后的邮件内容
     */
    String renderTemplate(String templateCode, Map<String, String> variables);

    /**
     * 预览邮件模板
     * @param templateCode 模板代码
     * @param variables 变量Map
     * @return 预览结果
     */
    Map<String, String> previewTemplate(String templateCode, Map<String, String> variables);

    /**
     * 启用/禁用模板
     * @param id 模板ID
     * @param status 状态
     * @return 是否操作成功
     */
    boolean updateTemplateStatus(Long id, Integer status);
}
