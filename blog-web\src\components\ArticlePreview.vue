<template>
  <div class="article-preview">
    <div class="article-header">
      <h2 class="article-title">
        <slot name="title">{{ article.title }}</slot>
      </h2>
      <div class="article-meta">
        <span class="article-author">
          <i class="el-icon-user"></i>
          {{ article.authorName }}
        </span>
        <span class="article-date">
          <i class="el-icon-time"></i>
          {{ formatDate(article.createTime) }}
        </span>
        <span class="article-views">
          <i class="el-icon-view"></i>
          {{ article.viewCount }} 阅读
        </span>
        <span v-if="article.isTop === 1" class="article-top">
          <el-tag size="small" type="danger">置顶</el-tag>
        </span>
      </div>
    </div>
    <div class="article-content">
      <div v-if="article.coverImage" class="article-cover">
        <img :src="coverImage" :alt="article.title">
      </div>
      <div class="article-summary">
        {{ article.summary }}
      </div>
    </div>
    <div class="article-footer">
      <div class="article-tags" v-if="article.tags">
        <el-tag size="small" v-for="(tag, index) in tags" :key="index">{{ tag }}</el-tag>
      </div>
      <div class="article-actions">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { buildResourceUrl } from '@/config/settings'

export default {
  name: 'ArticlePreview',
  props: {
    article: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
    
    // 处理标签
    const tags = computed(() => {
      if (!props.article.tags) return []
      return props.article.tags.split(',').filter(tag => tag.trim() !== '')
    })
    
    // 处理封面图片URL
    const coverImage = computed(() => {
      if (!props.article.coverImage) return '';
      return buildResourceUrl(props.article.coverImage);
    })
    
    return {
      formatDate,
      tags,
      coverImage
    }
  }
}
</script>

<style scoped>
.article-preview {
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.article-preview:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
}

.article-header {
  margin-bottom: 15px;
}

.article-title {
  margin: 0 0 10px;
  font-size: 1.5rem;
  font-weight: 600;
}

.article-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: #666;
}

.article-meta > span {
  margin-right: 15px;
}

.article-content {
  display: flex;
  margin-bottom: 15px;
}

.article-cover {
  flex: 0 0 200px;
  margin-right: 20px;
  overflow: hidden;
  border-radius: 4px;
}

.article-cover img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.article-summary {
  flex: 1;
  color: #666;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
}

.article-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.article-actions {
  display: flex;
  align-items: center;
}

.article-top {
  margin-left: auto;
}

@media (max-width: 768px) {
  .article-content {
    flex-direction: column;
  }
  
  .article-cover {
    margin-right: 0;
    margin-bottom: 15px;
    width: 100%;
  }
}
</style> 