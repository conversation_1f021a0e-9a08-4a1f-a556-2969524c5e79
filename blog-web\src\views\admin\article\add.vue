<template>
  <div class="article-add">
    <h1>添加文章</h1>
    <div class="form-container">
      <el-form :model="articleForm" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入文章标题"></el-input>
        </el-form-item>
        <el-form-item label="封面">
          <el-upload
            class="cover-uploader"
            action="/api/upload"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload">
            <img v-if="articleForm.coverImage" :src="articleForm.coverImage" class="cover-image" />
            <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="articleForm.categoryId" placeholder="请选择分类">
            <el-option 
              v-for="item in categories" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="articleForm.tagIds"
            multiple
            filterable
            placeholder="请选择或输入新标签"
            style="width: 100%">
            <el-option
              v-for="item in allTags"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="摘要">
          <el-input 
            v-model="articleForm.summary" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入文章摘要">
          </el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <div class="editor-container">
            <MarkdownEditor v-model="articleForm.content" height="500px" />
          </div>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="articleForm.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="置顶">
          <el-switch v-model="articleForm.isTop" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="允许评论">
          <el-switch v-model="articleForm.allowComment" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">发布文章</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { createArticle } from '@/api/article'
import { getCategories } from '@/api/category'
import { getTags } from '@/api/tag'
import { useUserStore } from '@/store/user'
import { buildResourceUrl } from '@/config/settings'

export default {
  name: 'ArticleAdd',
  components: {
    MarkdownEditor,
    Plus
  },
  setup() {
    const router = useRouter()
    const formRef = ref(null)
    const submitting = ref(false)
    const allTags = ref([])
    
    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入文章标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入文章内容', trigger: 'blur' }
      ]
    }
    
    const articleForm = reactive({
      title: '',
      categoryId: null,
      tagIds: [],
      summary: '',
      content: '',
      coverImage: '',
      status: 1,
      isTop: 0,
      allowComment: 1
    })
    
    const categories = ref([])
    
    // 获取分类列表
    const fetchCategories = async () => {
      try {
        const res = await getCategories()
        if (res.code === 200) {
          categories.value = res.data
        } else {
          ElMessage.error(res.message || '获取分类列表失败')
        }
      } catch (error) {
        console.error('获取分类列表失败', error)
        ElMessage.error('获取分类列表失败')
      }
    }

    // 获取标签列表
    const fetchTags = async () => {
      try {
        const res = await getTags()
        if (res.code === 200) {
          allTags.value = res.data
        } else {
          ElMessage.error(res.message || '获取标签列表失败')
        }
      } catch (error) {
        console.error('获取标签列表失败', error)
        ElMessage.error('获取标签列表失败')
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          submitting.value = true
          try {
            const res = await createArticle(articleForm)
            if (res.code === 200) {
              ElMessage.success('文章发布成功')
              router.push('/admin/article')
            } else {
              ElMessage.error(res.message || '文章发布失败')
            }
          } catch (error) {
            console.error('文章发布失败', error)
            ElMessage.error('文章发布失败，请稍后重试')
          } finally {
            submitting.value = false
          }
        } else {
          ElMessage.warning('请完善表单信息')
          return false
        }
      })
    }
    
    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
      articleForm.coverImage = ''
      articleForm.isTop = 0
      articleForm.allowComment = 1
    }
    
    // 处理封面上传成功
    const handleCoverSuccess = (res) => {
      if (res.code === 200) {
        // 使用统一的资源URL构建函数
        let imageUrl = res.data;
        
        // 调试信息
        console.log('上传成功响应:', res);
        console.log('原始图片URL:', imageUrl);
        
        // 使用统一的资源URL构建函数
        articleForm.coverImage = buildResourceUrl(imageUrl);
        console.log('构建的完整URL:', articleForm.coverImage);
        
        // 创建一个图片元素测试URL是否可访问
        const testImg = new Image();
        testImg.onload = () => console.log('图片URL有效，可以正常加载');
        testImg.onerror = () => console.error('图片URL无效，无法加载');
        testImg.src = articleForm.coverImage;
        
        ElMessage.success('封面上传成功');
      } else {
        ElMessage.error(res.message || '封面上传失败');
      }
    }
    
    // 上传前检查
    const beforeCoverUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('上传封面只能是图片格式!')
      }
      if (!isLt2M) {
        ElMessage.error('上传封面图片大小不能超过 2MB!')
      }
      
      return isImage && isLt2M
    }

    // 使用Pinia的用户store
    const userStore = useUserStore()
    const uploadHeaders = computed(() => {
      return {
        Authorization: `Bearer ${userStore.token}`
      }
    })
    
    onMounted(() => {
      fetchCategories()
      fetchTags()
    })
    
    return {
      formRef,
      articleForm,
      rules,
      categories,
      allTags,
      submitting,
      uploadHeaders,
      submitForm,
      resetForm,
      handleCoverSuccess,
      beforeCoverUpload
    }
  }
}
</script>

<style scoped>
.article-add {
  padding: 20px;
}
.form-container {
  max-width: 900px;
  margin-top: 20px;
}
.editor-container {
  width: 100%;
  margin-bottom: 20px;
}
.cover-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.cover-uploader:hover {
  border-color: #409EFF;
}
.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style> 