package com.blog.enums;

import lombok.Getter;

/**
 * 关注/取关操作的结果类型枚举
 */
@Getter
public enum FollowResultType {
    SUCCESS(200, "操作成功"),
    USER_NOT_FOUND(404, "用户不存在"),
    CANNOT_FOLLOW_SELF(400, "不能关注自己"),
    ALREADY_FOLLOWING(400, "已关注该用户"),
    NOT_FOLLOWING(400, "未关注该用户"),
    FAILED(500, "操作失败");

    private final long code;
    private final String message;

    FollowResultType(long code, String message) {
        this.code = code;
        this.message = message;
    }
} 