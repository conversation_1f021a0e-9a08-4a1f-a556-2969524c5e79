/**
 * 测试套件：用户认证流程
 * 描述：覆盖用户通过UI进行登录和登出的核心操作。
 */
describe('用户认证流程', () => {

  /**
   * 测试用例：用户应能通过UI表单成功登录
   */
  it('用户应能通过UI表单成功登录', () => {
    // 步骤1: 访问登录页
    cy.visit('/login');

    // 步骤2: 输入用户名和密码
    // 直接对 data-cy 元素进行 type 操作，Cypress 会自动找到可输入的子元素
    cy.get('[data-cy=login-username]').type('admin');
    cy.get('[data-cy=login-password]').type('admin123');

    // 步骤3: 点击登录按钮
    cy.get('[data-cy=login-button]').click();

    // 步骤4: 验证是否成功跳转到管理员后台
    cy.url().should('include', '/admin/dashboard');

    // 步骤5: 验证仪表盘页面是否加载成功
    // "仪表盘"是该页面独有的H1标题，是最佳的验证目标
    cy.contains('h1', '仪表盘').should('be.visible');
  });

  /**
   * 测试用例：已登录的管理员应能从后台成功登出
   * 描述：此测试验证的是管理员在专属后台布局中的登出功能。
   */
  it('已登录的管理员应能从后台成功登出', () => {
    // 前置准备: 通过API快速登录，为后续操作准备好一个已认证的管理员会话
    cy.request({
      method: 'POST',
      url: '/api/auth/login', // 登录API端点
      body: {
        username: 'admin', // 管理员用户名
        password: 'admin123' // 管理员密码
      }
    }).then(response => {
      // 提取登录成功后返回的token和用户信息
      const { token, ...userInfo } = response.body.data;
      
      // 手动将 token 和用户信息写入浏览器的cookie，模拟登录状态
      cy.setCookie('blog_token', token);
      // Cookie的值必须是经过URL编码的字符串，以防止特殊字符引起解析错误
      cy.setCookie('blog_user_info', encodeURIComponent(JSON.stringify(userInfo)));
    });

    // 步骤1: 访问站点根路径
    cy.visit('/');

    // 步骤2: 验证应用是否已自动重定向到管理员仪表盘
    // 这是关键验证，证明前端路由守卫能正确识别管理员并跳转
    cy.url().should('include', '/admin/dashboard');

    // 步骤3: 在管理员布局中，打开用户菜单并点击退出
    cy.get('[data-cy=admin-user-menu-trigger]').click(); // 点击管理员专用的用户菜单
    cy.get('[data-cy=admin-logout-button]').click();   // 点击管理员专用的登出按钮

    // 步骤4: 验证是否已成功登出并跳转到登录页
    // 登出后，应用应跳转回登录页面
    cy.url().should('include', '/login');
    // 验证登录页面的H2标题"用户登录"是否存在，证明页面加载正确
    cy.contains('h2', '用户登录').should('be.visible');
  });

}); 