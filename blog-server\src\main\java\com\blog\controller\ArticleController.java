package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.ArticleDTO;
import com.blog.service.ArticleService;
import com.blog.service.CategoryService;
import com.blog.service.TagService;
import com.blog.vo.ArticleVO;
import com.blog.vo.CategoryVO;
import com.blog.vo.TagVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 文章控制器
 */
@RestController
@RequestMapping("/articles")
@Api(tags = "文章管理接口")
public class ArticleController {

    @Autowired
    private ArticleService articleService;

    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private TagService tagService;

    /**
     * 创建文章
     * @param articleDTO 文章数据
     * @return 文章ID
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "创建文章", notes = "需要登录")
    public Result<Long> createArticle(@Validated @RequestBody ArticleDTO articleDTO) {
        Long articleId = articleService.createArticle(articleDTO);
        return Result.success(articleId);
    }

    /**
     * 更新文章
     * @param id 文章ID
     * @param articleDTO 文章数据
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "更新文章", notes = "需要登录，且只能更新自己的文章，管理员可更新所有文章")
    public Result<Boolean> updateArticle(@PathVariable Long id, @Validated @RequestBody ArticleDTO articleDTO) {
        articleDTO.setId(id);
        boolean result = articleService.updateArticle(articleDTO);
        return Result.success(result);
    }

    /**
     * 删除文章
     * @param id 文章ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "删除文章", notes = "需要登录，且只能删除自己的文章，管理员可删除所有文章")
    public Result<Boolean> deleteArticle(@PathVariable Long id) {
        boolean result = articleService.deleteArticle(id);
        return Result.success(result);
    }

    /**
     * 获取文章详情
     * @param id 文章ID
     * @return 文章详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取文章详情")
    public Result<ArticleVO> getArticleDetail(@PathVariable Long id) {
        ArticleVO articleVO = articleService.getArticleDetail(id);
        return Result.success(articleVO);
    }

    /**
     * 分页获取文章列表
     * @param current 当前页码
     * @param size 每页大小
     * @param keyword 搜索关键字，可选
     * @param categoryId 分类ID，可选
     * @param tagId 标签ID，可选
     * @param status 状态，可选
     * @return 文章分页列表
     */
    @GetMapping
    @ApiOperation(value = "分页获取文章列表")
    public Result<IPage<ArticleVO>> getArticleList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long tagId,
            @RequestParam(required = false) Integer status) {
        // 参数验证，防止NaN值
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        IPage<ArticleVO> page = articleService.getArticleList(current, size, keyword, categoryId, tagId, status);
        return Result.success(page);
    }

    /**
     * 获取当前用户的文章列表
     * @param current 当前页码
     * @param size 每页大小
     * @return 文章分页列表
     */
    @GetMapping("/my")
    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "获取当前用户的文章列表", notes = "需要登录")
    public Result<IPage<ArticleVO>> getMyArticleList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String username = userDetails.getUsername();
        IPage<ArticleVO> page = articleService.getArticleListByUsername(current, size, username, null);
        return Result.success(page);
    }

    /**
     * 获取推荐文章列表
     * @param limit 限制数量
     * @return 推荐文章列表
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "获取推荐文章列表")
    public Result<List<ArticleVO>> getRecommendArticleList(
            @RequestParam(defaultValue = "5") Integer limit) {
        List<ArticleVO> recommendList = articleService.getRecommendArticleList(limit);
        return Result.success(recommendList);
    }

    /**
     * 获取热门文章列表
     * @param limit 限制数量
     * @return 热门文章列表
     */
    @GetMapping("/popular")
    @ApiOperation(value = "获取热门文章列表")
    public Result<List<ArticleVO>> getPopularArticleList(
            @RequestParam(defaultValue = "5") Integer limit) {
        List<ArticleVO> popularList = articleService.getPopularArticleList(limit);
        return Result.success(popularList);
    }

    /**
     * 获取文章归档数据
     * @return 归档数据
     */
    @GetMapping("/archive")
    @ApiOperation(value = "获取文章归档数据")
    public Result<Map<String, Map<String, List<ArticleVO>>>> getArchiveData() {
        Map<String, Map<String, List<ArticleVO>>> archiveData = articleService.getArchiveData();
        return Result.success(archiveData);
    }
}