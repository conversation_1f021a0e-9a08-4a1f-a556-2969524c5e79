package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.UserFollow;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户关注Mapper接口
 */
public interface UserFollowMapper extends BaseMapper<UserFollow> {
    
    /**
     * 查询用户是否已关注
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 关注记录数
     */
    @Select("SELECT COUNT(*) FROM user_follow WHERE follower_id = #{followerId} AND followed_id = #{followedId}")
    int selectIsFollowing(@Param("followerId") Long followerId, @Param("followedId") Long followedId);
    
    /**
     * 查询用户关注数量
     * @param userId 用户ID
     * @return 关注数量
     */
    @Select("SELECT COUNT(*) FROM user_follow WHERE follower_id = #{userId}")
    int selectFollowingCount(@Param("userId") Long userId);
    
    /**
     * 查询用户粉丝数量
     * @param userId 用户ID
     * @return 粉丝数量
     */
    @Select("SELECT COUNT(*) FROM user_follow WHERE followed_id = #{userId}")
    int selectFollowerCount(@Param("userId") Long userId);
    
    /**
     * 查询用户关注的用户ID列表
     * @param userId 用户ID
     * @return 关注的用户ID列表
     */
    @Select("SELECT followed_id FROM user_follow WHERE follower_id = #{userId}")
    List<Long> selectFollowingIds(@Param("userId") Long userId);
    
    /**
     * 查询用户的粉丝ID列表
     * @param userId 用户ID
     * @return 粉丝ID列表
     */
    @Select("SELECT follower_id FROM user_follow WHERE followed_id = #{userId}")
    List<Long> selectFollowerIds(@Param("userId") Long userId);
} 