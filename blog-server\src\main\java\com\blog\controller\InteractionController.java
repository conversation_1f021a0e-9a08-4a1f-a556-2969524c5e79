package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.ArticleInteractionDTO;
import com.blog.service.CollectionService;
import com.blog.service.LikeService;
import com.blog.service.UserService;
import com.blog.vo.ArticleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户互动控制器
 */
@RestController
@RequestMapping("/interaction")
public class InteractionController {
    
    @Autowired
    private LikeService likeService;
    
    @Autowired
    private CollectionService collectionService;
    
    @Resource
    private UserService userService;
    
    /**
     * 添加互动（点赞/收藏）
     */
    @PostMapping("/add")
    @PreAuthorize("isAuthenticated()")
    public Result<?> addInteraction(@RequestBody ArticleInteractionDTO dto) {
        Long userId = getCurrentUserId();
        
        // 根据互动类型执行对应操作
        boolean success = false;
        if ("like".equals(dto.getType())) {
            success = likeService.likeArticle(dto.getArticleId(), userId);
        } else if ("collect".equals(dto.getType())) {
            success = collectionService.collectArticle(dto.getArticleId(), userId);
        } else {
            return Result.failed("不支持的互动类型");
        }
        
        if (success) {
            return Result.success("操作成功");
        } else {
            return Result.failed("操作失败");
        }
    }
    
    /**
     * 取消互动（取消点赞/取消收藏）
     */
    @PostMapping("/cancel")
    @PreAuthorize("isAuthenticated()")
    public Result<?> cancelInteraction(@RequestBody ArticleInteractionDTO dto) {
        Long userId = getCurrentUserId();
        
        // 根据互动类型执行对应操作
        boolean success = false;
        if ("like".equals(dto.getType())) {
            success = likeService.unlikeArticle(dto.getArticleId(), userId);
        } else if ("collect".equals(dto.getType())) {
            success = collectionService.uncollectArticle(dto.getArticleId(), userId);
        } else {
            return Result.failed("不支持的互动类型");
        }
        
        if (success) {
            return Result.success("操作成功");
        } else {
            return Result.failed("操作失败");
        }
    }
    
    /**
     * 获取文章互动状态
     */
    @GetMapping("/status/{articleId}")
    public Result<?> getInteractionStatus(@PathVariable Long articleId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取点赞和收藏总数（不论是否登录都需要获取）
        int likeCount = likeService.getArticleLikeCount(articleId);
        int collectCount = collectionService.getArticleCollectionCount(articleId);
        
        // 默认未登录状态
        boolean isLiked = false;
        boolean isCollected = false;
        
        // 如果用户已登录，查询互动状态
        Long userId = getCurrentUserId();
        if (userId != null) {
            isLiked = likeService.hasLikedArticle(articleId, userId);
            isCollected = collectionService.hasCollectedArticle(articleId, userId);
        }
        
        result.put("isLiked", isLiked);
        result.put("isCollected", isCollected);
        result.put("likeCount", likeCount);
        result.put("collectCount", collectCount);
        
        return Result.success(result);
    }
    
    /**
     * 获取用户收藏列表
     */
    @GetMapping("/collections")
    @PreAuthorize("isAuthenticated()")
    public Result<?> getUserCollections() {
        Long userId = getCurrentUserId();
        List<ArticleVO> collections = collectionService.getUserCollections(userId);
        return Result.success(collections);
    }
    
    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        String username = authentication.getName();
        if ("anonymousUser".equals(username)) {
            return null;
        }
        return userService.getUserByUsername(username).getId();
    }
} 