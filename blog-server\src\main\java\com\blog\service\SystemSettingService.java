package com.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.entity.SystemSetting;

import java.util.List;
import java.util.Map;

/**
 * 系统设置服务接口
 */
public interface SystemSettingService extends IService<SystemSetting> {

    /**
     * 根据设置键名获取设置值
     * @param settingKey 设置键名
     * @return 设置值
     */
    String getValueByKey(String settingKey);

    /**
     * 根据设置键名获取设置值，如果不存在则返回默认值
     * @param settingKey 设置键名
     * @param defaultValue 默认值
     * @return 设置值
     */
    String getValueByKey(String settingKey, String defaultValue);

    /**
     * 设置配置项
     * @param settingKey 设置键名
     * @param settingValue 设置值
     * @param description 描述
     * @param settingGroup 分组
     * @return 是否成功
     */
    boolean setSetting(String settingKey, String settingValue, String description, String settingGroup);

    /**
     * 批量设置配置项
     * @param settings 设置Map
     * @param settingGroup 分组
     * @return 是否成功
     */
    boolean batchSetSettings(Map<String, String> settings, String settingGroup);

    /**
     * 根据分组获取设置列表
     * @param settingGroup 分组
     * @return 设置列表
     */
    List<SystemSetting> getSettingsByGroup(String settingGroup);

    /**
     * 获取所有设置的Map形式
     * @return 设置Map
     */
    Map<String, String> getAllSettingsMap();
}
