/**
 * 测试套件：分类管理功能
 * 描述：覆盖管理员对分类进行增删改查的核心操作
 */
describe('分类管理功能', () => {
  let authToken;
  
  // 在所有测试开始前获取认证Token
  before(() => {
    // 直接使用API登录获取token
    cy.request({
      method: 'POST',
      url: '/api/auth/login',
      body: {
        username: 'admin',
        password: 'admin123'
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      cy.log('Login Response:', JSON.stringify(response.body));
      
      // 从登录响应中正确提取Token信息
      const { token, tokenHead } = response.body.data;
      expect(token).to.exist;

      // 组装成标准的Authorization请求头，并存储以备后用
      authToken = `${tokenHead} ${token}`;
      
      // 将token存储到localStorage中
      window.localStorage.setItem('blog_token', token);
      
      // 存储用户信息
      const userInfo = {
        userId: response.body.data.userId,
        username: response.body.data.username,
        nickname: response.body.data.nickname,
        role: response.body.data.role
      };
      window.localStorage.setItem('blog_user_info', JSON.stringify(userInfo));
    });
  });

  // 测试分类列表API是否正常工作
  it('应能通过API获取分类列表', () => {
    cy.request({
      method: 'GET',
      url: '/api/categories',
      headers: {
        'Authorization': authToken
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      expect(response.body.code).to.eq(200);
      expect(response.body.data).to.be.an('array');
    });
  });

  // 测试创建分类API是否正常工作
  it('应能通过API创建分类', () => {
    const categoryName = `测试分类-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/categories',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: categoryName,
        order: 1
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      cy.log('Category Response:', JSON.stringify(response.body));
      
      // 验证返回的数据
      expect(response.body.code).to.eq(200);
      expect(response.body.data).to.exist;
      
      const category = response.body.data;
      
      // 验证分类对象
      expect(category).to.exist;
      expect(category).to.have.property('id');
      expect(category.id).to.be.a('number');
      expect(category.name).to.eq(categoryName);
    });
  });

  // 测试更新分类API是否正常工作
  it('应能通过API更新分类', () => {
    // 先创建一个分类
    const originalName = `原分类-${Date.now()}`;
    const updatedName = `更新分类-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/categories',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: originalName,
        order: 1
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      const categoryId = response.body.data.id;
      
      // 然后更新这个分类
      cy.request({
        method: 'PUT',
        url: `/api/categories/${categoryId}`,
        headers: {
          'Authorization': authToken
        },
        body: {
          name: updatedName,
          order: 1
        }
      }).then(updateResponse => {
        expect(updateResponse.status).to.eq(200);
        expect(updateResponse.body.code).to.eq(200);
        expect(updateResponse.body.data).to.be.true;
      });
    });
  });

  // 测试删除分类API是否正常工作
  it('应能通过API删除分类', () => {
    // 先创建一个分类
    const categoryName = `待删除分类-${Date.now()}`;
    
    cy.request({
      method: 'POST',
      url: '/api/categories',
      headers: {
        'Authorization': authToken
      },
      body: {
        name: categoryName,
        order: 1
      }
    }).then(response => {
      expect(response.status).to.eq(200);
      const categoryId = response.body.data.id;
      
      // 然后删除这个分类
      cy.request({
        method: 'DELETE',
        url: `/api/categories/${categoryId}`,
        headers: {
          'Authorization': authToken
        }
      }).then(deleteResponse => {
        expect(deleteResponse.status).to.eq(200);
        expect(deleteResponse.body.code).to.eq(200);
        expect(deleteResponse.body.data).to.be.true;
      });
    });
  });
});