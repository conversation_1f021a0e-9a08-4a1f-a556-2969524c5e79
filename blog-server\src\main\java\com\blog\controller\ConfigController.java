package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.service.ConfigService;
import com.blog.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统配置控制器
 */
@RestController
@RequestMapping("/config")
@Api(tags = "系统配置管理接口")
public class ConfigController {

    @Autowired
    private ConfigService configService;

    @Autowired
    private EmailService emailService;

    /**
     * 获取所有配置
     */
    @GetMapping
    @ApiOperation("获取所有配置")
    public Result<Map<String, String>> getAllConfigs() {
        Map<String, String> configs = configService.getAllConfigs();
        return Result.success(configs);
    }

    /**
     * 获取网站基本信息配置
     */
    @GetMapping("/site")
    @ApiOperation("获取网站基本信息配置")
    public Result<Map<String, String>> getSiteInfo() {
        Map<String, String> siteInfo = configService.getSiteInfo();
        return Result.success(siteInfo);
    }

    /**
     * 获取网站标题
     */
    @GetMapping("/site_title")
    @ApiOperation("获取网站标题")
    public Result<String> getSiteTitle() {
        String siteTitle = configService.getValueByKey("site_title");
        return Result.success(siteTitle != null ? siteTitle : "个人动态博客");
    }

    /**
     * 获取评论相关配置
     */
    @GetMapping("/comment")
    @ApiOperation("获取评论相关配置")
    public Result<Map<String, String>> getCommentConfig() {
        Map<String, String> commentConfig = configService.getCommentConfig();
        return Result.success(commentConfig);
    }

    /**
     * 获取邮件相关配置
     */
    @GetMapping("/email")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("获取邮件相关配置")
    public Result<Map<String, String>> getEmailConfig() {
        Map<String, String> emailConfig = configService.getEmailConfig();
        return Result.success(emailConfig);
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/{key}")
    @ApiOperation("根据配置键获取配置值")
    public Result<String> getConfigByKey(@PathVariable String key) {
        String value = configService.getValueByKey(key);
        return Result.success(value);
    }

    /**
     * 更新单个配置
     */
    @PutMapping("/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("更新单个配置")
    public Result<Boolean> updateConfig(@PathVariable String key, @RequestBody String value) {
        boolean success = configService.updateValueByKey(key, value);
        return Result.success(success, success ? "配置更新成功" : "配置更新失败");
    }

    /**
     * 批量更新配置
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("批量更新配置")
    public Result<Boolean> batchUpdateConfigs(@RequestBody Map<String, String> configs) {
        boolean success = configService.batchUpdateConfigs(configs);
        return Result.success(success, success ? "配置更新成功" : "配置更新失败");
    }

    /**
     * 更新网站基本信息配置
     */
    @PutMapping("/site")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("更新网站基本信息配置")
    public Result<Boolean> updateSiteInfo(@RequestBody Map<String, String> siteInfo) {
        boolean success = configService.batchUpdateConfigs(siteInfo);
        return Result.success(success, success ? "网站信息更新成功" : "网站信息更新失败");
    }

    /**
     * 更新评论相关配置
     */
    @PutMapping("/comment")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("更新评论相关配置")
    public Result<Boolean> updateCommentConfig(@RequestBody Map<String, String> commentConfig) {
        boolean success = configService.batchUpdateConfigs(commentConfig);
        return Result.success(success, success ? "评论配置更新成功" : "评论配置更新失败");
    }

    /**
     * 更新邮件相关配置
     */
    @PutMapping("/email")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("更新邮件相关配置")
    public Result<Boolean> updateEmailConfig(@RequestBody Map<String, String> emailConfig) {
        boolean success = configService.batchUpdateConfigs(emailConfig);
        return Result.success(success, success ? "邮件配置更新成功" : "邮件配置更新失败");
    }

    /**
     * 测试邮件配置
     */
    @PostMapping("/email/test")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation("测试邮件配置")
    public Result<String> testEmailConfig(@RequestBody Map<String, String> emailConfig) {
        try {
            // 验证邮件配置参数
            String smtpServer = emailConfig.get("smtp_server");
            String smtpPort = emailConfig.get("smtp_port");
            String fromEmail = emailConfig.get("from_email");
            String username = emailConfig.get("email_username");
            String password = emailConfig.get("email_password");

            if (smtpServer == null || smtpServer.trim().isEmpty()) {
                return Result.failed("SMTP服务器不能为空");
            }
            if (fromEmail == null || fromEmail.trim().isEmpty()) {
                return Result.failed("发件人邮箱不能为空");
            }
            if (username == null || username.trim().isEmpty()) {
                return Result.failed("邮箱用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.failed("邮箱密码不能为空");
            }

            // 使用EmailService进行真正的邮件发送测试
            String testResult = emailService.testEmailConfig(emailConfig);
            return Result.success(testResult);
        } catch (Exception e) {
            return Result.failed("邮件配置测试失败: " + e.getMessage());
        }
    }
}
