package com.blog.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 邮件模板DTO
 */
@Data
public class EmailTemplateDTO {

    /**
     * 模板ID（更新时需要）
     */
    private Long id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 模板代码
     */
    @NotBlank(message = "模板代码不能为空")
    private String templateCode;

    /**
     * 邮件主题
     */
    @NotBlank(message = "邮件主题不能为空")
    private String subject;

    /**
     * 邮件内容模板
     */
    @NotBlank(message = "邮件内容不能为空")
    private String content;

    /**
     * 模板类型：TEXT/HTML
     */
    @NotBlank(message = "模板类型不能为空")
    private String templateType;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
