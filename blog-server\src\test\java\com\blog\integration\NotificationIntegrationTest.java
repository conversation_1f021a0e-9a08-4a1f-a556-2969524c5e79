package com.blog.integration;

import com.blog.entity.Article;
import com.blog.entity.User;
import com.blog.mapper.ArticleMapper;
import com.blog.mapper.UserMapper;
import com.blog.service.NotificationService;
import com.blog.vo.NotificationVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 通知功能集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("通知功能集成测试")
class NotificationIntegrationTest {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private UserMapper userMapper;

    private User author;
    private User liker;
    private Article article;

    @BeforeEach
    void setUp() {
        // 创建测试用户 - 作者
        author = new User();
        author.setUsername("test_author_" + System.currentTimeMillis());
        author.setNickname("测试作者");
        author.setEmail("<EMAIL>");
        author.setPassword("password");
        author.setRole("user");
        author.setStatus(1);
        userMapper.insert(author);

        // 创建测试用户 - 点赞者
        liker = new User();
        liker.setUsername("test_liker_" + System.currentTimeMillis());
        liker.setNickname("测试点赞者");
        liker.setEmail("<EMAIL>");
        liker.setPassword("password");
        liker.setRole("user");
        liker.setStatus(1);
        userMapper.insert(liker);

        // 创建测试文章
        article = new Article();
        article.setTitle("测试文章_" + System.currentTimeMillis());
        article.setSummary("测试文章摘要");
        article.setContent("测试文章内容");
        article.setAuthorId(author.getId());
        article.setCategoryId(1L);
        article.setStatus(1);
        article.setDelFlag(0);
        article.setViewCount(0);
        article.setLikeCount(0);
        article.setCommentCount(0);
        article.setIsTop(0);
        article.setAllowComment(1);
        articleMapper.insert(article);
    }

    @Test
    @DisplayName("点赞文章应该发送通知")
    void testLikeArticleShouldSendNotification() {
        // 获取发送通知前的通知数量
        List<NotificationVO> notificationsBefore = notificationService.getUserNotifications(
                author.getId(), null, 10);
        int countBefore = notificationsBefore.size();

        // 执行点赞操作
        notificationService.sendLikeNotification(article.getId(), liker.getId());

        // 验证通知是否创建
        List<NotificationVO> notificationsAfter = notificationService.getUserNotifications(
                author.getId(), null, 10);

        assertNotNull(notificationsAfter);
        assertTrue(notificationsAfter.size() > countBefore);

        // 查找新创建的点赞通知
        NotificationVO likeNotification = notificationsAfter.stream()
                .filter(n -> "like".equals(n.getType()) &&
                           liker.getId().equals(n.getFromUserId()) &&
                           article.getId().equals(n.getResourceId()))
                .findFirst()
                .orElse(null);

        assertNotNull(likeNotification);
        assertEquals("like", likeNotification.getType());
        assertEquals(liker.getId(), likeNotification.getFromUserId());
        assertEquals(author.getId(), likeNotification.getUserId());
        assertEquals(article.getId(), likeNotification.getResourceId());
        assertEquals("article", likeNotification.getResourceType());
    }

    @Test
    @DisplayName("获取未读通知数量")
    void testGetUnreadNotificationCount() {
        // 获取发送前的未读数量
        int unreadCountBefore = notificationService.getUnreadCount(author.getId());

        // 发送几个通知
        notificationService.sendLikeNotification(article.getId(), liker.getId());
        notificationService.sendCollectNotification(article.getId(), liker.getId());

        // 获取未读数量
        int unreadCountAfter = notificationService.getUnreadCount(author.getId());

        // 验证未读数量增加了
        assertTrue(unreadCountAfter > unreadCountBefore);
    }

    @Test
    @DisplayName("标记通知为已读")
    void testMarkNotificationAsRead() {
        // 发送通知
        notificationService.sendLikeNotification(article.getId(), liker.getId());

        // 获取所有通知，找到刚发送的通知
        List<NotificationVO> allNotifications = notificationService.getUserNotifications(
                author.getId(), null, 10);

        // 找到刚发送的点赞通知
        NotificationVO targetNotification = allNotifications.stream()
                .filter(n -> "like".equals(n.getType()) &&
                           liker.getId().equals(n.getFromUserId()) &&
                           article.getId().equals(n.getResourceId()))
                .findFirst()
                .orElse(null);

        assertNotNull(targetNotification, "应该找到刚发送的点赞通知");

        // 获取标记前的未读数量
        int unreadCountBefore = notificationService.getUnreadCount(author.getId());

        // 标记为已读
        boolean result = notificationService.markAsRead(author.getId(),
                Collections.singletonList(targetNotification.getId()));

        assertTrue(result);

        // 验证未读数量减少（如果该通知原本是未读的）
        int unreadCountAfter = notificationService.getUnreadCount(author.getId());
        assertTrue(unreadCountAfter <= unreadCountBefore);
    }

    @Test
    @DisplayName("删除通知")
    void testDeleteNotification() {
        // 发送通知
        notificationService.sendLikeNotification(article.getId(), liker.getId());

        // 获取通知列表
        List<NotificationVO> notificationsBefore = notificationService.getUserNotifications(
                author.getId(), null, 10);

        assertFalse(notificationsBefore.isEmpty());

        // 删除通知
        Long notificationId = notificationsBefore.get(0).getId();
        boolean result = notificationService.deleteNotifications(author.getId(), Collections.singletonList(notificationId));

        assertTrue(result);

        // 验证通知已删除
        List<NotificationVO> notificationsAfter = notificationService.getUserNotifications(
                author.getId(), null, 10);

        assertTrue(notificationsAfter.size() < notificationsBefore.size() ||
                  notificationsAfter.stream().noneMatch(n -> n.getId().equals(notificationId)));
    }

    @Test
    @DisplayName("防止重复通知")
    void testPreventDuplicateNotifications() {
        // 获取发送前的通知数量
        List<NotificationVO> notificationsBefore = notificationService.getUserNotifications(
                author.getId(), null, 10);
        long likeNotificationCountBefore = notificationsBefore.stream()
                .filter(n -> "like".equals(n.getType()) &&
                           liker.getId().equals(n.getFromUserId()) &&
                           article.getId().equals(n.getResourceId()))
                .count();

        // 多次发送相同类型的通知
        notificationService.sendLikeNotification(article.getId(), liker.getId());
        notificationService.sendLikeNotification(article.getId(), liker.getId());
        notificationService.sendLikeNotification(article.getId(), liker.getId());

        // 验证通知数量
        List<NotificationVO> notificationsAfter = notificationService.getUserNotifications(
                author.getId(), null, 10);

        long likeNotificationCountAfter = notificationsAfter.stream()
                .filter(n -> "like".equals(n.getType()) &&
                           liker.getId().equals(n.getFromUserId()) &&
                           article.getId().equals(n.getResourceId()))
                .count();

        // 验证只增加了一个通知（防重复机制）
        assertEquals(likeNotificationCountBefore + 1, likeNotificationCountAfter);
    }

    @Test
    @DisplayName("标记所有通知为已读")
    void testMarkAllNotificationsAsRead() {
        // 发送多个通知
        notificationService.sendLikeNotification(article.getId(), liker.getId());
        notificationService.sendCollectNotification(article.getId(), liker.getId());

        // 获取未读数量
        int unreadCountBefore = notificationService.getUnreadCount(author.getId());

        // 标记所有为已读
        boolean result = notificationService.markAllAsRead(author.getId());
        assertTrue(result);

        // 验证未读数量减少或为0
        int unreadCountAfter = notificationService.getUnreadCount(author.getId());
        assertTrue(unreadCountAfter <= unreadCountBefore);

        // 如果之前有未读通知，现在应该为0
        if (unreadCountBefore > 0) {
            assertEquals(0, unreadCountAfter);
        }
    }

    @Test
    @DisplayName("自己操作自己的内容不应该发送通知")
    void testSelfActionShouldNotSendNotification() {
        // 作者给自己的文章点赞
        notificationService.sendLikeNotification(article.getId(), author.getId());

        // 验证没有创建通知
        List<NotificationVO> notifications = notificationService.getUserNotifications(
                author.getId(), null, 10);

        // 应该没有通知，或者通知不是来自自己的
        long selfNotificationCount = notifications.stream()
                .filter(n -> author.getId().equals(n.getFromUserId()))
                .count();

        assertEquals(0, selfNotificationCount);
    }
}
