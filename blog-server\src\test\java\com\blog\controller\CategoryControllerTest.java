package com.blog.controller;

import com.blog.dto.CategoryDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import com.blog.entity.Article;
import com.blog.mapper.ArticleMapper;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 集成测试：CategoryController
 * 描述：测试分类管理相关的API端点
 */
@SpringBootTest // 启动一个完整的Spring应用上下文用于测试
@AutoConfigureMockMvc // 自动配置MockMvc，用于发起模拟HTTP请求
@Transactional // 每个测试方法都在一个事务中运行，测试结束后自动回滚，不污染数据库
public class CategoryControllerTest {

    @Autowired
    private MockMvc mockMvc; // 注入MockMvc实例

    @Autowired
    private ObjectMapper objectMapper; // 注入ObjectMapper，用于将Java对象转换为JSON字符串

    @Autowired
    private ArticleMapper articleMapper; // 注入ArticleMapper，用于直接操作文章数据

    /**
     * 测试用例：获取所有分类列表
     * 场景：当用户（需要认证）请求分类列表时
     * 预期：应返回200 OK状态，且响应体中包含一个JSON数组格式的分类数据
     */
    @Test
    @WithMockUser // 模拟一个已认证的用户，绕过登录认证
    public void shouldGetAllCategories() throws Exception {
        // 执行一个GET请求到/categories
        mockMvc.perform(get("/categories"))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：响应的Content-Type应为application/json
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                // 断言3：响应体JSON中，'code'字段的值应为200
                .andExpect(jsonPath("$.code").value(200))
                // 断言4：响应体JSON中，'data'字段应为一个数组 (表示分类列表)
                .andExpect(jsonPath("$.data").isArray());
    }

    /**
     * 测试用例：管理员应能成功添加一个新分类
     * 场景：当管理员发送一个包含新分类信息的POST请求时
     * 预期：应返回200 OK状态，响应体中包含新创建分类的ID
     */
    @Test
    @WithMockUser(roles = "ADMIN") // 模拟一个角色为'ADMIN'的用户
    public void shouldAddCategoryAsAdmin() throws Exception {
        // 准备一个待添加的分类DTO对象
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("TDD测试分类");
        newCategory.setDescription("这是一个通过TDD测试添加的分类");
        newCategory.setOrder(100);

        // 执行POST请求，并将DTO对象序列化为JSON字符串作为请求体
        mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newCategory)))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：响应体JSON中，'code'字段的值应为200
                .andExpect(jsonPath("$.code").value(200))
                // 断言3：响应体JSON中，'data'字段应为一个对象，包含id属性
                .andExpect(jsonPath("$.data").isMap())
                .andExpect(jsonPath("$.data.id").isNumber())
                .andExpect(jsonPath("$.data.name").value("TDD测试分类"));
    }

    /**
     * 测试用例：非管理员用户应被禁止添加分类
     * 场景：当一个没有管理员角色的普通用户尝试发送POST请求添加分类时
     * 预期：应返回403 Forbidden状态，拒绝该次操作
     */
    @Test
    @WithMockUser(roles = "USER") // 模拟一个角色为'USER'的普通用户
    public void shouldForbidAddCategoryForNonAdmin() throws Exception {
        // 准备一个待添加的分类DTO对象
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("违规测试分类");
        newCategory.setDescription("这是一个普通用户尝试添加的分类");
        newCategory.setOrder(999);

        // 执行POST请求
        mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newCategory)))
                // 断言：HTTP状态码应为403 (Forbidden)，因为用户权限不足
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：管理员应能成功更新一个分类
     * 场景：管理员提供一个有效的分类ID和更新信息
     * 预期：应返回200 OK状态，并且分类信息得到更新
     */
    @Test
    @WithMockUser(roles = "ADMIN") // 模拟一个角色为'ADMIN'的用户
    public void shouldUpdateCategoryAsAdmin() throws Exception {
        // 步骤 1: 创建一个分类用于更新
        // 为了让测试独立，我们首先通过API创建一个新的分类，并获取其ID
        CategoryDTO originalCategory = new CategoryDTO();
        originalCategory.setName("原始分类名");
        originalCategory.setDescription("这是一个即将被更新的分类");
        originalCategory.setOrder(50);

        // 发送POST请求创建分类，并获取返回的响应
        String createResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(originalCategory)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // 从JSON响应中解析出新创建分类的ID，并确保其为Long类型
        Long categoryId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data.id")).longValue();

        // 步骤 2: 准备更新数据并执行PUT请求
        CategoryDTO updatedInfo = new CategoryDTO();
        updatedInfo.setName("更新后的分类名");
        updatedInfo.setDescription("分类描述已被成功更新");
        updatedInfo.setOrder(55);

        // 假设更新操作的端点是 PUT /categories/{id}
        mockMvc.perform(put("/categories/" + categoryId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：业务成功码应为200
                .andExpect(jsonPath("$.code").value(200));

        // 步骤 3: （验证）发送GET请求，确认分类信息已被修改
        // 假设获取单个分类的端点是 GET /categories/{id}
        // 这一步能更可靠地验证更新操作的持久化效果
        mockMvc.perform(get("/categories/" + categoryId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.name").value("更新后的分类名"))
                .andExpect(jsonPath("$.data.description").value("分类描述已被成功更新"))
                .andExpect(jsonPath("$.data.order").value(55));
    }

    /**
     * 测试用例：非管理员用户应被禁止更新分类
     * 场景：一个没有管理员角色的普通用户尝试发送PUT请求更新分类时
     * 预期：应返回403 Forbidden状态，拒绝该次操作
     */
    @Test
    @WithMockUser(roles = "ADMIN") // 默认使用ADMIN角色来创建测试数据
    public void shouldForbidUpdateCategoryForNonAdmin() throws Exception {
        // 步骤 1: (作为admin) 先创建一个分类，获取其ID
        CategoryDTO originalCategory = new CategoryDTO();
        originalCategory.setName("分类禁止更新测试");
        originalCategory.setDescription("这是一个用于测试权限的分类");
        originalCategory.setOrder(99);

        String createResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(originalCategory)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        Long categoryId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data.id")).longValue();

        // 步骤 2: 准备一个符合校验规则的完整更新数据
        CategoryDTO updatedInfo = new CategoryDTO();
        updatedInfo.setName("不应成功的更新");
        updatedInfo.setDescription("一个普通用户尝试的更新");
        updatedInfo.setOrder(199);

        // 步骤 3: (切换为普通用户) 尝试执行PUT请求
        mockMvc.perform(put("/categories/" + categoryId)
                        .with(user("normalUser").roles("USER")) // 关键：在本次请求中，将身份切换为普通用户
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                // 断言：HTTP状态码应为403 (Forbidden)，因为用户权限不足
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：尝试更新一个不存在的分类时应返回404
     * 场景：管理员向一个不存在的分类ID发送更新请求
     * 预期：应返回404 Not Found状态
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldReturnNotFoundWhenUpdatingNonExistentCategory() throws Exception {
        // 步骤 1: 定义一个不存在的分类ID
        long nonExistentCategoryId = 99999L;

        // 步骤 2: 准备更新数据
        CategoryDTO updatedInfo = new CategoryDTO();
        updatedInfo.setName("不存在的分类");
        updatedInfo.setDescription("此更新不应成功");
        updatedInfo.setOrder(1);

        // 步骤 3: 执行PUT请求并断言结果
        mockMvc.perform(put("/categories/" + nonExistentCategoryId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedInfo)))
                // 断言：HTTP状态码应为404 (Not Found)
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：管理员应能成功删除一个分类
     * 场景：管理员提供一个有效的、无子分类和关联文章的分类ID
     * 预期：应返回200 OK状态，并且该分类被成功删除
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldDeleteCategoryAsAdmin() throws Exception {
        // 步骤 1: 创建一个用于删除的分类
        CategoryDTO categoryToDelete = new CategoryDTO();
        categoryToDelete.setName("待删除的分类");
        categoryToDelete.setDescription("这是一个用于测试删除功能的分类");
        categoryToDelete.setOrder(200);

        String createResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoryToDelete)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        Long categoryId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data.id")).longValue();

        // 步骤 2: 执行DELETE请求
        mockMvc.perform(delete("/categories/" + categoryId))
                // 断言1：HTTP状态码应为200 (OK)
                .andExpect(status().isOk())
                // 断言2：业务成功码应为200
                .andExpect(jsonPath("$.code").value(200));

        // 步骤 3: (验证) 确认分类已被删除
        // 再次请求该分类，预期应返回404 Not Found
        mockMvc.perform(get("/categories/" + categoryId))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：非管理员用户应被禁止删除分类
     * 场景：一个没有管理员角色的普通用户尝试发送DELETE请求删除分类时
     * 预期：应返回403 Forbidden状态
     */
    @Test
    @WithMockUser(roles = "ADMIN") // 默认使用ADMIN角色来创建测试数据
    public void shouldForbidDeleteCategoryForNonAdmin() throws Exception {
        // 步骤 1: (作为admin) 先创建一个分类，获取其ID
        CategoryDTO categoryToDelete = new CategoryDTO();
        categoryToDelete.setName("分类禁止删除测试");
        categoryToDelete.setDescription("这是一个用于测试权限的分类");
        categoryToDelete.setOrder(201);

        String createResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoryToDelete)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        Long categoryId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data.id")).longValue();

        // 步骤 2: (切换为普通用户) 尝试执行DELETE请求
        mockMvc.perform(delete("/categories/" + categoryId)
                        .with(user("normalUser").roles("USER"))) // 关键：在本次请求中，将身份切换为普通用户
                // 断言：HTTP状态码应为403 (Forbidden)，因为用户权限不足
                .andExpect(status().isForbidden());
    }

    /**
     * 测试用例：尝试删除一个不存在的分类时应返回404
     * 场景：管理员向一个不存在的分类ID发送删除请求
     * 预期：应返回404 Not Found状态
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldReturnNotFoundWhenDeletingNonExistentCategory() throws Exception {
        // 步骤 1: 定义一个不存在的分类ID
        long nonExistentCategoryId = 99999L;

        // 步骤 2: 执行DELETE请求并断言结果
        mockMvc.perform(delete("/categories/" + nonExistentCategoryId))
                // 断言：HTTP状态码应为404 (Not Found)
                .andExpect(status().isNotFound());
    }

    /**
     * 测试用例：应禁止删除含有子分类的分类
     * 场景：管理员尝试删除一个下面挂有子分类的分类
     * 预期：应返回409 Conflict状态，并给出明确的错误信息
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldForbidDeletionOfCategoryWithChildren() throws Exception {
        // 步骤 1: 创建父分类
        CategoryDTO parentCategory = new CategoryDTO();
        parentCategory.setName("父分类");
        parentCategory.setDescription("这是一个父分类");
        parentCategory.setOrder(300);

        String parentResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(parentCategory)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long parentId = ((Number) com.jayway.jsonpath.JsonPath.read(parentResponse, "$.data.id")).longValue();

        // 步骤 2: 创建子分类
        CategoryDTO childCategory = new CategoryDTO();
        childCategory.setName("子分类");
        childCategory.setDescription("这是一个子分类");
        childCategory.setOrder(301);
        childCategory.setParentId(parentId); // 关联到父分类

        mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(childCategory)))
                .andExpect(status().isOk());

        // 步骤 3: 尝试删除父分类，并断言结果
        mockMvc.perform(delete("/categories/" + parentId))
                // 断言1：HTTP状态码应为409 (Conflict)，表示由于资源状态冲突而无法完成
                .andExpect(status().isConflict())
                // 断言2：响应体中应包含具体的业务错误码和信息
                .andExpect(jsonPath("$.code").value(500)) // 假设业务失败码仍为500
                .andExpect(jsonPath("$.message").value("该分类下有子分类，无法删除"));
    }

    /**
     * 测试用例：应禁止删除含有关联文章的分类
     * 场景：管理员尝试删除一个下面挂有文章的分类
     * 预期：应返回409 Conflict状态，并给出明确的错误信息
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    public void shouldForbidDeletionOfCategoryWithArticles() throws Exception {
        // 步骤 1: 创建一个分类
        CategoryDTO categoryWithArticle = new CategoryDTO();
        categoryWithArticle.setName("有关联文章的分类");
        categoryWithArticle.setDescription("此分类不应能被删除");
        categoryWithArticle.setOrder(400);

        String createResponse = mockMvc.perform(post("/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoryWithArticle)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Long categoryId = ((Number) com.jayway.jsonpath.JsonPath.read(createResponse, "$.data.id")).longValue();

        // 步骤 2: 创建一篇关联的文章，并直接插入数据库
        Article article = new Article();
        article.setTitle("测试关联文章");
        article.setContent("这是一篇用于测试分类删除约束的文章");
        article.setAuthorId(1L); // 假设存在ID为1的作者
        article.setCategoryId(categoryId); // 关联到刚刚创建的分类
        article.setStatus(1); // 已发布状态
        articleMapper.insert(article);

        // 步骤 3: 尝试删除该分类，并断言结果
        mockMvc.perform(delete("/categories/" + categoryId))
                // 断言1：HTTP状态码应为409 (Conflict)
                .andExpect(status().isConflict())
                // 断言2：响应体中应包含具体的业务错误码和信息
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("该分类下有关联的文章，无法删除"));
    }
} 