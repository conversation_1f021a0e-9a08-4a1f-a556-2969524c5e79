package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.common.exception.BusinessException;
import com.blog.common.utils.SecurityUtils;
import com.blog.dto.CommentDTO;
import com.blog.service.CommentService;
import com.blog.vo.CommentVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 评论控制器
 */
@RestController
@RequestMapping("/comments")
public class CommentController {

    @Resource
    private CommentService commentService;
    
    @Resource
    private SecurityUtils securityUtils;
    
    /**
     * 创建评论
     *
     * @param commentDTO 评论信息
     * @return 评论ID
     */
    @PostMapping
    public Result<Long> createComment(@Valid @RequestBody CommentDTO commentDTO) {
        // 尝试获取当前用户ID，如果未登录则为null（支持匿名评论）
        Long userId = null;
        try {
            userId = securityUtils.getCurrentUserId();
        } catch (Exception e) {
            // 用户未登录，userId保持为null，支持匿名评论
        }

        Long commentId = commentService.createComment(commentDTO, userId);
        return Result.success(commentId, "评论成功");
    }
    
    /**
     * 删除评论
     * 
     * @param id 评论ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteComment(@PathVariable Long id) {
        Long userId = securityUtils.getCurrentUserId();
        boolean success = commentService.deleteComment(id, userId);
        return Result.success(success, "删除成功");
    }
    
    /**
     * 获取文章评论列表
     * 
     * @param articleId 文章ID
     * @return 评论列表
     */
    @GetMapping("/article/{articleId}")
    public Result<List<CommentVO>> getArticleComments(@PathVariable Long articleId) {
        List<CommentVO> comments = commentService.getArticleComments(articleId);
        return Result.success(comments);
    }
    
    /**
     * 获取用户评论列表
     * 
     * @return 评论列表
     */
    @GetMapping("/user")
    public Result<List<CommentVO>> getCurrentUserComments() {
        Long userId = securityUtils.getCurrentUserId();
        List<CommentVO> comments = commentService.getUserComments(userId);
        return Result.success(comments);
    }
    
    /**
     * 获取指定用户评论列表（管理员权限）
     * 
     * @param userId 用户ID
     * @return 评论列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<List<CommentVO>> getUserComments(@PathVariable Long userId) {
        List<CommentVO> comments = commentService.getUserComments(userId);
        return Result.success(comments);
    }
    
    /**
     * 获取所有评论列表（管理员权限）
     * 
     * @return 所有评论列表
     */
    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<List<CommentVO>> getAllComments() {
        List<CommentVO> comments = commentService.getAllComments();
        return Result.success(comments);
    }
    
    /**
     * 更新评论状态（审核/取消审核）（管理员权限）
     * 
     * @param id 评论ID
     * @param status 状态（0-未审核，1-已发布）
     * @return 更新结果
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Boolean> updateCommentStatus(@PathVariable Long id, @RequestParam Integer status) {
        if (status != 0 && status != 1) {
            throw new BusinessException("状态值只能为0或1");
        }
        boolean success = commentService.updateCommentStatus(id, status);
        return Result.success(success, "状态更新成功");
    }
} 