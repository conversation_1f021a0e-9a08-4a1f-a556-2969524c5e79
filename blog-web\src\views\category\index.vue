<template>
  <div class="category-page">
    <h1>分类: {{ categoryName }}</h1>
    
    <div class="article-list">
      <el-empty v-if="articles.length === 0" description="暂无文章"></el-empty>
      <div v-else>
        <div v-for="article in articles" :key="article.id" class="article-item">
          <h2 class="article-title">
            <router-link :to="`/article/${article.id}`">{{ article.title }}</router-link>
          </h2>
          <div class="article-info">
            <span>作者: {{ article.author }}</span>
            <span>发布时间: {{ article.createTime }}</span>
            <span>分类: {{ article.categoryName }}</span>
            <span>标签: {{ article.tags.join(', ') }}</span>
          </div>
          <div class="article-summary">{{ article.summary }}</div>
          <div class="article-footer">
            <router-link :to="`/article/${article.id}`">阅读全文</router-link>
          </div>
        </div>
      </div>
      
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          layout="prev, pager, next"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'CategoryPage',
  setup() {
    const route = useRoute()
    const loading = ref(false)
    const categoryName = ref('')
    
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    
    const articles = ref([])
    
    const fetchCategoryInfo = async (id) => {
      try {
        // 获取分类信息
        // 模拟数据
        categoryName.value = '分类名称'
      } catch (error) {
        console.error('获取分类信息失败', error)
      }
    }
    
    const fetchArticles = async (id) => {
      loading.value = true
      try {
        // 获取分类下的文章列表
        // 模拟数据
        articles.value = []
        pagination.total = 0
      } catch (error) {
        console.error('获取文章列表失败', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleCurrentChange = (page) => {
      pagination.currentPage = page
      fetchArticles(route.params.id)
    }
    
    watch(() => route.params.id, (newId) => {
      if (newId) {
        pagination.currentPage = 1
        fetchCategoryInfo(newId)
        fetchArticles(newId)
      }
    })
    
    onMounted(() => {
      const id = route.params.id
      if (id) {
        fetchCategoryInfo(id)
        fetchArticles(id)
      }
    })
    
    return {
      loading,
      categoryName,
      pagination,
      articles,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.category-page {
  padding: 20px;
}

.article-list {
  margin-top: 20px;
}

.article-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.article-title {
  margin-bottom: 10px;
}

.article-title a {
  color: #333;
  text-decoration: none;
}

.article-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
}

.article-summary {
  margin-bottom: 15px;
  line-height: 1.6;
}

.article-footer a {
  color: #409eff;
  text-decoration: none;
}

.pagination {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
</style> 