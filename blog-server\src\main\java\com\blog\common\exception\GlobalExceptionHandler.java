package com.blog.common.exception;

import com.blog.common.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.http.HttpStatus;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理 Spring Security 的权限不足异常
     * <p>
     * 将其重新抛出，以便 Spring Security 的 Filter-Chain 能捕获并进行处理
     *
     * @param e 权限不足异常
     * @throws AccessDeniedException
     */
    @ExceptionHandler(value = AccessDeniedException.class)
    public void accessDeniedExceptionHandler(AccessDeniedException e) throws AccessDeniedException {
        // 抛出此异常，让 Spring Security 的统一异常处理器处理
        log.warn("捕获到 AccessDeniedException，重新抛出以便 Security Filter Chain 处理: {}", e.getMessage());
        throw e;
    }

    /**
     * 处理验证异常
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result<?> handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        return Result.validateFailed(message);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(value = BindException.class)
    public Result<?> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        return Result.validateFailed(message);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public Result<?> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = "";
        if (!violations.isEmpty()) {
            message = violations.iterator().next().getMessage();
        }
        return Result.validateFailed(message);
    }

    /**
     * 处理资源未找到异常
     * @param e 资源未找到异常
     * @return 封装的返回结果 (HTTP 404)
     */
    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<?> notFoundExceptionHandler(NotFoundException e) {
        log.warn("资源未找到(NotFoundException): {}", e.getMessage());
        return Result.failed(HttpStatus.NOT_FOUND.value(), e.getMessage());
    }

    /**
     * 处理资源冲突异常
     * @param e 资源冲突异常
     * @return 封装的返回结果 (HTTP 409)
     */
    @ExceptionHandler(ConflictException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public Result<?> conflictExceptionHandler(ConflictException e) {
        log.warn("业务冲突(ConflictException): {}", e.getMessage());
        return Result.failed(HttpStatus.CONFLICT.value(), e.getMessage());
    }

    /**
     * 处理自定义的业务异常
     * @param e 业务异常
     * @return 封装的返回结果
     */
    @ExceptionHandler(value = BusinessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> businessExceptionHandler(BusinessException e) {
        log.error("业务异常(BusinessException): {}", e.getMessage(), e);
        return Result.failed(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
    }

    /**
     * 处理其他所有未捕获的异常
     * @param e 异常
     * @return 封装的返回结果
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> exceptionHandler(Exception e) {
        log.error("未知异常(Exception): {}", e.getMessage(), e);
        return Result.failed(HttpStatus.INTERNAL_SERVER_ERROR.value(), "系统异常，请联系管理员");
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("参数不合法(IllegalArgumentException): {}", e.getMessage());
        return Result.failed(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }

    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBadRequestException(BadRequestException e) {
        log.warn("请求参数错误(BadRequestException): {}", e.getMessage());
        return Result.failed(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }

    /**
     * 处理参数类型转换异常
     * @param e 参数类型转换异常
     * @return 封装的返回结果
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        String paramName = e.getName();
        String paramValue = String.valueOf(e.getValue());
        String requiredType = e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "未知类型";

        log.warn("参数类型转换异常(MethodArgumentTypeMismatchException): 参数名={}, 参数值={}, 期望类型={}",
                paramName, paramValue, requiredType);

        String message = String.format("参数 '%s' 的值 '%s' 无法转换为 %s 类型", paramName, paramValue, requiredType);
        return Result.failed(HttpStatus.BAD_REQUEST.value(), message);
    }
}