package com.blog.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知视图对象
 */
@Data
public class NotificationVO {

    /**
     * 通知ID
     */
    private Long id;

    /**
     * 接收通知的用户ID
     */
    private Long userId;

    /**
     * 发送通知的用户ID
     */
    private Long fromUserId;

    /**
     * 发送者用户名
     */
    private String fromUsername;

    /**
     * 发送者昵称
     */
    private String fromNickname;

    /**
     * 发送者头像
     */
    private String fromAvatar;

    /**
     * 通知类型
     */
    private String type;

    /**
     * 通知类型描述
     */
    private String typeDescription;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 关联的资源ID
     */
    private Long resourceId;

    /**
     * 关联的资源类型
     */
    private String resourceType;

    /**
     * 关联的资源标题（如文章标题）
     */
    private String resourceTitle;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
