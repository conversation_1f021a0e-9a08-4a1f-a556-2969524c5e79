<template>
  <div class="user-profile">
    <div class="profile-header">
      <h1>个人资料</h1>
      <el-button type="primary" @click="showEditDialog = true">
        <el-icon><Edit /></el-icon>
        编辑资料
      </el-button>
    </div>

    <div class="profile-content">
      <!-- 用户基本信息卡片 -->
      <el-card class="profile-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <div class="user-info-section">
          <div class="avatar-section">
            <el-avatar :size="120" :src="userInfo.avatar" class="user-avatar">
              {{ userInfo.nickname?.charAt(0) }}
            </el-avatar>
            <el-button type="text" @click="showAvatarDialog = true" class="change-avatar-btn">
              更换头像
            </el-button>
          </div>

          <div class="info-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">
                {{ userInfo.username }}
              </el-descriptions-item>
              <el-descriptions-item label="昵称">
                {{ userInfo.nickname }}
              </el-descriptions-item>
              <el-descriptions-item label="邮箱">
                {{ userInfo.email }}
              </el-descriptions-item>
              <el-descriptions-item label="角色">
                <el-tag :type="userInfo.role === 'admin' ? 'danger' : 'primary'">
                  {{ userInfo.role === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="注册时间">
                {{ formatDateTime(userInfo.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后登录">
                {{ formatDateTime(userInfo.lastLoginTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 统计信息卡片 -->
      <el-card class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>我的统计</span>
          </div>
        </template>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ userStats.articleCount }}</div>
            <div class="stat-label">发布文章</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStats.commentCount }}</div>
            <div class="stat-label">评论数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStats.likeCount }}</div>
            <div class="stat-label">获得点赞</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStats.collectionCount }}</div>
            <div class="stat-label">获得收藏</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStats.followingCount }}</div>
            <div class="stat-label">关注数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStats.followerCount }}</div>
            <div class="stat-label">粉丝数</div>
          </div>
        </div>
      </el-card>

      <!-- 快捷操作卡片 -->
      <el-card class="actions-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>快捷操作</span>
          </div>
        </template>

        <div class="action-buttons">
          <el-button type="primary" @click="$router.push('/user/notifications')">
            <el-icon><Bell /></el-icon>
            消息中心
            <el-badge v-if="unreadCount > 0" :value="unreadCount" :max="99" class="action-badge" />
          </el-button>
          <el-button type="success" @click="$router.push('/user/collection')">
            <el-icon><Star /></el-icon>
            我的收藏
          </el-button>
          <el-button type="info" @click="$router.push('/user/comment')">
            <el-icon><ChatDotRound /></el-icon>
            我的评论
          </el-button>
          <el-button type="warning" @click="$router.push('/user/follow')">
            <el-icon><User /></el-icon>
            关注管理
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑个人资料" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="80px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateProfile" :loading="updating">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更换头像对话框 -->
    <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="/api/upload/avatar"
          :headers="uploadHeaders"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="newAvatar" :src="newAvatar" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAvatarDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateAvatar" :loading="updatingAvatar" :disabled="!newAvatar">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Bell, Star, ChatDotRound, User, Plus } from '@element-plus/icons-vue'
import { getUserInfo, updateUserProfile } from '@/api/user'
import { getUnreadCount } from '@/api/notification'
import { getToken } from '@/utils/auth'

export default {
  name: 'UserProfile',
  components: {
    Edit, Bell, Star, ChatDotRound, User, Plus
  },
  setup() {
    const userInfo = ref({
      username: '',
      nickname: '',
      email: '',
      avatar: '',
      role: '',
      createTime: '',
      lastLoginTime: ''
    })

    const userStats = ref({
      articleCount: 0,
      commentCount: 0,
      likeCount: 0,
      collectionCount: 0,
      followingCount: 0,
      followerCount: 0
    })

    const unreadCount = ref(0)
    const showEditDialog = ref(false)
    const showAvatarDialog = ref(false)
    const updating = ref(false)
    const updatingAvatar = ref(false)
    const newAvatar = ref('')

    const editForm = reactive({
      nickname: '',
      email: ''
    })

    const editRules = {
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ]
    }

    const uploadHeaders = computed(() => ({
      'Authorization': `Bearer ${getToken()}`
    }))

    // 获取用户信息
    const fetchUserInfo = async () => {
      try {
        const res = await getUserInfo()
        if (res.code === 200) {
          userInfo.value = res.data
          // 同时更新编辑表单
          editForm.nickname = res.data.nickname
          editForm.email = res.data.email
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
        ElMessage.error('获取用户信息失败')
      }
    }

    // 获取用户统计信息
    const fetchUserStats = async () => {
      try {
        // 这里应该调用获取用户统计的API
        // 暂时使用模拟数据
        userStats.value = {
          articleCount: 12,
          commentCount: 45,
          likeCount: 128,
          collectionCount: 67,
          followingCount: 23,
          followerCount: 89
        }
      } catch (error) {
        console.error('获取统计信息失败', error)
      }
    }

    // 获取未读通知数量
    const fetchUnreadCount = async () => {
      try {
        const res = await getUnreadCount()
        if (res.code === 200) {
          unreadCount.value = res.data.unreadCount
        }
      } catch (error) {
        console.error('获取未读通知数量失败', error)
      }
    }

    // 更新个人资料
    const handleUpdateProfile = async () => {
      updating.value = true
      try {
        const res = await updateUserProfile(editForm)
        if (res.code === 200) {
          ElMessage.success('更新成功')
          showEditDialog.value = false
          await fetchUserInfo() // 重新获取用户信息
        }
      } catch (error) {
        console.error('更新失败', error)
        ElMessage.error('更新失败')
      } finally {
        updating.value = false
      }
    }

    // 头像上传前验证
    const beforeAvatarUpload = (file) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        ElMessage.error('头像图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('头像图片大小不能超过 2MB!')
        return false
      }
      return true
    }

    // 头像上传成功
    const handleAvatarSuccess = (response) => {
      if (response.code === 200) {
        newAvatar.value = response.data.url
        ElMessage.success('头像上传成功')
      } else {
        ElMessage.error('头像上传失败')
      }
    }

    // 更新头像
    const handleUpdateAvatar = async () => {
      updatingAvatar.value = true
      try {
        const res = await updateUserProfile({ avatar: newAvatar.value })
        if (res.code === 200) {
          ElMessage.success('头像更新成功')
          showAvatarDialog.value = false
          newAvatar.value = ''
          await fetchUserInfo() // 重新获取用户信息
        }
      } catch (error) {
        console.error('头像更新失败', error)
        ElMessage.error('头像更新失败')
      } finally {
        updatingAvatar.value = false
      }
    }

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }

    onMounted(() => {
      fetchUserInfo()
      fetchUserStats()
      fetchUnreadCount()
    })

    return {
      userInfo,
      userStats,
      unreadCount,
      showEditDialog,
      showAvatarDialog,
      updating,
      updatingAvatar,
      newAvatar,
      editForm,
      editRules,
      uploadHeaders,
      handleUpdateProfile,
      beforeAvatarUpload,
      handleAvatarSuccess,
      handleUpdateAvatar,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.user-profile {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.profile-header h1 {
  margin: 0;
  color: #303133;
}

.profile-content {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

/* 基本信息卡片 */
.profile-card {
  margin-bottom: 0;
}

.user-info-section {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  border: 4px solid #f0f2f5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.change-avatar-btn {
  font-size: 12px;
  color: #409eff;
}

.info-section {
  flex: 1;
}

/* 统计信息卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  transition: transform 0.3s;
}

.stat-item:hover {
  transform: translateY(-4px);
}

.stat-item:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-item:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-item:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-item:nth-child(5) {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-item:nth-child(6) {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 快捷操作卡片 */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-buttons .el-button {
  height: 60px;
  font-size: 16px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-badge {
  margin-left: 8px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 头像上传 */
.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar-preview {
  width: 178px;
  height: 178px;
  object-fit: cover;
}

.upload-tips {
  margin-top: 16px;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    padding: 12px;
  }

  .profile-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .user-info-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .action-buttons .el-button {
    height: 50px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>