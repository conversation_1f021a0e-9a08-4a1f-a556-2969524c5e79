<template>
  <div class="comment-manage">
    <div class="header">
      <h1>评论管理</h1>
    </div>
    
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="内容">
          <el-input v-model="searchForm.content" placeholder="请输入评论内容"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="已审核" :value="1"></el-option>
            <el-option label="未审核" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchComments">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table :data="comments" v-loading="loading" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="content" label="评论内容" min-width="200"></el-table-column>
      <el-table-column prop="articleTitle" label="文章标题" width="180"></el-table-column>
      <el-table-column prop="username" label="用户" width="120"></el-table-column>
      <el-table-column prop="createTime" label="评论时间" width="180">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '已审核' : '未审核' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button v-if="scope.row.status === 0" size="small" type="success" @click="approveComment(scope.row)">审核</el-button>
          <el-button size="small" @click="viewArticle(scope.row)">查看文章</el-button>
          <el-button size="small" type="danger" @click="deleteComment(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { formatDateTime } from '@/utils/format';

export default {
  name: 'CommentManage',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    
    const searchForm = reactive({
      content: '',
      status: ''
    })
    
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    
    const comments = ref([])
    
    const fetchComments = async () => {
      loading.value = true
      try {
        // 获取评论列表
        // 模拟数据
        comments.value = []
        pagination.total = 0
      } catch (error) {
        console.error('获取评论列表失败', error)
      } finally {
        loading.value = false
      }
    }
    
    const searchComments = () => {
      pagination.currentPage = 1
      fetchComments()
    }
    
    const resetSearch = () => {
      searchForm.content = ''
      searchForm.status = ''
      searchComments()
    }
    
    const handleSizeChange = (size) => {
      pagination.pageSize = size
      fetchComments()
    }
    
    const handleCurrentChange = (page) => {
      pagination.currentPage = page
      fetchComments()
    }
    
    const approveComment = async (row) => {
      try {
        // 审核评论
        console.log('审核评论', row.id)
        fetchComments()
      } catch (error) {
        console.error('审核评论失败', error)
      }
    }
    
    const viewArticle = (row) => {
      router.push(`/article/${row.articleId}`)
    }
    
    const deleteComment = async (row) => {
      try {
        // 删除评论
        console.log('删除评论', row.id)
        fetchComments()
      } catch (error) {
        console.error('删除评论失败', error)
      }
    }
    
    onMounted(() => {
      fetchComments()
    })
    
    return {
      loading,
      searchForm,
      pagination,
      comments,
      searchComments,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      approveComment,
      viewArticle,
      deleteComment
    }
  }
}
</script>

<style scoped>
.comment-manage {
  padding: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 