package com.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.entity.EmailTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 邮件模板Mapper接口
 */
@Mapper
public interface EmailTemplateMapper extends BaseMapper<EmailTemplate> {

    /**
     * 根据模板代码查询模板
     * @param templateCode 模板代码
     * @return 邮件模板
     */
    EmailTemplate selectByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 检查模板代码是否存在（排除指定ID）
     * @param templateCode 模板代码
     * @param excludeId 排除的ID
     * @return 存在数量
     */
    int countByTemplateCode(@Param("templateCode") String templateCode, @Param("excludeId") Long excludeId);
}
