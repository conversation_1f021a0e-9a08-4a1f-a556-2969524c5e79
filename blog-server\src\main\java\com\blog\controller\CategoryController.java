package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.dto.CategoryDTO;
import com.blog.service.CategoryService;
import com.blog.vo.CategoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@RestController
@RequestMapping("/categories")
@Api(tags = "分类管理接口")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 添加分类
     * @param categoryDTO 分类数据
     * @return 新增分类的ID
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "添加分类", notes = "需要管理员权限")
    public Result<CategoryVO> addCategory(@Validated @RequestBody CategoryDTO categoryDTO) {
        // 直接调用Service层方法，获取完整的VO对象
        CategoryVO categoryVO = categoryService.addCategory(categoryDTO);
        return Result.success(categoryVO);
    }

    /**
     * 更新分类
     * @param id 分类ID
     * @param categoryDTO 分类数据
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "更新分类", notes = "需要管理员权限")
    public Result<Boolean> updateCategory(@PathVariable Long id, @Validated @RequestBody CategoryDTO categoryDTO) {
        categoryDTO.setId(id);
        boolean result = categoryService.updateCategory(categoryDTO);
        return Result.success(result);
    }

    /**
     * 删除分类
     * @param id 分类ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @ApiOperation(value = "删除分类", notes = "需要管理员权限")
    public Result<Boolean> deleteCategory(@PathVariable Long id) {
        boolean result = categoryService.deleteCategory(id);
        return Result.success(result);
    }

    /**
     * 获取分类详情
     * @param id 分类ID
     * @return 分类详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取分类详情")
    public Result<CategoryVO> getCategoryDetail(@PathVariable Long id) {
        CategoryVO categoryVO = categoryService.getCategoryDetail(id);
        return Result.success(categoryVO);
    }

    /**
     * 获取分类列表（平铺结构）
     * @return 分类列表
     */
    @GetMapping
    @ApiOperation(value = "获取分类列表")
    public Result<List<CategoryVO>> getCategoryList() {
        List<CategoryVO> categoryList = categoryService.getCategoryList();
        return Result.success(categoryList);
    }

    /**
     * 获取分类树形结构
     * @return 分类树形结构
     */
    @GetMapping("/tree")
    @ApiOperation(value = "获取分类树形结构")
    public Result<List<CategoryVO>> getCategoryTree() {
        List<CategoryVO> categoryTree = categoryService.getCategoryTree();
        return Result.success(categoryTree);
    }
} 