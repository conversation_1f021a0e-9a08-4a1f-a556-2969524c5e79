package com.blog.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邮件模板VO
 */
@Data
public class EmailTemplateVO {

    /**
     * 模板ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板代码
     */
    private String templateCode;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件内容模板
     */
    private String content;

    /**
     * 模板类型：TEXT/HTML
     */
    private String templateType;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
