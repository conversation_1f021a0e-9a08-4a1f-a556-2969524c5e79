package com.blog.config;

import org.springframework.stereotype.Component;

/**
 * 邮件模板配置类
 */
@Component
public class EmailTemplateConfig {

    /**
     * 获取测试邮件模板
     */
    public String getTestEmailTemplate(String smtpServer, String port) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>博客系统邮件配置测试</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>" +
                "<h1 style='color: white; margin: 0; font-size: 28px;'>🎉 邮件配置测试成功！</h1>" +
                "</div>" +
                "<div style='background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>" +
                "<p style='font-size: 16px; margin-bottom: 20px;'>恭喜您！邮件配置已经成功完成。</p>" +
                "<div style='background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>" +
                "<h3 style='color: #28a745; margin-top: 0;'>📧 配置信息</h3>" +
                "<p><strong>SMTP服务器：</strong>" + smtpServer + "</p>" +
                "<p><strong>端口：</strong>" + port + "</p>" +
                "<p><strong>测试时间：</strong>" + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "</p>" +
                "</div>" +
                "<p style='color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;'>" +
                "此邮件由个人动态博客系统自动发送，请勿回复。<br>" +
                "如有问题，请联系系统管理员。" +
                "</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    /**
     * 获取评论通知邮件模板
     */
    public String getCommentNotificationTemplate(String userName, String articleTitle, String commentContent, String articleUrl) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>新评论通知</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<div style='background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>" +
                "<h1 style='color: white; margin: 0; font-size: 24px;'>💬 您收到了新评论</h1>" +
                "</div>" +
                "<div style='background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>" +
                "<p style='font-size: 16px;'>您好！</p>" +
                "<p style='font-size: 16px;'><strong>" + userName + "</strong> 在您的文章《<strong>" + articleTitle + "</strong>》中发表了评论：</p>" +
                "<div style='background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;'>" +
                "<p style='font-style: italic; color: #495057;'>\"" + commentContent + "\"</p>" +
                "</div>" +
                "<div style='text-align: center; margin: 30px 0;'>" +
                "<a href='" + articleUrl + "' style='background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>查看评论</a>" +
                "</div>" +
                "<p style='color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;'>" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    /**
     * 获取点赞通知邮件模板
     */
    public String getLikeNotificationTemplate(String userName, String articleTitle, String articleUrl) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>点赞通知</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<div style='background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>" +
                "<h1 style='color: white; margin: 0; font-size: 24px;'>👍 您的文章获得了点赞</h1>" +
                "</div>" +
                "<div style='background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>" +
                "<p style='font-size: 16px;'>您好！</p>" +
                "<p style='font-size: 16px;'><strong>" + userName + "</strong> 点赞了您的文章《<strong>" + articleTitle + "</strong>》</p>" +
                "<div style='text-align: center; margin: 30px 0;'>" +
                "<a href='" + articleUrl + "' style='background-color: #e91e63; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>查看文章</a>" +
                "</div>" +
                "<p style='color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;'>" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    /**
     * 获取关注通知邮件模板
     */
    public String getFollowNotificationTemplate(String userName, String userProfileUrl) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>新关注者通知</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<div style='background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>" +
                "<h1 style='color: #333; margin: 0; font-size: 24px;'>🎉 您有新的关注者</h1>" +
                "</div>" +
                "<div style='background-color: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>" +
                "<p style='font-size: 16px;'>您好！</p>" +
                "<p style='font-size: 16px;'><strong>" + userName + "</strong> 开始关注您了！</p>" +
                "<p style='font-size: 16px;'>快去看看TA的动态吧！</p>" +
                "<div style='text-align: center; margin: 30px 0;'>" +
                "<a href='" + userProfileUrl + "' style='background-color: #17a2b8; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>查看TA的主页</a>" +
                "</div>" +
                "<p style='color: #6c757d; font-size: 14px; text-align: center; margin-top: 30px;'>" +
                "此邮件由个人动态博客系统自动发送，请勿回复。" +
                "</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }
}
