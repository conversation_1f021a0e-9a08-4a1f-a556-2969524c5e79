# 个人动态博客系统 - 第一阶段测试指南

本文档提供了测试个人动态博客系统基础功能的指南，现在完全基于自动化测试流程。

## 1. 环境准备

### 1.1 数据库准备

1. 确保MySQL数据库已启动
   - 用户名：root
   - 密码：12345

2. 确保已创建`blog_system`数据库并已执行最新的初始化脚本。

### 1.2 后端环境准备

1. 确保已安装JDK 1.8
   ```bash
   java -version
   ```

2. 确保已安装Maven
   ```bash
   mvn -v
   ```

### 1.3 前端环境准备

1. 确保已安装Node.js
   ```bash
   node -v
   npm -v
   ```

## 2. 后端自动化测试

用户认证模块的核心功能（包括注册、登录、访问控制等）已由自动化测试覆盖。相关测试代码位于 `src/test/java/com/blog/` 下的 `service/UserServiceTest.java` 和 `controller/TestControllerTest.java`。

这些测试用例验证了以下场景：
- 新用户成功注册
- 重复用户名的用户无法注册
- 用户使用正确/错误的密码登录
- 未经认证的用户访问受保护接口会被拒绝 (返回401)
- 匿名接口可以被成功访问

### 2.1 运行后端测试

进入后端项目根目录，执行以下命令即可一键运行所有后端测试：

```bash
mvn -f blog-server/pom.xml test
```

### 2.2 验证测试结果
        
观察Maven的输出。如果所有测试都通过，您将在日志末尾看到 `[INFO] BUILD SUCCESS`。
        
任何失败的测试都会被明确标记为 `[ERROR]`，并指出失败的原因，方便快速定位问题。

## 3. 前端端到端(E2E)测试

前端的关键业务流程将通过Cypress自动化测试来保障。这能模拟真实用户在浏览器中的操作，确保前后端系统协同工作顺畅。

### 3.1 首次运行与配置

如果您是第一次在项目中运行Cypress，请先进行初始化：

1.  **启动Cypress**
    ```bash
    # 进入前端项目目录
    cd blog-web
    # 启动Cypress可视化测试运行器
    npx cypress open
    ```
2.  **配置向导**
    - 在打开的Cypress窗口中，选择 **"E2E Testing"**。
    - Cypress会自动创建所需配置文件（如`cypress.config.js`），点击 **"Continue"** 确认即可。
    - 选择一个您希望用于测试的浏览器（如Chrome）。

### 3.2 编写测试脚本

Cypress的测试脚本（Specs）存放在 `blog-web/cypress/e2e/` 目录下。

**命名规范**: 测试文件通常以 `.cy.js` 结尾，例如 `login.cy.js`。

**测试示例 (`home.cy.js`)**:
```javascript
describe('首页冒烟测试', () => {
  it('应能成功加载首页并显示标题', () => {
    // 访问在 cypress.config.js 中配置的 baseUrl
    cy.visit('/');
    // 验证页面上是否包含特定文本
    cy.contains('个人动态博客系统').should('be.visible');
  });
});
```

### 3.3 运行测试

1.  确保您的前端开发服务器正在运行 (`npm run dev`)。
2.  打开Cypress测试运行器 (`npx cypress open`)。
3.  在测试列表（Specs）中，点击您想要运行的测试文件，例如 `home.cy.js`。
4.  Cypress将自动打开浏览器并执行测试脚本，您可以实时看到执行过程和结果。

### 3.4 计划中的测试场景 (用户认证)

- **注册流程** (`register.cy.js`): 访问注册页，填写表单，提交后验证跳转和提示。
- **登录流程** (`login.cy.js`): 访问登录页，输入正确/错误凭据，验证登录成功/失败的反馈。
- **登出流程**: 登录后，点击登出按钮，验证是否成功返回登录页。

## 4. 常见问题排查

1. **数据库连接失败**: 检查`application.yml`中的数据库配置是否正确。
2. **端口占用问题**: 如果8080端口被占用，可以在`application.yml`中修改`server.port`配置。
3. **测试失败**: 仔细阅读`[ERROR]`部分的日志，定位失败的断言或异常。