package com.blog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 文章与标签的关联实体类，用于维护多对多关系
 */
@Data
@TableName("article_tag")
public class ArticleTag implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 文章ID
     */
    private Long articleId;
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 空参构造器
     */
    public ArticleTag() {}
    
    /**
     * 全参构造器
     */
    public ArticleTag(Long articleId, Long tagId) {
        this.articleId = articleId;
        this.tagId = tagId;
    }
} 