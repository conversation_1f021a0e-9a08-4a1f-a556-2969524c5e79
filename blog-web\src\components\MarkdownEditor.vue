<template>
  <div class="markdown-editor">
    <div ref="editorElement"></div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import '@toast-ui/editor/dist/toastui-editor.css'
import Editor from '@toast-ui/editor'

export default {
  name: 'MarkdownEditor',
  props: {
    // 编辑器内容
    modelValue: {
      type: String,
      default: ''
    },
    // 编辑器高度
    height: {
      type: String,
      default: '400px'
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // 编辑器DOM元素引用
    const editorElement = ref(null)
    // 编辑器实例
    let editor = null

    // 初始化编辑器
    const initEditor = () => {
      // 创建编辑器实例
      editor = new Editor({
        el: editorElement.value,
        height: props.height,
        initialEditType: 'markdown',
        previewStyle: 'vertical',
        initialValue: props.modelValue,
        toolbarItems: [
          ['heading', 'bold', 'italic', 'strike'],
          ['hr', 'quote'],
          ['ul', 'ol', 'task', 'indent', 'outdent'],
          ['table', 'image', 'link'],
          ['code', 'codeblock'],
          ['scrollSync']
        ]
      })

      // 监听编辑器内容变化
      editor.on('change', () => {
        const content = editor.getMarkdown()
        emit('update:modelValue', content)
        emit('change', content)
      })
    }

    // 组件挂载后初始化编辑器
    onMounted(() => {
      initEditor()
    })

    // 组件卸载前销毁编辑器
    onBeforeUnmount(() => {
      if (editor) {
        editor.destroy()
        editor = null
      }
    })

    // 监听modelValue变化，更新编辑器内容
    watch(() => props.modelValue, (newVal) => {
      if (editor && newVal !== editor.getMarkdown()) {
        editor.setMarkdown(newVal)
      }
    })

    // 监听readonly变化，更新编辑器状态
    watch(() => props.readonly, (newVal) => {
      if (editor) {
        if (newVal) {
          editor.disable()
        } else {
          editor.enable()
        }
      }
    }, { immediate: true })

    return {
      editorElement
    }
  }
}
</script>

<style scoped>
.markdown-editor {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style> 