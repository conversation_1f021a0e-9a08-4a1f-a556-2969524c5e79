# API 接口文档

## 用户模块 (user.js)

### 获取当前登录用户信息
```js
getUserInfo()
```
- **描述**：获取当前登录用户的详细信息
- **请求方式**：GET
- **URL**：`/user/info`
- **参数**：无
- **返回示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "avatar": "http://example.com/avatar.jpg",
    "role": "admin",
    "createTime": "2023-01-01 00:00:00"
  }
}
```

### 更新用户信息
```js
updateUserInfo(data)
```
- **描述**：更新当前登录用户的个人资料
- **请求方式**：PUT
- **URL**：`/user/info`
- **参数**：
  - `nickname`：用户昵称（选填）
  - `email`：用户邮箱（选填）
- **返回示例**：
```json
{
  "code": 200,
  "msg": "用户信息更新成功",
  "data": null
}
```

### 修改密码
```js
updatePassword(data)
```
- **描述**：修改当前登录用户的密码
- **请求方式**：PUT
- **URL**：`/user/password`
- **参数**：
  - `currentPassword`：当前密码（必填）
  - `newPassword`：新密码（必填）
  - `confirmPassword`：确认新密码（必填）
- **返回示例**：
```json
{
  "code": 200,
  "msg": "密码修改成功，请重新登录",
  "data": null
}
```
- **注意**：密码修改成功后，用户需要重新登录

### 更新用户头像
```js
updateAvatar(data)
```
- **描述**：更新当前登录用户的头像
- **请求方式**：PUT
- **URL**：`/user/avatar`
- **参数**：
  - `avatar`：头像URL地址（必填）
- **返回示例**：
```json
{
  "code": 200,
  "msg": "头像更新成功",
  "data": null
}
```
- **注意**：头像上传需要先调用上传接口，获取到URL后再调用此接口

### 获取用户收藏列表
```js
getUserCollections()
```
- **描述**：获取当前登录用户的收藏文章列表
- **请求方式**：GET
- **URL**：`/user/collections`
- **参数**：无
- **返回示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "title": "文章标题",
      "summary": "文章摘要",
      "createTime": "2023-01-01 00:00:00"
    }
  ]
}
```

### 获取用户评论列表
```js
getUserComments()
```
- **描述**：获取当前登录用户发表的评论列表
- **请求方式**：GET
- **URL**：`/user/comments`
- **参数**：无
- **返回示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "content": "评论内容",
      "articleId": 1,
      "articleTitle": "文章标题",
      "createTime": "2023-01-01 00:00:00"
    }
  ]
}
``` 